"""
Orchestrator Agent - Main Coordination Agent
Coordinates all other agents in the multi-agent financial advisory system
"""

import asyncio
import logging
from typing import Dict, Any, List, Optional
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)

class TaskPriority(Enum):
    """Task priority levels"""
    LOW = 1
    MEDIUM = 2
    HIGH = 3
    CRITICAL = 4

@dataclass
class AgentTask:
    """Task for agent execution"""
    task_id: str
    agent_name: str
    task_type: str
    data: Dict[str, Any]
    priority: TaskPriority
    dependencies: List[str] = None
    timeout: int = 30

@dataclass
class AgentTaskResult:
    """Result of a task executed by an agent"""
    task_id: str
    agent_name: str
    success: bool
    data: Dict[str, Any]
    error: Optional[str] = None
    execution_time: float = 0.0

class OrchestratorAgent:
    """Main orchestrator agent that coordinates all other agents"""
    
    def __init__(self):
        self.agents = {}
        self.task_queue = asyncio.Queue()
        self.active_tasks = {}
        self.completed_tasks = {}
        self.agent_status = {}
        self.is_running = False
        
    async def initialize(self):
        """Initialize the orchestrator agent"""
        try:
            logger.info("Initializing Orchestrator Agent")
            self.is_running = True
            
            # Start task processor
            asyncio.create_task(self._process_tasks())
            
            logger.info("✅ Orchestrator Agent initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize Orchestrator Agent: {e}")
            raise
    
    def register_agent(self, name: str, agent: Any):
        """Register an agent with the orchestrator"""
        self.agents[name] = agent
        self.agent_status[name] = 'active'
        logger.info(f"Registered agent: {name}")
    
    async def process_user_query(self, user_id: str, query: str) -> Dict[str, Any]:
        """Process a user query through the multi-agent system"""
        try:
            logger.info(f"Processing query for user {user_id}: {query[:50]}...")
            
            # Step 1: Input validation and safety check
            guardrails_task = AgentTask(
                task_id="guardrails_input",
                agent_name="guardrails",
                task_type="validate_input",
                data={"user_id": user_id, "query": query},
                priority=TaskPriority.CRITICAL
            )
            
            guardrails_result = await self._execute_task(guardrails_task)
            
            # Check if guardrails failed completely or blocked the query
            if not guardrails_result.success:
                logger.warning("Guardrails check failed, proceeding with caution")
            elif not guardrails_result.data.get("is_safe", True):
                return {
                    "response": "I can't help with that request for safety reasons. Please ask about legitimate financial topics.",
                    "confidence": 0.0,
                    "agent_responses": {"guardrails": guardrails_result.data}
                }
            elif not guardrails_result.data.get("is_relevant", True):
                # For relevance issues, be more helpful
                return {
                    "response": f"Hi! I'm PennyWise, your financial advisor. I can help you with budgeting, investing, retirement planning, debt management, and other financial topics. What would you like to know about your finances?",
                    "confidence": 0.8,
                    "agent_responses": {"guardrails": guardrails_result.data}
                }
            
            # Continue with other agents...
            # Step 2: Get user context
            context_result = await self._get_user_context(user_id, query)
            
            # Step 3: Generate response using available agents
            response_data = await self._generate_response(user_id, query, context_result)
            
            return response_data
            
        except Exception as e:
            logger.error(f"Failed to process query: {e}")
            return {
                "response": "I apologize, but I encountered an error. Please try asking about your financial goals, budgeting, or investment questions.",
                "confidence": 0.1,
                "error": str(e)
            }

    async def _get_user_context(self, user_id: str, query: str) -> Dict[str, Any]:
        """Get user context from memory agent"""
        try:
            if 'memory' not in self.agents:
                logger.warning("Memory agent not available, using empty context")
                return {}

            # Create memory task to get context
            memory_task = AgentTask(
                task_id="memory_context",
                agent_name="memory",
                task_type="get_context",
                data={"user_id": user_id, "query": query},
                priority=TaskPriority.HIGH
            )

            memory_result = await self._execute_task(memory_task)

            if memory_result.success:
                return memory_result.data
            else:
                logger.warning(f"Failed to get memory context: {memory_result.data}")
                return {}

        except Exception as e:
            logger.error(f"Error getting user context: {e}")
            return {}

    async def _generate_response(self, user_id: str, query: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Generate response using available agents"""
        try:
            # Simple response generation for now
            query_lower = query.lower()
            
            agent_responses = {}

            # Check if any MCP servers can handle this query
            mcp_response = await self._check_mcp_servers(query, context)
            if mcp_response:
                agent_responses["mcp_tools"] = mcp_response

            # Route to appropriate specialized agent
            primary_agent = None
            if any(word in query_lower for word in ['invest', 'stock', 'portfolio', 'retirement', 'financial']):
                primary_agent = "financial"
            elif any(word in query_lower for word in ['remember', 'profile', 'age', 'income']):
                primary_agent = "memory"
            elif any(word in query_lower for word in ['plan', 'strategy', 'goal']):
                primary_agent = "planning"

            # Execute primary agent if available
            if primary_agent and primary_agent in self.agents:
                try:
                    agent_result = await self.agents[primary_agent].process_query(user_id, query, context)
                    if agent_result.get("success"):
                        agent_responses[primary_agent] = agent_result
                except Exception as e:
                    logger.error(f"Error executing {primary_agent} agent: {e}")

            # Generate final response using response agent or fallback
            if 'response' in self.agents:
                try:
                    response_result = await self.agents['response'].generate_response(
                        user_id, query, context, agent_responses
                    )
                    if response_result.get("success"):
                        return {
                            "response": response_result.get("response", "I'm here to help with your financial questions."),
                            "confidence": response_result.get("confidence", 0.7),
                            "agent_responses": agent_responses
                        }
                except Exception as e:
                    logger.error(f"Error with response agent: {e}")

            # Use MCP tool response if available
            if mcp_response and mcp_response.get("success"):
                return {
                    "response": f"Using {mcp_response['tool_used']}: {mcp_response['response']}",
                    "confidence": 0.8,
                    "agent_responses": agent_responses
                }

            # Use primary agent response if available
            if agent_responses and primary_agent in agent_responses:
                primary_response = agent_responses[primary_agent]
                return {
                    "response": primary_response.get("analysis", "I can help with your financial planning needs."),
                    "confidence": primary_response.get("confidence", 0.6),
                    "agent_responses": agent_responses
                }

            # Final fallback
            return {
                "response": "I'm here to help with your financial questions. Please ask about investments, retirement planning, or budgeting.",
                "confidence": 0.5,
                "agent_responses": {}
            }
            
        except Exception as e:
            logger.error(f"Failed to generate response: {e}")
            return {
                "response": "I can help you with budgeting, investing, retirement planning, and other financial topics. What would you like to know?",
                "confidence": 0.5,
                "error": str(e)
            }

    async def _check_mcp_servers(self, query: str, context: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Check if any MCP servers can handle this query"""
        try:
            from mcp.dynamic_server_manager import dynamic_mcp_manager

            available_tools = dynamic_mcp_manager.get_available_tools()

            if not available_tools:
                return None

            # Simple keyword matching to find relevant tools
            query_lower = query.lower()
            for tool in available_tools:
                tool_desc_lower = tool["description"].lower()
                tool_name_lower = tool["name"].lower()

                # Check if query matches tool description or name
                if (any(word in tool_desc_lower for word in query_lower.split()) or
                    any(word in tool_name_lower for word in query_lower.split())):

                    # Execute the tool
                    result = await dynamic_mcp_manager.execute_server_tool(
                        tool["server_name"], query, context
                    )

                    if result.get("success"):
                        return {
                            "tool_used": tool["name"],
                            "server_name": tool["server_name"],
                            "response": result.get("response", ""),
                            "success": True
                        }

            return None

        except Exception as e:
            logger.error(f"Error checking MCP servers: {e}")
            return None

    def _determine_required_agents(self, query: str, reasoning_data: Dict[str, Any]) -> List[str]:
        """Determine which agents are needed based on the query"""
        required = []
        
        query_lower = query.lower()
        
        # Financial data needed
        if any(word in query_lower for word in ['stock', 'price', 'market', 'invest', 'portfolio', 'return']):
            required.append('financial')
        
        # Market analysis needed
        if any(word in query_lower for word in ['market', 'trend', 'sentiment', 'sector', 'economy']):
            required.append('market')
        
        # Geographic data needed
        if any(word in query_lower for word in ['country', 'region', 'international', 'global', 'local']):
            required.append('geography')
        
        return required
    
    def _needs_planning(self, query: str, reasoning_data: Dict[str, Any]) -> bool:
        """Determine if financial planning is needed"""
        planning_keywords = ['plan', 'strategy', 'goal', 'retirement', 'save', 'budget', 'comprehensive']
        return any(word in query.lower() for word in planning_keywords)
    
    async def _execute_task(self, task: AgentTask) -> AgentTaskResult:
        """Execute a task with an agent"""
        try:
            if task.agent_name not in self.agents:
                logger.warning(f"Agent '{task.agent_name}' not available, using fallback")
                return AgentTaskResult(
                    task_id=task.task_id,
                    agent_name=task.agent_name,
                    success=True,
                    data={"fallback": True, "message": f"{task.agent_name} agent not available"}
                )
            
            agent = self.agents[task.agent_name]
            
            # Execute based on task type
            if task.task_type == "validate_input":
                result = await agent.validate_input(task.data.get("query", ""))
            elif task.task_type == "get_context":
                result = await agent.get_context(task.data.get("user_id"), task.data.get("query"))
            elif task.task_type == "analyze_query":
                result = await agent.analyze_query(task.data.get("query"), task.data.get("context", {}))
            elif task.task_type == "create_plan":
                result = await agent.create_plan(task.data.get("query"), task.data.get("context", {}))
            elif task.task_type == "synthesize_response":
                result = await agent.synthesize_response(**task.data)
            else:
                result = {"success": False, "error": f"Unknown task type: {task.task_type}"}
            
            return AgentTaskResult(
                task_id=task.task_id,
                agent_name=task.agent_name,
                success=result.get("success", True),
                data=result
            )
            
        except Exception as e:
            logger.error(f"Task execution failed for {task.agent_name}: {e}")
            return AgentTaskResult(
                task_id=task.task_id,
                agent_name=task.agent_name,
                success=False,
                data={"error": str(e)}
            )
    
    async def _execute_tasks_parallel(self, tasks: List[AgentTask]) -> List[AgentTaskResult]:
        """Execute multiple tasks in parallel"""
        if not tasks:
            return []
        
        # Create coroutines for all tasks
        coroutines = [self._execute_task(task) for task in tasks]
        
        # Execute all tasks concurrently
        results = await asyncio.gather(*coroutines, return_exceptions=True)
        
        # Handle any exceptions
        responses = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                responses.append(AgentTaskResult(
                    task_id=tasks[i].task_id,
                    agent_name=tasks[i].agent_name,
                    success=False,
                    data={},
                    error=str(result)
                ))
            else:
                responses.append(result)
        
        return responses
    
    async def _process_tasks(self):
        """Background task processor"""
        while self.is_running:
            try:
                # Process any queued tasks
                await asyncio.sleep(0.1)
            except Exception as e:
                logger.error(f"Task processor error: {e}")
    
    def get_agent_status(self) -> Dict[str, str]:
        """Get status of all registered agents"""
        return self.agent_status.copy()
    
    async def shutdown(self):
        """Shutdown the orchestrator"""
        self.is_running = False
        logger.info("Orchestrator Agent shutdown")


