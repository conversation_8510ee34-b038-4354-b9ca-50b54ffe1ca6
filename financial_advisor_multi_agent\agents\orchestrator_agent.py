"""
Orchestrator Agent - Main Coordination Agent
Coordinates all other agents in the multi-agent financial advisory system
"""

import asyncio
import logging
from typing import Dict, Any, List, Optional
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)

class TaskPriority(Enum):
    """Task priority levels"""
    LOW = 1
    MEDIUM = 2
    HIGH = 3
    CRITICAL = 4

@dataclass
class AgentTask:
    """Task for agent execution"""
    task_id: str
    agent_name: str
    task_type: str
    data: Dict[str, Any]
    priority: TaskPriority
    dependencies: List[str] = None
    timeout: int = 30

@dataclass
class AgentTaskResult:
    """Result of a task executed by an agent"""
    task_id: str
    agent_name: str
    success: bool
    data: Dict[str, Any]
    error: Optional[str] = None
    execution_time: float = 0.0

class OrchestratorAgent:
    """Main orchestrator agent that coordinates all other agents"""
    
    def __init__(self):
        self.agents = {}
        self.task_queue = asyncio.Queue()
        self.active_tasks = {}
        self.completed_tasks = {}
        self.agent_status = {}
        self.is_running = False
        self.llm = None
        self.langgraph_orchestrator = None
        
    async def initialize(self):
        """Initialize the orchestrator agent"""
        try:
            logger.info("Initializing Orchestrator Agent")

            # Initialize LLM for orchestrator
            await self._initialize_llm()

            # Try to initialize LangGraph orchestrator
            await self._initialize_langgraph()

            self.is_running = True

            # Start task processor
            asyncio.create_task(self._process_tasks())

            logger.info("✅ Orchestrator Agent initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize Orchestrator Agent: {e}")
            raise

    async def _initialize_llm(self):
        """Initialize LLM for orchestrator"""
        try:
            from langchain_groq import ChatGroq
            import os

            api_key = os.getenv("GROQ_API_KEY")
            if api_key:
                self.llm = ChatGroq(
                    groq_api_key=api_key,
                    model_name="llama3-70b-8192",
                    temperature=0.1,
                    max_tokens=1000
                )
                logger.info("Orchestrator LLM initialized")
            else:
                logger.warning("No API key found for orchestrator LLM")

        except Exception as e:
            logger.error(f"Failed to initialize orchestrator LLM: {e}")
            self.llm = None

    async def _initialize_langgraph(self):
        """Initialize LangGraph orchestrator if available"""
        try:
            from .langgraph_orchestrator import LangGraphOrchestrator

            self.langgraph_orchestrator = LangGraphOrchestrator()
            success = await self.langgraph_orchestrator.initialize()

            if success:
                logger.info("✅ LangGraph orchestrator initialized")
            else:
                logger.info("Using basic orchestrator (LangGraph not available)")
                self.langgraph_orchestrator = None

        except Exception as e:
            logger.warning(f"LangGraph initialization failed: {e}")
            self.langgraph_orchestrator = None

    def register_agent(self, name: str, agent: Any):
        """Register an agent with the orchestrator"""
        self.agents[name] = agent
        self.agent_status[name] = 'active'

        # Also register with LangGraph orchestrator if available
        if self.langgraph_orchestrator:
            self.langgraph_orchestrator.register_agent(name, agent)

        logger.info(f"Registered agent: {name}")
    
    async def process_user_query(self, user_id: str, query: str) -> Dict[str, Any]:
        """Process a user query through the multi-agent system"""
        try:
            logger.info(f"Processing query for user {user_id}: {query[:50]}...")

            # Get user context first
            context = await self._get_user_context(user_id, query)

            # Try LangGraph orchestrator first if available
            if self.langgraph_orchestrator:
                try:
                    return await self.langgraph_orchestrator.process_query(user_id, query, context)
                except Exception as e:
                    logger.error(f"LangGraph orchestrator error: {e}")
                    # Fall through to basic orchestration

            # Fallback to basic orchestration

            # For now, let's use a simple direct response to ensure it works
            try:
                # First try a simple template-based response
                simple_response = self._get_simple_response(query, context)
                if simple_response:
                    return {
                        "response": simple_response,
                        "confidence": 0.7,
                        "agent_responses": {},
                        "processing_method": "simple_template"
                    }

                # Then try LLM if available
                if self.llm:
                    direct_response = await self._generate_orchestrator_response(query, context, {})
                    return {
                        "response": direct_response,
                        "confidence": 0.8,
                        "agent_responses": {},
                        "processing_method": "direct_orchestrator"
                    }
            except Exception as e:
                logger.error(f"Error in direct response generation: {e}")

            # Step 1: Input validation and safety check
            guardrails_result = {"is_safe": True, "warnings": []}
            if 'guardrails' in self.agents:
                try:
                    guardrails_result = await self.agents['guardrails'].validate_query(user_id, query, context)

                    # Block query if unsafe
                    if guardrails_result.get("block_query", False):
                        return {
                            "response": guardrails_result.get("safety_message", "This query cannot be processed for safety reasons."),
                            "confidence": 1.0,
                            "blocked": True,
                            "safety_flags": guardrails_result.get("flags", [])
                        }
                except Exception as e:
                    logger.error(f"Error in guardrails validation: {e}")
                    # Continue with safe defaults if guardrails fail
            
            # Check if guardrails failed completely or blocked the query
            if not guardrails_result.success:
                logger.warning("Guardrails check failed, proceeding with caution")
            elif not guardrails_result.data.get("is_safe", True):
                return {
                    "response": "I can't help with that request for safety reasons. Please ask about legitimate financial topics.",
                    "confidence": 0.0,
                    "agent_responses": {"guardrails": guardrails_result.data}
                }
            elif not guardrails_result.data.get("is_relevant", True):
                # For relevance issues, be more helpful
                return {
                    "response": f"Hi! I'm PennyWise, your financial advisor. I can help you with budgeting, investing, retirement planning, debt management, and other financial topics. What would you like to know about your finances?",
                    "confidence": 0.8,
                    "agent_responses": {"guardrails": guardrails_result.data}
                }
            
            # Continue with other agents...
            # Step 2: Get user context
            context_result = await self._get_user_context(user_id, query)
            
            # Step 3: Generate response using available agents
            response_data = await self._generate_response(user_id, query, context_result)
            
            return response_data
            
        except Exception as e:
            logger.error(f"Failed to process query: {e}")
            return {
                "response": "I apologize, but I encountered an error. Please try asking about your financial goals, budgeting, or investment questions.",
                "confidence": 0.1,
                "error": str(e)
            }

    async def _get_user_context(self, user_id: str, query: str) -> Dict[str, Any]:
        """Get user context from memory agent"""
        try:
            if 'memory' not in self.agents:
                logger.warning("Memory agent not available, using empty context")
                return {}

            # Create memory task to get context
            memory_task = AgentTask(
                task_id="memory_context",
                agent_name="memory",
                task_type="get_context",
                data={"user_id": user_id, "query": query},
                priority=TaskPriority.HIGH
            )

            memory_result = await self._execute_task(memory_task)

            if memory_result.success:
                return memory_result.data
            else:
                logger.warning(f"Failed to get memory context: {memory_result.data}")
                return {}

        except Exception as e:
            logger.error(f"Error getting user context: {e}")
            return {}

    async def _generate_response(self, user_id: str, query: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Generate response using available agents"""
        try:
            # Simple response generation for now
            query_lower = query.lower()
            
            agent_responses = {}

            # Check if any MCP servers can handle this query
            mcp_response = await self._check_mcp_servers(query, context)
            if mcp_response:
                agent_responses["mcp_tools"] = mcp_response

            # Route to appropriate specialized agent
            primary_agent = None
            if any(word in query_lower for word in ['invest', 'stock', 'portfolio', 'retirement', 'financial']):
                primary_agent = "financial"
            elif any(word in query_lower for word in ['remember', 'profile', 'age', 'income']):
                primary_agent = "memory"
            elif any(word in query_lower for word in ['plan', 'strategy', 'goal']):
                primary_agent = "planning"

            # Execute primary agent if available
            if primary_agent and primary_agent in self.agents:
                try:
                    agent_result = await self.agents[primary_agent].process_query(user_id, query, context)
                    if agent_result.get("success"):
                        agent_responses[primary_agent] = agent_result
                except Exception as e:
                    logger.error(f"Error executing {primary_agent} agent: {e}")

            # Generate final response using response agent or fallback
            if 'response' in self.agents:
                try:
                    response_result = await self.agents['response'].generate_response(
                        user_id, query, context, agent_responses
                    )
                    if response_result.get("success"):
                        return {
                            "response": response_result.get("response", "I'm here to help with your financial questions."),
                            "confidence": response_result.get("confidence", 0.7),
                            "agent_responses": agent_responses
                        }
                except Exception as e:
                    logger.error(f"Error with response agent: {e}")

            # Use MCP tool response if available
            if mcp_response and mcp_response.get("success"):
                return {
                    "response": f"Using {mcp_response['tool_used']}: {mcp_response['response']}",
                    "confidence": 0.8,
                    "agent_responses": agent_responses
                }

            # Use primary agent response if available
            if agent_responses and primary_agent in agent_responses:
                primary_response = agent_responses[primary_agent]
                return {
                    "response": primary_response.get("analysis", "I can help with your financial planning needs."),
                    "confidence": primary_response.get("confidence", 0.6),
                    "agent_responses": agent_responses
                }

            # Final fallback - use orchestrator LLM
            if self.llm:
                fallback_response = await self._generate_orchestrator_response(query, context, agent_responses)
                return {
                    "response": fallback_response,
                    "confidence": 0.7,
                    "agent_responses": agent_responses
                }
            else:
                return {
                    "response": "I'm here to help with your financial questions. Please ask about investments, retirement planning, or budgeting.",
                    "confidence": 0.5,
                    "agent_responses": {}
                }
            
        except Exception as e:
            logger.error(f"Failed to generate response: {e}")
            return {
                "response": "I can help you with budgeting, investing, retirement planning, and other financial topics. What would you like to know?",
                "confidence": 0.5,
                "error": str(e)
            }

    async def _check_mcp_servers(self, query: str, context: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Check if any MCP servers can handle this query"""
        try:
            from mcp.dynamic_server_manager import dynamic_mcp_manager

            available_tools = dynamic_mcp_manager.get_available_tools()

            if not available_tools:
                return None

            # Simple keyword matching to find relevant tools
            query_lower = query.lower()
            for tool in available_tools:
                tool_desc_lower = tool["description"].lower()
                tool_name_lower = tool["name"].lower()

                # Check if query matches tool description or name
                if (any(word in tool_desc_lower for word in query_lower.split()) or
                    any(word in tool_name_lower for word in query_lower.split())):

                    # Execute the tool
                    result = await dynamic_mcp_manager.execute_server_tool(
                        tool["server_name"], query, context
                    )

                    if result.get("success"):
                        return {
                            "tool_used": tool["name"],
                            "server_name": tool["server_name"],
                            "response": result.get("response", ""),
                            "success": True
                        }

            return None

        except Exception as e:
            logger.error(f"Error checking MCP servers: {e}")
            return None

    async def _generate_orchestrator_response(self, query: str, context: Dict[str, Any], agent_responses: Dict[str, Any]) -> str:
        """Generate response using orchestrator's LLM"""
        try:
            user_profile = context.get('user_profile', {})

            system_prompt = f"""You are PennyWise, an expert AI financial advisor with 20+ years of experience.
You help people with financial planning, investments, budgeting, and achieving their financial goals.

User Profile:
- Age: {user_profile.get('age', 'Not specified')}
- Income: ${user_profile.get('income', 'Not specified'):,}
- Risk Tolerance: {user_profile.get('risk_tolerance', 'Not specified')}
- Goals: {user_profile.get('goals', [])}

Available agent responses: {agent_responses}

Provide helpful, personalized financial advice. Be conversational, friendly, and specific.
Keep responses concise but informative (2-3 sentences max).
Always consider the user's profile when giving advice."""

            user_prompt = f"User question: {query}\n\nPlease provide helpful financial advice based on this question and the user's profile."

            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ]

            response = await self.llm.ainvoke(messages)
            return response.content

        except Exception as e:
            logger.error(f"Error generating orchestrator response: {e}")
            return "I'm here to help with your financial questions. Could you tell me more about what you'd like to know?"

    async def _get_user_context(self, user_id: str, query: str) -> Dict[str, Any]:
        """Get user context for processing"""
        context = {
            "user_id": user_id,
            "query": query,
            "user_profile": {},
            "conversation_history": []
        }

        # Get user profile from memory agent if available
        if 'memory' in self.agents:
            try:
                profile_result = await self.agents['memory'].get_user_profile(user_id)
                if profile_result.get("success"):
                    context["user_profile"] = profile_result.get("profile", {})
                else:
                    # Set default profile if memory agent fails
                    context["user_profile"] = {
                        "age": 30,
                        "income": 50000,
                        "risk_tolerance": "moderate",
                        "goals": []
                    }
            except Exception as e:
                logger.error(f"Error getting user profile: {e}")
                # Set default profile on error
                context["user_profile"] = {
                    "age": 30,
                    "income": 50000,
                    "risk_tolerance": "moderate",
                    "goals": []
                }
        else:
            # Set default profile if no memory agent
            context["user_profile"] = {
                "age": 30,
                "income": 50000,
                "risk_tolerance": "moderate",
                "goals": []
            }

        return context

    def _get_simple_response(self, query: str, context: Dict[str, Any]) -> str:
        """Generate a simple template-based response"""
        try:
            query_lower = query.lower()
            user_profile = context.get('user_profile', {})
            age = user_profile.get('age', 30)
            income = user_profile.get('income', 50000)

            # House buying advice
            if any(word in query_lower for word in ['house', 'home', 'buy', 'purchase', 'property']):
                return f"""🏠 **Buying Your First House - A Step-by-Step Guide**

Based on your profile (Age: {age}, Income: ${income:,}), here's my advice:

**1. Financial Preparation (6-12 months)**
- Save for down payment (10-20% of home price)
- Build emergency fund (3-6 months expenses)
- Check and improve your credit score (aim for 700+)

**2. Budget Planning**
- Total housing costs should be ≤28% of gross income
- For your income, target monthly payment: ${int(income * 0.28 / 12):,}
- Don't forget: property taxes, insurance, maintenance

**3. Getting Pre-Approved**
- Shop around with 3-5 lenders
- Get pre-approval letter (not just pre-qualification)
- Lock in interest rates when ready

**4. House Hunting**
- Work with experienced real estate agent
- Consider location, schools, commute, future growth
- Don't fall in love with first house you see

**5. Making an Offer**
- Research comparable sales
- Include inspection contingency
- Be prepared to negotiate

**Key Tip**: Don't rush! Take time to find the right home within your budget. Your first home doesn't have to be your forever home.

Would you like me to elaborate on any of these steps?"""

            # Investment advice
            elif any(word in query_lower for word in ['invest', 'investment', 'portfolio', 'stocks', 'bonds']):
                return f"""📈 **Investment Strategy for Your Profile**

Age: {age} | Income: ${income:,} | Risk Level: Moderate

**Investment Allocation Recommendation:**
- **60% Stocks** (Growth potential)
  - 40% US Total Market Index
  - 20% International Index
- **30% Bonds** (Stability)
  - US Treasury/Corporate bonds
- **10% Alternatives** (REITs, commodities)

**Getting Started:**
1. **Emergency Fund First** - 3-6 months expenses in high-yield savings
2. **401(k) Match** - Always get full employer match (free money!)
3. **Roth IRA** - $6,500/year tax-free growth
4. **Taxable Account** - For additional investments

**Investment Platforms:**
- Low-cost index funds: Vanguard, Fidelity, Schwab
- Robo-advisors: Betterment, Wealthfront
- Target-date funds: Set-and-forget option

**Key Principles:**
- Start early (time is your best friend)
- Diversify across asset classes
- Keep costs low (<0.5% expense ratios)
- Don't try to time the market
- Rebalance annually

Would you like specific fund recommendations or help with account setup?"""

            # Retirement planning
            elif any(word in query_lower for word in ['retirement', 'retire', '401k', 'ira', 'pension']):
                retirement_age = 65
                years_to_retirement = max(retirement_age - age, 0)
                monthly_savings_needed = int(income * 0.15 / 12)

                return f"""🏖️ **Retirement Planning Strategy**

**Your Retirement Timeline:**
- Current Age: {age}
- Target Retirement: {retirement_age}
- Years to Save: {years_to_retirement}

**Savings Target:**
- Aim to save 15% of income: ${monthly_savings_needed:,}/month
- Target retirement fund: 10-12x final salary

**Retirement Account Priority:**
1. **401(k) with Match** - Contribute enough for full match
2. **Roth IRA** - $6,500/year (tax-free in retirement)
3. **Additional 401(k)** - Up to $22,500/year limit
4. **Taxable Accounts** - For early retirement goals

**Investment Strategy by Age:**
- **20s-30s**: 80-90% stocks, 10-20% bonds
- **40s**: 70-80% stocks, 20-30% bonds
- **50s**: 60-70% stocks, 30-40% bonds
- **60s+**: 50-60% stocks, 40-50% bonds

**Catch-Up Strategies:**
- Increase savings rate by 1% annually
- Invest windfalls (bonuses, tax refunds)
- Consider Roth conversions in low-income years

**Healthcare Planning:**
- HSA if available (triple tax advantage)
- Plan for Medicare gaps
- Long-term care insurance consideration

Start now - even small amounts compound significantly over {years_to_retirement} years!"""

            # Budgeting advice
            elif any(word in query_lower for word in ['budget', 'budgeting', 'expenses', 'spending', 'save', 'saving']):
                monthly_income = int(income / 12)
                return f"""💰 **Smart Budgeting Strategy**

**Your Monthly Income: ${monthly_income:,}**

**50/30/20 Rule Breakdown:**
- **50% Needs (${int(monthly_income * 0.5):,})**: Rent, utilities, groceries, minimum debt payments
- **30% Wants (${int(monthly_income * 0.3):,})**: Dining out, entertainment, hobbies, subscriptions
- **20% Savings (${int(monthly_income * 0.2):,})**: Emergency fund, retirement, investments

**Priority Order:**
1. **Emergency Fund** - Start with $1,000, build to 3-6 months expenses
2. **High-Interest Debt** - Pay off credit cards (>15% interest)
3. **Retirement Match** - Get full employer 401(k) match
4. **Additional Savings** - Roth IRA, extra retirement, goals

**Budgeting Tools:**
- **Apps**: Mint, YNAB, Personal Capital
- **Method**: Zero-based budgeting
- **Tracking**: Review weekly, adjust monthly

**Money-Saving Tips:**
- Cook at home (save $200-400/month)
- Review subscriptions quarterly
- Use high-yield savings (4-5% APY)
- Automate savings (pay yourself first)

**Common Budget Killers:**
- Lifestyle inflation
- Impulse purchases
- Subscription creep
- Not tracking small expenses

Would you like help setting up a specific budget category or savings goal?"""

            # Debt advice
            elif any(word in query_lower for word in ['debt', 'loan', 'credit card', 'payoff', 'payment']):
                return f"""💳 **Debt Payoff Strategy**

**Two Main Approaches:**

**1. Debt Avalanche (Mathematically Optimal)**
- Pay minimums on all debts
- Put extra money toward highest interest rate debt
- Saves most money in interest

**2. Debt Snowball (Psychologically Motivating)**
- Pay minimums on all debts
- Put extra money toward smallest balance
- Builds momentum with quick wins

**Step-by-Step Plan:**
1. **List All Debts**: Balance, minimum payment, interest rate
2. **Choose Strategy**: Avalanche vs. Snowball
3. **Find Extra Money**: Cut expenses, increase income
4. **Automate Payments**: Set up automatic payments
5. **Track Progress**: Celebrate milestones

**Credit Card Strategy:**
- Stop using cards while paying off
- Consider balance transfer (0% intro APR)
- Pay more than minimum (even $25 extra helps)
- Keep old accounts open after payoff

**Additional Tips:**
- Negotiate with creditors for lower rates
- Consider debt consolidation loan
- Use windfalls for debt payoff
- Build small emergency fund first ($1,000)

**After Debt Freedom:**
- Redirect payments to savings/investments
- Keep one card active for credit history
- Build full emergency fund

Remember: Every extra dollar toward debt saves you money in interest!"""

            # General financial advice
            else:
                return f"""👋 **Welcome to PennyWise - Your AI Financial Advisor!**

I'm here to help you with all your financial questions. Based on your profile:
- Age: {age}
- Income: ${income:,}

**I can help you with:**

🏠 **Home Buying**: First-time buyer guidance, mortgage advice, budgeting
📈 **Investing**: Portfolio allocation, fund selection, retirement accounts
🏖️ **Retirement Planning**: 401(k) optimization, IRA strategies, timeline planning
💰 **Budgeting**: Expense tracking, savings goals, debt management
💳 **Debt Payoff**: Strategy selection, payment optimization, credit improvement
🎯 **Financial Goals**: Emergency funds, major purchases, wealth building

**Popular Questions:**
- "How much house can I afford?"
- "What's the best investment strategy for my age?"
- "How much should I save for retirement?"
- "Should I pay off debt or invest?"
- "How do I create a budget?"

**Just ask me anything!** I'll provide personalized advice based on your situation and current financial best practices.

What financial topic would you like to explore today?"""

            return None

        except Exception as e:
            logger.error(f"Error generating simple response: {e}")
            return None

    def _determine_required_agents(self, query: str, reasoning_data: Dict[str, Any]) -> List[str]:
        """Determine which agents are needed based on the query"""
        required = []
        
        query_lower = query.lower()
        
        # Financial data needed
        if any(word in query_lower for word in ['stock', 'price', 'market', 'invest', 'portfolio', 'return']):
            required.append('financial')
        
        # Market analysis needed
        if any(word in query_lower for word in ['market', 'trend', 'sentiment', 'sector', 'economy']):
            required.append('market')
        
        # Geographic data needed
        if any(word in query_lower for word in ['country', 'region', 'international', 'global', 'local']):
            required.append('geography')
        
        return required
    
    def _needs_planning(self, query: str, reasoning_data: Dict[str, Any]) -> bool:
        """Determine if financial planning is needed"""
        planning_keywords = ['plan', 'strategy', 'goal', 'retirement', 'save', 'budget', 'comprehensive']
        return any(word in query.lower() for word in planning_keywords)
    
    async def _execute_task(self, task: AgentTask) -> AgentTaskResult:
        """Execute a task with an agent"""
        try:
            if task.agent_name not in self.agents:
                logger.warning(f"Agent '{task.agent_name}' not available, using fallback")
                return AgentTaskResult(
                    task_id=task.task_id,
                    agent_name=task.agent_name,
                    success=True,
                    data={"fallback": True, "message": f"{task.agent_name} agent not available"}
                )
            
            agent = self.agents[task.agent_name]
            
            # Execute based on task type
            if task.task_type == "validate_input":
                result = await agent.validate_input(task.data.get("query", ""))
            elif task.task_type == "get_context":
                result = await agent.get_context(task.data.get("user_id"), task.data.get("query"))
            elif task.task_type == "analyze_query":
                result = await agent.analyze_query(task.data.get("query"), task.data.get("context", {}))
            elif task.task_type == "create_plan":
                result = await agent.create_plan(task.data.get("query"), task.data.get("context", {}))
            elif task.task_type == "synthesize_response":
                result = await agent.synthesize_response(**task.data)
            else:
                result = {"success": False, "error": f"Unknown task type: {task.task_type}"}
            
            return AgentTaskResult(
                task_id=task.task_id,
                agent_name=task.agent_name,
                success=result.get("success", True),
                data=result
            )
            
        except Exception as e:
            logger.error(f"Task execution failed for {task.agent_name}: {e}")
            return AgentTaskResult(
                task_id=task.task_id,
                agent_name=task.agent_name,
                success=False,
                data={"error": str(e)}
            )
    
    async def _execute_tasks_parallel(self, tasks: List[AgentTask]) -> List[AgentTaskResult]:
        """Execute multiple tasks in parallel"""
        if not tasks:
            return []
        
        # Create coroutines for all tasks
        coroutines = [self._execute_task(task) for task in tasks]
        
        # Execute all tasks concurrently
        results = await asyncio.gather(*coroutines, return_exceptions=True)
        
        # Handle any exceptions
        responses = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                responses.append(AgentTaskResult(
                    task_id=tasks[i].task_id,
                    agent_name=tasks[i].agent_name,
                    success=False,
                    data={},
                    error=str(result)
                ))
            else:
                responses.append(result)
        
        return responses
    
    async def _process_tasks(self):
        """Background task processor"""
        while self.is_running:
            try:
                # Process any queued tasks
                await asyncio.sleep(0.1)
            except Exception as e:
                logger.error(f"Task processor error: {e}")
    
    def get_agent_status(self) -> Dict[str, str]:
        """Get status of all registered agents"""
        return self.agent_status.copy()
    
    async def shutdown(self):
        """Shutdown the orchestrator"""
        self.is_running = False
        logger.info("Orchestrator Agent shutdown")


