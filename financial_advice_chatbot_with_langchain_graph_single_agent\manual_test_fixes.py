#!/usr/bin/env python3
"""
Manual test to verify the fixes are working
"""

def test_main_py_fixes():
    """Test that main.py has the required fixes"""
    print("🔧 TESTING MAIN.PY FIXES")
    print("=" * 50)
    
    try:
        with open("main.py", "r") as f:
            content = f.read()
        
        # Test 1: Check for thinking process fixes
        print("1. Testing Thinking Process Variable Fixes:")
        
        if "analysis_type = thinking_data.get('analysis_type'" in content:
            print("   ✅ analysis_type variable properly defined")
        else:
            print("   ❌ analysis_type variable not found")
            return False
        
        if "vector_context = thinking_data.get('vector_context'" in content:
            print("   ✅ vector_context variable properly defined")
        else:
            print("   ❌ vector_context variable not found")
            return False
        
        if "financial_plan = thinking_data.get('financial_plan'" in content:
            print("   ✅ financial_plan variable properly defined")
        else:
            print("   ❌ financial_plan variable not found")
            return False
        
        if "processing_time = thinking_data.get('processing_time'" in content:
            print("   ✅ processing_time variable properly defined")
        else:
            print("   ❌ processing_time variable not found")
            return False
        
        # Test 2: Check for feedback storage fixes
        print("\n2. Testing Feedback Storage Fixes:")
        
        if "memory.store_feedback(st.session_state.user_id, prompt, feedback_message, 4)" in content:
            print("   ✅ Positive feedback storage found")
        else:
            print("   ❌ Positive feedback storage not found")
            return False
        
        if "memory.store_feedback(st.session_state.user_id, prompt, feedback_message, 2)" in content:
            print("   ✅ Negative feedback storage found")
        else:
            print("   ❌ Negative feedback storage not found")
            return False
        
        if "[DETAILED_FEEDBACK]" in content:
            print("   ✅ Detailed feedback tag found")
        else:
            print("   ❌ Detailed feedback tag not found")
            return False
        
        # Test 3: Check for UI elements
        print("\n3. Testing UI Elements:")
        
        if "🧠 Show My Thinking" in content:
            print("   ✅ Combined thinking button found")
        else:
            print("   ❌ Combined thinking button not found")
            return False
        
        if "👍 Good" in content and "👎 Poor" in content:
            print("   ✅ Thumbs up/down buttons found")
        else:
            print("   ❌ Thumbs up/down buttons not found")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing main.py: {e}")
        return False

def test_workflow_fixes():
    """Test that enhanced_workflow.py has the required fixes"""
    print("\n🔧 TESTING WORKFLOW FIXES")
    print("=" * 50)
    
    try:
        with open("graphs/enhanced_workflow.py", "r") as f:
            content = f.read()
        
        # Test feedback retrieval enhancement
        print("1. Testing Feedback Retrieval Enhancement:")
        
        if "feedback_history = memory.get_user_feedback_history" in content:
            print("   ✅ Feedback history retrieval found")
        else:
            print("   ❌ Feedback history retrieval not found")
            return False
        
        if "RECENT USER FEEDBACK:" in content:
            print("   ✅ Recent feedback processing found")
        else:
            print("   ❌ Recent feedback processing not found")
            return False
        
        if "USER FEEDBACK HISTORY:" in content:
            print("   ✅ Feedback history processing found")
        else:
            print("   ❌ Feedback history processing not found")
            return False
        
        if "Adjust your response style based on this feedback!" in content:
            print("   ✅ Feedback instruction found")
        else:
            print("   ❌ Feedback instruction not found")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing workflow: {e}")
        return False

def main():
    """Run all manual tests"""
    print("🧪 MANUAL TESTING OF CURRENT PROJECT FIXES")
    print("=" * 70)
    
    test1_passed = test_main_py_fixes()
    test2_passed = test_workflow_fixes()
    
    print("\n" + "=" * 70)
    print("📊 MANUAL TEST RESULTS")
    print("=" * 70)
    
    print(f"1. Main.py Fixes: {'✅ PASSED' if test1_passed else '❌ FAILED'}")
    print(f"2. Workflow Fixes: {'✅ PASSED' if test2_passed else '❌ FAILED'}")
    
    if test1_passed and test2_passed:
        print("\n🎉 ALL FIXES VERIFIED SUCCESSFULLY!")
        print("\n📋 WHAT'S BEEN FIXED:")
        print("✅ 1. Thinking Process Variables - No more NameError")
        print("✅ 2. Enhanced Feedback Storage - Dual storage method")
        print("✅ 3. Improved Feedback Retrieval - Better context extraction")
        print("✅ 4. Combined Thinking Process - User-friendly display")
        print("✅ 5. Simplified Feedback UI - Thumbs up/down + comments")
        
        print("\n🚀 READY TO TEST IN FRONTEND:")
        print("1. Start the app: streamlit run main.py")
        print("2. Ask a question")
        print("3. Give feedback using 👍👎 buttons")
        print("4. Ask another question - bot should remember feedback")
        print("5. Click 'Show My Thinking' - should work without errors")
        
        return True
    else:
        print("\n⚠️ Some fixes failed verification")
        return False

if __name__ == "__main__":
    main()
