# 🤖 PennyWise Multi-Agent Financial Advisor

## 🏗️ Multi-Agent Architecture

This project implements a sophisticated multi-agent financial advisory system using LangGraph and MCP (Model Context Protocol) tools.

### 🎯 Agent Architecture

```
┌─────────────────────────────────────────────────────────────────┐
│                    ORCHESTRATOR AGENT                           │
│                  (Main Coordination)                            │
└─────────────────────────────────────────────────────────────────┘
                                │
                ┌───────────────┼───────────────┐
                ▼               ▼               ▼
┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐
│  MEMORY AGENT   │ │ PLANNING AGENT  │ │REASONING AGENT  │
│  • Short-term   │ │ • Goal Analysis │ │ • Logic Chain   │
│  • Semantic     │ │ • Step Planning │ │ • Decision Tree │
│  • Behavioral   │ │ • Execution     │ │ • Validation    │
│  • Episodic     │ │ • Monitoring    │ │ • Synthesis     │
└─────────────────┘ └─────────────────┘ └─────────────────┘
                                │
                ┌───────────────┼───────────────┐
                ▼               ▼               ▼
┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐
│FINANCIAL AGENT  │ │GEOGRAPHY AGENT  │ │ MARKET AGENT    │
│ • Stock Data    │ │ • Country Data  │ │ • Sentiment     │
│ • Economic Data │ │ • Demographics  │ │ • Sector Perf   │
│ • Analysis      │ │ • Regional Info │ │ • Trends        │
└─────────────────┘ └─────────────────┘ └─────────────────┘
                                │
                ┌───────────────┼───────────────┐
                ▼               ▼               ▼
┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐
│GUARDRAILS AGENT │ │FEEDBACK AGENT   │ │RESPONSE AGENT   │
│ • Input Valid   │ │ • Collection    │ │ • Synthesis     │
│ • Safety Check  │ │ • Analysis      │ │ • Formatting    │
│ • Compliance    │ │ • Learning      │ │ • Delivery      │
└─────────────────┘ └─────────────────┘ └─────────────────┘
```

## 🚀 Quick Start

### Prerequisites
- Python 3.11+
- Docker and Docker Compose (optional)
- Groq API Key

### 1. Clone and Setup
```bash
cd financial_advisor_multi_agent
cp .env.example .env
# Edit .env and add your Groq API key
```

### 2. Install Dependencies
```bash
pip install -r requirements.txt
```

### 3. Start with Docker (Recommended)
```bash
docker-compose up -d
```

### 4. Or Start Manually
```bash
python start.py
```

### 5. Access the Application
- **Main App**: http://localhost:8501
- **MongoDB Express**: http://localhost:8081 (admin/admin123)
- **Redis Commander**: http://localhost:8082

## 🔧 Dynamic Configuration

The frontend provides runtime configuration for:

### Database URLs
- **MongoDB URL**: `mongodb://localhost:27017`
- **Redis URL**: `redis://localhost:6379`
- **Qdrant URL**: `http://localhost:6333`
- **Groq API Key**: Your LLM access key

### Configuration Features
- ✅ **Runtime Updates**: Change database connections without restart
- ✅ **Connection Testing**: Verify database connectivity
- ✅ **Agent Monitoring**: Real-time agent status
- ✅ **System Health**: Overall system status dashboard

## 🧠 Memory Systems

### Multi-Layer Memory Architecture
1. **Short-Term Memory** (Redis)
   - Session data and conversation history
   - User profiles and preferences
   - Immediate feedback storage

2. **Semantic Memory** (Qdrant)
   - Financial knowledge base
   - Vector-based knowledge retrieval
   - Contextual information search

3. **Behavioral Memory** (MongoDB)
   - User interaction patterns
   - Preference learning
   - Behavioral insights

4. **Episodic Memory** (MongoDB)
   - Specific interaction episodes
   - Conversation context
   - Historical analysis

## 🎯 Agent Capabilities

### Core Agents
- **Orchestrator**: Coordinates all agents and manages workflow
- **Memory**: Handles all memory operations across agents
- **Planning**: Creates comprehensive financial plans
- **Reasoning**: Provides logical analysis and decision support
- **Response**: Synthesizes final responses from all inputs

### Specialized Agents
- **Financial**: Real-time stock data and economic indicators
- **Market**: Market sentiment and sector analysis
- **Geography**: Location-based financial insights
- **Guardrails**: Safety, compliance, and input validation
- **Feedback**: User feedback collection and learning

## 🛠️ Features

### User Experience
- 🧠 **Combined Thinking Process**: Single button shows planning + reasoning
- 💬 **Simplified Feedback**: Thumbs up/down + optional comments
- 🔄 **Dynamic Configuration**: Runtime database URL updates
- 📊 **Agent Monitoring**: Real-time system status

### Technical Features
- 🤖 **Multi-Agent Coordination**: Parallel task execution
- 🔧 **MCP Integration**: Modern tool protocol support
- 📈 **Real-Time Data**: Live market and financial data
- 🛡️ **Safety First**: Comprehensive guardrails and validation
- 💾 **Persistent Memory**: Multi-layer memory architecture

## 📁 Project Structure

```
financial_advisor_multi_agent/
├── agents/                    # All agent implementations
├── memory/                    # Memory system implementations
├── config/                    # Configuration management
├── frontend/                  # Streamlit UI
├── docker-compose.yml         # Multi-service orchestration
├── Dockerfile                 # Container definition
├── requirements.txt           # Python dependencies
├── start.py                   # Quick start script
└── main.py                    # Application entry point
```

## 🔄 Workflow Process

### User Query Processing
1. **Input Validation** (Guardrails Agent)
   - Safety and relevance checking
   - PII detection and filtering

2. **Memory Retrieval** (Memory Agent)
   - User profile and conversation history
   - Relevant knowledge and feedback

3. **Reasoning Analysis** (Reasoning Agent)
   - Query complexity assessment
   - Reasoning approach selection

4. **Data Gathering** (Parallel Execution)
   - Financial data (Financial Agent)
   - Market analysis (Market Agent)
   - Geographic insights (Geography Agent)

5. **Planning** (Planning Agent)
   - Goal analysis and step creation
   - Timeline and priority setting

6. **Response Synthesis** (Response Agent)
   - Combine all agent inputs
   - Generate personalized response

7. **Output Validation** (Guardrails Agent)
   - Compliance and safety checking
   - Quality assurance

## 🧪 Testing

### Manual Testing
```bash
# Test individual agents
python -m pytest tests/test_agents.py

# Test memory systems
python -m pytest tests/test_memory.py

# Test configuration
python -m pytest tests/test_config.py
```

### Integration Testing
```bash
# Test full workflow
python -m pytest tests/test_integration.py

# Test with Docker
docker-compose -f docker-compose.test.yml up --abort-on-container-exit
```

## 🚀 Deployment

### Development
```bash
python start.py
```

### Production with Docker
```bash
docker-compose up -d
```

### Production with Load Balancer
```bash
docker-compose --profile production up -d
```

### Monitoring (Optional)
```bash
docker-compose --profile monitoring up -d
```

## 🔧 Configuration

### Environment Variables
```bash
# Required
GROQ_API_KEY=your_groq_api_key

# Optional (defaults provided)
MONGODB_URL=mongodb://localhost:27017
REDIS_URL=redis://localhost:6379
QDRANT_URL=http://localhost:6333
ALPHA_VANTAGE_API_KEY=your_alpha_vantage_key
```

### Runtime Configuration
- Access the sidebar in the web interface
- Update database URLs dynamically
- Test connections in real-time
- Monitor agent status

## 📊 Monitoring

### System Health
- Agent status monitoring
- Database connection health
- Memory usage tracking
- Response time metrics

### User Analytics
- Conversation insights
- Feedback analysis
- Usage patterns
- Satisfaction metrics

## 🛡️ Security

### Data Protection
- No PII storage in logs
- Encrypted database connections
- Input sanitization
- Output validation

### Compliance
- Financial advice disclaimers
- Risk disclosures
- Regulatory compliance checks
- Audit trail maintenance

## 🤝 Contributing

### Development Setup
1. Fork the repository
2. Create feature branch
3. Install development dependencies
4. Run tests before committing
5. Submit pull request

### Code Standards
- Follow PEP 8 for Python
- Add type hints
- Include docstrings
- Write unit tests
- Update documentation

## 📝 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

### Common Issues
- **Database Connection**: Check URLs in configuration
- **API Keys**: Verify Groq API key is valid
- **Memory Issues**: Restart Redis/MongoDB services
- **Agent Errors**: Check logs for specific error messages

### Getting Help
- Check the documentation
- Review error logs
- Test individual components
- Contact support team

## 🎯 Roadmap

### Upcoming Features
- [ ] Additional financial data sources
- [ ] Advanced portfolio analysis
- [ ] Tax optimization strategies
- [ ] International market support
- [ ] Mobile application
- [ ] Voice interface
- [ ] Advanced analytics dashboard

### Performance Improvements
- [ ] Agent response caching
- [ ] Parallel processing optimization
- [ ] Memory system performance
- [ ] Database query optimization

---

**PennyWise Multi-Agent Financial Advisor** - Intelligent, scalable, and user-friendly financial guidance powered by cutting-edge multi-agent AI architecture.
