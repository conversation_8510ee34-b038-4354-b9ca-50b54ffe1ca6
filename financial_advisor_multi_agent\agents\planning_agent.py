"""
Planning Agent - Intelligent Financial Planning and Strategy
Specialized AI agent for creating financial plans, goal setting, and strategic advice
"""

import asyncio
import logging
import os
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from dataclasses import dataclass
from .base_agent import BaseAgent

logger = logging.getLogger(__name__)

@dataclass
class PlanningStep:
    """Single planning step"""
    step_id: str
    description: str
    action: str
    timeline: str
    priority: str
    status: str = "pending"

@dataclass
class FinancialGoal:
    """Financial goal definition"""
    goal_id: str
    goal_type: str
    target_amount: float
    timeline: str
    priority: str
    current_progress: float = 0.0

class PlanningAgent(BaseAgent):
    """Intelligent agent specialized in financial planning and strategy"""

    def __init__(self):
        super().__init__(
            agent_name="Financial Planner",
            specialization="Financial planning, goal setting, strategic advice, and step-by-step financial roadmaps"
        )
        self.planning_templates = {}
        self.goal_frameworks = {}

    async def _agent_specific_init(self):
        """Initialize planning-specific capabilities"""
        try:
            # Load planning templates and frameworks
            await self._load_planning_frameworks()

            logger.info("Planning Agent specific initialization completed")

        except Exception as e:
            logger.error(f"Planning Agent specific initialization failed: {e}")
            raise

    async def _load_planning_frameworks(self):
        """Load planning frameworks and templates"""
        self.planning_templates = {
            "retirement_planning": {
                "steps": [
                    "Calculate current retirement savings",
                    "Determine retirement income needs",
                    "Estimate retirement timeline",
                    "Choose appropriate investment vehicles",
                    "Create contribution schedule",
                    "Plan for healthcare costs",
                    "Review and adjust annually"
                ],
                "timeline": "20-40 years",
                "priority": "high"
            },
            "emergency_fund": {
                "steps": [
                    "Calculate monthly expenses",
                    "Set target amount (3-6 months expenses)",
                    "Choose high-yield savings account",
                    "Set up automatic transfers",
                    "Build fund gradually",
                    "Keep separate from other savings"
                ],
                "timeline": "6-12 months",
                "priority": "critical"
            },
            "debt_payoff": {
                "steps": [
                    "List all debts with balances and rates",
                    "Choose strategy (avalanche vs snowball)",
                    "Create payment schedule",
                    "Cut unnecessary expenses",
                    "Consider debt consolidation",
                    "Avoid new debt",
                    "Celebrate milestones"
                ],
                "timeline": "1-5 years",
                "priority": "high"
            },
            "house_purchase": {
                "steps": [
                    "Determine budget and down payment",
                    "Check and improve credit score",
                    "Save for down payment and closing costs",
                    "Get pre-approved for mortgage",
                    "Research neighborhoods and markets",
                    "Find real estate agent",
                    "Make offer and close"
                ],
                "timeline": "1-3 years",
                "priority": "medium"
            }
        }

        self.goal_frameworks = {
            "SMART": {
                "Specific": "Clearly defined goal",
                "Measurable": "Quantifiable target",
                "Achievable": "Realistic given circumstances",
                "Relevant": "Aligned with values and priorities",
                "Time-bound": "Clear deadline"
            },
            "priority_matrix": {
                "urgent_important": "Do first (emergency fund, high-interest debt)",
                "important_not_urgent": "Schedule (retirement, investments)",
                "urgent_not_important": "Delegate or minimize",
                "neither": "Eliminate"
            }
        }

    def _build_system_prompt(self, context: Dict[str, Any]) -> str:
        """Build specialized financial planning system prompt"""
        user_profile = context.get('user_profile', {})
        age = user_profile.get('age', 30)
        income = user_profile.get('income', 50000)
        goals = user_profile.get('goals', [])

        return f"""You are a specialized Financial Planning AI agent with expertise in creating comprehensive financial plans.

Your expertise includes:
- Strategic financial planning and goal setting
- Step-by-step action plans and roadmaps
- Timeline and priority management
- Risk assessment and contingency planning
- Progress tracking and milestone setting

Current client profile:
- Age: {age}
- Income: ${income:,}
- Goals: {goals}

Available planning frameworks:
- SMART goal framework
- Priority matrix for financial decisions
- Retirement planning templates
- Emergency fund strategies
- Debt payoff plans
- Home buying roadmaps

Create detailed, actionable financial plans with:
1. Clear step-by-step actions
2. Realistic timelines
3. Priority levels
4. Success metrics
5. Contingency plans

Respond in JSON format with structured planning recommendations."""

    async def _fallback_response(self, user_id: str, query: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Fallback response when LLM is not available"""
        user_profile = context.get('user_profile', {})
        goals = user_profile.get('goals', [])

        # Identify planning need
        query_lower = query.lower()

        if 'retirement' in query_lower:
            template = self.planning_templates["retirement_planning"]
            return {
                "analysis": f"Retirement planning requires a {template['timeline']} approach with {template['priority']} priority.",
                "recommendations": template["steps"],
                "confidence": 0.8,
                "reasoning": "Standard retirement planning framework",
                "data": {"template": "retirement_planning", "timeline": template["timeline"]},
                "next_steps": template["steps"][:3]  # First 3 steps
            }

        elif any(word in query_lower for word in ['emergency', 'fund', 'savings']):
            template = self.planning_templates["emergency_fund"]
            return {
                "analysis": f"Emergency fund planning is {template['priority']} priority with {template['timeline']} timeline.",
                "recommendations": template["steps"],
                "confidence": 0.9,
                "reasoning": "Emergency fund is foundational to financial security",
                "data": {"template": "emergency_fund", "timeline": template["timeline"]},
                "next_steps": template["steps"][:3]
            }

        elif any(word in query_lower for word in ['debt', 'payoff', 'loan']):
            template = self.planning_templates["debt_payoff"]
            return {
                "analysis": f"Debt payoff planning is {template['priority']} priority with {template['timeline']} timeline.",
                "recommendations": template["steps"],
                "confidence": 0.8,
                "reasoning": "Debt reduction improves financial flexibility",
                "data": {"template": "debt_payoff", "timeline": template["timeline"]},
                "next_steps": template["steps"][:3]
            }

        elif any(word in query_lower for word in ['house', 'home', 'buy', 'purchase']):
            template = self.planning_templates["house_purchase"]
            return {
                "analysis": f"Home buying planning requires {template['timeline']} preparation with {template['priority']} priority.",
                "recommendations": template["steps"],
                "confidence": 0.7,
                "reasoning": "Home purchase requires careful planning and preparation",
                "data": {"template": "house_purchase", "timeline": template["timeline"]},
                "next_steps": template["steps"][:3]
            }

        else:
            return {
                "analysis": "I can help create comprehensive financial plans for retirement, emergency funds, debt payoff, and major purchases.",
                "recommendations": [
                    "Identify your top financial goals",
                    "Prioritize goals using urgency/importance matrix",
                    "Create SMART goals with specific timelines",
                    "Develop step-by-step action plans",
                    "Set up progress tracking and milestones"
                ],
                "confidence": 0.6,
                "reasoning": "General financial planning principles",
                "data": {"available_templates": list(self.planning_templates.keys())},
                "next_steps": ["Define specific financial goals", "Assess current financial situation"]
            }

    async def initialize(self):
        """Initialize the planning agent"""
        try:
            logger.info("Initializing Planning Agent")
            
            self.is_initialized = True
            logger.info("✅ Planning Agent initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize Planning Agent: {e}")
            raise
    
    async def create_plan(self, user_id: str, query: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Create a financial plan based on user query and context"""
        try:
            user_profile = context.get('user_profile', {})
            
            # Analyze query for planning requirements
            plan_type = self._determine_plan_type(query)
            
            # Create planning steps
            planning_steps = []
            
            if plan_type == "retirement":
                planning_steps = await self._create_retirement_plan(user_profile, query)
            elif plan_type == "investment":
                planning_steps = await self._create_investment_plan(user_profile, query)
            elif plan_type == "budgeting":
                planning_steps = await self._create_budgeting_plan(user_profile, query)
            elif plan_type == "debt_management":
                planning_steps = await self._create_debt_plan(user_profile, query)
            elif plan_type == "emergency_fund":
                planning_steps = await self._create_emergency_plan(user_profile, query)
            else:
                planning_steps = await self._create_general_plan(user_profile, query)
            
            return {
                "plan_type": plan_type,
                "planning_steps": planning_steps,
                "user_profile_used": bool(user_profile),
                "created_at": datetime.now().isoformat(),
                "estimated_timeline": self._estimate_timeline(planning_steps)
            }
            
        except Exception as e:
            logger.error(f"Failed to create plan: {e}")
            return {"error": str(e), "planning_steps": []}
    
    def _determine_plan_type(self, query: str) -> str:
        """Determine the type of financial plan needed"""
        query_lower = query.lower()
        
        if any(word in query_lower for word in ['retire', 'retirement', '401k', 'ira', 'pension']):
            return "retirement"
        elif any(word in query_lower for word in ['invest', 'investment', 'portfolio', 'stock', 'bond']):
            return "investment"
        elif any(word in query_lower for word in ['budget', 'budgeting', 'expense', 'spending']):
            return "budgeting"
        elif any(word in query_lower for word in ['debt', 'loan', 'credit', 'payoff']):
            return "debt_management"
        elif any(word in query_lower for word in ['emergency', 'emergency fund', 'savings']):
            return "emergency_fund"
        else:
            return "general"
    
    async def _create_retirement_plan(self, user_profile: Dict[str, Any], query: str) -> List[Dict[str, Any]]:
        """Create retirement planning steps"""
        steps = []
        age = user_profile.get('age', 30)
        income = user_profile.get('income', 50000)
        
        steps.append({
            "step": "assessment",
            "description": "Assess current retirement readiness",
            "action": f"Review current savings and calculate retirement needs based on age {age}",
            "timeline": "Immediate",
            "priority": "high",
            "status": "completed"
        })
        
        steps.append({
            "step": "goal_setting",
            "description": "Set retirement savings target",
            "action": f"Target 10-15x annual income (${income:,}) by retirement",
            "timeline": "Long-term",
            "priority": "high",
            "status": "in_progress"
        })
        
        if age < 50:
            steps.append({
                "step": "401k_maximization",
                "description": "Maximize employer 401(k) match",
                "action": "Contribute at least enough to get full employer match",
                "timeline": "Next paycheck",
                "priority": "high",
                "status": "pending"
            })
        
        steps.append({
            "step": "ira_contribution",
            "description": "Consider IRA contributions",
            "action": "Evaluate Roth vs Traditional IRA based on current tax situation",
            "timeline": "This tax year",
            "priority": "medium",
            "status": "pending"
        })
        
        steps.append({
            "step": "investment_allocation",
            "description": "Optimize investment allocation",
            "action": f"Consider age-appropriate allocation (roughly {100-age}% stocks)",
            "timeline": "Next quarter",
            "priority": "medium",
            "status": "pending"
        })
        
        return steps
    
    async def _create_investment_plan(self, user_profile: Dict[str, Any], query: str) -> List[Dict[str, Any]]:
        """Create investment planning steps"""
        steps = []
        risk_tolerance = user_profile.get('risk_tolerance', 'moderate')
        income = user_profile.get('income', 50000)
        
        steps.append({
            "step": "risk_assessment",
            "description": "Confirm risk tolerance and investment timeline",
            "action": f"Current risk tolerance: {risk_tolerance} - validate this aligns with goals",
            "timeline": "Immediate",
            "priority": "high",
            "status": "completed"
        })
        
        steps.append({
            "step": "emergency_fund_check",
            "description": "Ensure emergency fund is adequate before investing",
            "action": "Verify 3-6 months expenses in emergency fund",
            "timeline": "Before investing",
            "priority": "high",
            "status": "pending"
        })
        
        steps.append({
            "step": "account_setup",
            "description": "Set up appropriate investment accounts",
            "action": "Consider tax-advantaged accounts (401k, IRA) before taxable accounts",
            "timeline": "1-2 weeks",
            "priority": "high",
            "status": "pending"
        })
        
        if risk_tolerance == 'conservative':
            allocation = "60% stocks, 40% bonds"
        elif risk_tolerance == 'aggressive':
            allocation = "90% stocks, 10% bonds"
        else:
            allocation = "70% stocks, 30% bonds"
        
        steps.append({
            "step": "portfolio_allocation",
            "description": "Implement diversified portfolio",
            "action": f"Suggested allocation: {allocation}",
            "timeline": "1 month",
            "priority": "medium",
            "status": "pending"
        })
        
        steps.append({
            "step": "regular_contributions",
            "description": "Set up automatic investing",
            "action": f"Automate monthly contributions (suggest 10-20% of ${income:,} income)",
            "timeline": "Ongoing",
            "priority": "medium",
            "status": "pending"
        })
        
        return steps
    
    async def _create_budgeting_plan(self, user_profile: Dict[str, Any], query: str) -> List[Dict[str, Any]]:
        """Create budgeting planning steps"""
        steps = []
        income = user_profile.get('income', 50000)
        
        steps.append({
            "step": "expense_tracking",
            "description": "Track all expenses for one month",
            "action": "Use app or spreadsheet to record every expense",
            "timeline": "Next 30 days",
            "priority": "high",
            "status": "in_progress"
        })
        
        steps.append({
            "step": "categorize_expenses",
            "description": "Categorize expenses into needs vs wants",
            "action": "Separate fixed costs, variable needs, and discretionary spending",
            "timeline": "After tracking month",
            "priority": "high",
            "status": "pending"
        })
        
        steps.append({
            "step": "50_30_20_rule",
            "description": "Apply 50/30/20 budgeting rule",
            "action": f"50% needs (${income*0.5/12:,.0f}/month), 30% wants (${income*0.3/12:,.0f}/month), 20% savings (${income*0.2/12:,.0f}/month)",
            "timeline": "Next month",
            "priority": "medium",
            "status": "pending"
        })
        
        steps.append({
            "step": "automate_savings",
            "description": "Automate savings and bill payments",
            "action": "Set up automatic transfers to savings and investment accounts",
            "timeline": "2 weeks",
            "priority": "medium",
            "status": "pending"
        })
        
        return steps
    
    async def _create_debt_plan(self, user_profile: Dict[str, Any], query: str) -> List[Dict[str, Any]]:
        """Create debt management planning steps"""
        steps = []
        
        steps.append({
            "step": "debt_inventory",
            "description": "List all debts with balances and interest rates",
            "action": "Create spreadsheet with debt name, balance, minimum payment, interest rate",
            "timeline": "This week",
            "priority": "high",
            "status": "in_progress"
        })
        
        steps.append({
            "step": "strategy_selection",
            "description": "Choose debt payoff strategy",
            "action": "Consider debt avalanche (highest interest first) or snowball (smallest balance first)",
            "timeline": "After inventory",
            "priority": "high",
            "status": "pending"
        })
        
        steps.append({
            "step": "minimum_payments",
            "description": "Ensure all minimum payments are covered",
            "action": "Never miss minimum payments to avoid penalties and credit damage",
            "timeline": "Ongoing",
            "priority": "critical",
            "status": "pending"
        })
        
        steps.append({
            "step": "extra_payments",
            "description": "Apply extra payments strategically",
            "action": "Put any extra money toward target debt while maintaining minimums on others",
            "timeline": "Monthly",
            "priority": "medium",
            "status": "pending"
        })
        
        return steps
    
    async def _create_emergency_plan(self, user_profile: Dict[str, Any], query: str) -> List[Dict[str, Any]]:
        """Create emergency fund planning steps"""
        steps = []
        income = user_profile.get('income', 50000)
        monthly_expenses = income / 12 * 0.7  # Estimate 70% of income for expenses
        
        steps.append({
            "step": "calculate_target",
            "description": "Calculate emergency fund target",
            "action": f"Target: 3-6 months expenses (${monthly_expenses*3:,.0f} - ${monthly_expenses*6:,.0f})",
            "timeline": "Immediate",
            "priority": "high",
            "status": "completed"
        })
        
        steps.append({
            "step": "separate_account",
            "description": "Open dedicated high-yield savings account",
            "action": "Keep emergency fund separate from checking account",
            "timeline": "This week",
            "priority": "high",
            "status": "pending"
        })
        
        steps.append({
            "step": "automate_contributions",
            "description": "Set up automatic contributions",
            "action": f"Automate ${monthly_expenses*3/12:,.0f}/month until target reached",
            "timeline": "Next paycheck",
            "priority": "medium",
            "status": "pending"
        })
        
        steps.append({
            "step": "accessibility_check",
            "description": "Ensure funds are easily accessible",
            "action": "Verify you can access funds within 24-48 hours if needed",
            "timeline": "After setup",
            "priority": "medium",
            "status": "pending"
        })
        
        return steps
    
    async def _create_general_plan(self, user_profile: Dict[str, Any], query: str) -> List[Dict[str, Any]]:
        """Create general financial planning steps"""
        steps = []
        
        steps.append({
            "step": "financial_assessment",
            "description": "Complete comprehensive financial assessment",
            "action": "Review income, expenses, assets, debts, and financial goals",
            "timeline": "This week",
            "priority": "high",
            "status": "in_progress"
        })
        
        steps.append({
            "step": "goal_prioritization",
            "description": "Prioritize financial goals",
            "action": "Rank goals by importance and timeline (emergency fund, debt payoff, retirement, etc.)",
            "timeline": "After assessment",
            "priority": "high",
            "status": "pending"
        })
        
        steps.append({
            "step": "action_plan",
            "description": "Create specific action plan",
            "action": "Develop step-by-step plan for top 3 financial priorities",
            "timeline": "Next week",
            "priority": "medium",
            "status": "pending"
        })
        
        return steps
    
    def _estimate_timeline(self, planning_steps: List[Dict[str, Any]]) -> str:
        """Estimate overall timeline for the plan"""
        if not planning_steps:
            return "Unknown"
        
        # Count steps by timeline
        immediate_steps = sum(1 for step in planning_steps if "immediate" in step.get("timeline", "").lower())
        short_term_steps = sum(1 for step in planning_steps if any(word in step.get("timeline", "").lower() for word in ["week", "month"]))
        long_term_steps = sum(1 for step in planning_steps if any(word in step.get("timeline", "").lower() for word in ["year", "long-term", "ongoing"]))
        
        if long_term_steps > 0:
            return "Long-term (1+ years)"
        elif short_term_steps > immediate_steps:
            return "Short-term (1-6 months)"
        else:
            return "Immediate (1-4 weeks)"
    
    async def execute_task(self, task_type: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """Execute a planning task"""
        try:
            if task_type == "create_plan":
                return await self.create_plan(data['user_id'], data['query'], data.get('context', {}))
            else:
                return {'success': False, 'error': f'Unknown task type: {task_type}'}
                
        except Exception as e:
            logger.error(f"Failed to execute planning task {task_type}: {e}")
            return {'success': False, 'error': str(e)}
    
    def update_config(self, config):
        """Update configuration"""
        try:
            logger.info("Updated planning agent configuration")
        except Exception as e:
            logger.error(f"Failed to update config: {e}")
