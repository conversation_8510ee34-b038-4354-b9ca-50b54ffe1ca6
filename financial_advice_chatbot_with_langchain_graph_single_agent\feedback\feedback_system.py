from typing import Dict, List, Any, Optional
from datetime import datetime
from dataclasses import dataclass
from enum import Enum

# Fallback import logic for memory
try:
    from memory.enhanced_memory import memory
except ImportError:
    from memory.short_term_memory import short_term_memory as memory

class FeedbackType(Enum):
    RESPONSE_QUALITY = "response_quality"
    ACCURACY = "accuracy"
    HELPFULNESS = "helpfulness"
    RELEVANCE = "relevance"
    CLARITY = "clarity"
    COMPLETENESS = "completeness"
    TONE = "tone"
    SPEED = "speed"
    OVERALL = "overall"

@dataclass
class FeedbackEntry:
    user_id: str
    session_id: str
    query: str
    response: str
    quality: str
    feedback_type: FeedbackType
    comment: str
    timestamp: datetime
    response_time: float
    context: Dict[str, Any]

class FeedbackCollector:
    def __init__(self):
        self.feedback_storage = []
        self.memory = memory
        self.memory_available = True
        self.feedback_stats = {
            "total_feedback": 0,
            "feedback_by_type": {},
            "improvement_areas": []
        }

    def collect_response_feedback(self, user_id: str, session_id: str, query: str, response: str,
                                  quality: str, comment: str = "", response_time: float = 0.0,
                                  context: Dict[str, Any] = None) -> str:

        feedback = FeedbackEntry(
            user_id=user_id,
            session_id=session_id,
            query=query,
            response=response,
            quality=quality,
            feedback_type=FeedbackType.RESPONSE_QUALITY,
            comment=comment,
            timestamp=datetime.now(),
            response_time=response_time,
            context=context or {}
        )

        self.feedback_storage.append(feedback)
        self._update_stats(quality, comment)

        if self.memory_available:
            feedback_type = "POSITIVE" if quality == 'good' else "NEGATIVE"
            memory_message = f"[FEEDBACK_{feedback_type}] User marked the response as {quality} for query: '{query[:100]}...'"
            if comment:
                memory_message += f" User said: '{comment}'"
            memory_message += f" IMPORTANT: {'User liked this response style - continue similar approach' if quality == 'good' else 'User disliked this response - improve by being more detailed and careful'}"

            self.memory.add_feedback(user_id, query, response, comment or f"Quality: {quality}", 4 if quality == 'good' else 2)

        return f"Thank you for your feedback! Quality: {quality}"

    def collect_specific_feedback(self, user_id: str, session_id: str, query: str, response: str,
                                  feedback_type: str, rating: int, quality: str,
                                  comment: str = "") -> str:

        try:
            fb_type = FeedbackType(feedback_type)
        except ValueError:
            fb_type = FeedbackType.OVERALL

        feedback = FeedbackEntry(
            user_id=user_id,
            session_id=session_id,
            query=query,
            response=response,
            quality=quality,
            feedback_type=fb_type,
            comment=comment,
            timestamp=datetime.now(),
            response_time=0.0,
            context={}
        )

        self.feedback_storage.append(feedback)
        self._update_stats(quality, comment)

        return f"Thank you for your {feedback_type} feedback!"

    def _update_stats(self, quality: str, comment: str):
        self.feedback_stats["total_feedback"] = len(self.feedback_storage)
        improvement_areas = []
        if quality == 'bad':
            improvement_areas.append({
                "area": "response_quality",
                "detailed_feedback": comment if comment else None
            })
        self.feedback_stats["improvement_areas"] = improvement_areas

    def get_feedback_summary(self) -> Dict[str, Any]:
        return {
            "stats": self.feedback_stats,
            "recent_feedback": [
                {
                    "type": fb.feedback_type.value,
                    "comment": fb.comment,
                    "timestamp": fb.timestamp.isoformat()
                }
                for fb in self.feedback_storage[-10:]
            ],
            "recommendations": self._generate_recommendations()
        }

    def _generate_recommendations(self) -> List[str]:
        recommendations = []
        for area in self.feedback_stats["improvement_areas"]:
            area_name = area.get("area")
            if area_name == "response_quality":
                recommendations.append("Improve response quality - Focus on more accurate and helpful answers")
            elif area_name == "detailed_feedback":
                recommendations.append("Improve response clarity - Use simpler language and better structure")
        if not recommendations:
            recommendations.append("Great job! All feedback areas are performing well.")
        return recommendations

class FeedbackAnalyzer:
    def __init__(self, feedback_collector: FeedbackCollector):
        self.collector = feedback_collector

    def analyze_trends(self) -> Dict[str, Any]:
        if not self.collector.feedback_storage:
            return {"message": "No feedback data available"}

        daily_feedback = {}
        for fb in self.collector.feedback_storage:
            date_key = fb.timestamp.date().isoformat()
            if date_key not in daily_feedback:
                daily_feedback[date_key] = []
            daily_feedback[date_key].append(fb.quality)

        return {
            "trend": "improving" if any(fb == 'good' for fb in self.collector.feedback_storage) else "stable"
        }

    def get_user_feedback_patterns(self, user_id: str) -> Dict[str, Any]:
        user_feedback = [fb for fb in self.collector.feedback_storage if fb.user_id == user_id]
        if not user_feedback:
            return {"message": f"No feedback found for user {user_id}"}
        return {
            "total_feedback": len(user_feedback),
            "response_quality": [fb.quality for fb in user_feedback]
        }

# Global feedback system instance
feedback_collector = FeedbackCollector()
feedback_analyzer = FeedbackAnalyzer(feedback_collector)