// PennyWise Financial Advisor - Complete LangGraph Workflow
digraph PennyWise_Complete_Workflow {
	bgcolor=white fontname="Arial Bold" fontsize=18 label="PennyWise Financial Advisor - Complete LangGraph Workflow" labelloc=t nodesep=0.8 pad=0.5 rankdir=TB ranksep=1.2 ratio=fill size="16,12"
	node [fontname=Arial fontsize=11 margin="0.3,0.2" penwidth=2 shape=box style="filled,rounded"]
	edge [arrowsize=0.8 fontname=Arial fontsize=9 penwidth=1.5]
	subgraph cluster_user {
		color=darkblue fillcolor=lightblue fontsize=14 label="👤 User Interaction Layer" style=filled
		user_input [label="📝 User Input
(Financial Question)" fillcolor=lightgreen shape=ellipse]
		final_output [label="💬 Final Response
(Personalized Advice)" fillcolor=lightgreen shape=ellipse]
	}
	subgraph cluster_guardrails {
		color=darkred fillcolor=mistyrose fontsize=14 label="🛡️ Safety & Validation Layer" style=filled
		input_guardrails [label="🔍 Input Validation
• PII Detection
• Relevance Check
• Safety Filter" fillcolor=lightyellow]
		output_guardrails [label="✅ Response Validation
• Content Safety
• Financial Compliance
• Quality Check" fillcolor=lightyellow]
	}
	subgraph cluster_workflow {
		color=darkgreen fillcolor=lightcyan fontsize=14 label="🧠 Enhanced Workflow Engine" style=filled
		query_analysis [label="🔍 Query Analysis
• Intent Detection
• Complexity Assessment
• Tool Requirements" fillcolor=lightcyan]
		context_retrieval [label="📚 Context Retrieval
• Memory Systems
• User Profile
• Conversation History" fillcolor=lightcyan]
		decision_router [label="🎯 Decision Router
• Direct Response
• Planning Required
• Reasoning Required" fillcolor=orange shape=diamond]
	}
	subgraph cluster_memory {
		color=purple fillcolor=lavender fontsize=14 label="🧠 Memory Systems" style=filled
		short_term [label="⚡ Short-Term
(Redis)
• Session Data
• Chat History" fillcolor=lightcyan]
		semantic [label="🔍 Semantic
(Qdrant)
• Financial Knowledge
• Vector Search" fillcolor=lightcyan]
		behavioral [label="📊 Behavioral
(MongoDB)
• User Patterns
• Preferences" fillcolor=lightcyan]
		episodic [label="📝 Episodic
(MongoDB)
• Past Interactions
• Context Memory" fillcolor=lightcyan]
		long_term [label="🏛️ Long-Term
(Unified)
• Complete Profile
• Journey Tracking" fillcolor=lightcyan]
	}
	subgraph cluster_planning {
		color=darkmagenta fillcolor=thistle fontsize=14 label="📋 Planning System" style=filled
		plan_creation [label="📝 Plan Creation
• Goal Analysis
• Step Generation
• Resource Planning" fillcolor=lightpink]
		plan_execution [label="⚡ Plan Execution
• Step-by-Step
• Progress Tracking
• Adaptation" fillcolor=lightpink]
		plan_evaluation [label="📊 Plan Evaluation
• Quality Assessment
• Feasibility Check
• Risk Analysis" fillcolor=lightpink]
	}
	subgraph cluster_reasoning {
		color=darkorange fillcolor=peachpuff fontsize=14 label="🤔 Reasoning System" style=filled
		reasoning_router [label="🎯 Reasoning Router
• Type Selection
• Complexity Analysis" fillcolor=orange shape=diamond]
		plan_execute [label="📋 Plan-and-Execute
• Systematic Approach
• Structured Thinking" fillcolor=lightpink]
		react_reasoning [label="🔄 ReAct Reasoning
• Dynamic Thinking
• Iterative Analysis" fillcolor=lightpink]
		hybrid_reasoning [label="🔀 Hybrid Reasoning
• Combined Approach
• Complex Problems" fillcolor=lightpink]
	}
	subgraph cluster_tools {
		color=darkgreen fillcolor=honeydew fontsize=14 label="🛠️ Financial Tools" style=filled
		stock_data [label="📈 Stock Data
(Yahoo Finance)
• Real-time Prices
• Market Data" fillcolor=palegreen]
		economic_data [label="📊 Economic Data
(Alpha Vantage)
• Indicators
• Trends" fillcolor=palegreen]
		market_sentiment [label="💭 Market Sentiment
• VIX Analysis
• Trend Detection" fillcolor=palegreen]
		country_data [label="🌍 Country Data
• Economic Stats
• Demographics" fillcolor=palegreen]
	}
	subgraph cluster_response {
		color=darkblue fillcolor=aliceblue fontsize=14 label="💬 Response Generation" style=filled
		llm_synthesis [label="🧠 LLM Synthesis
• Context Integration
• Personalization
• Response Crafting" fillcolor=lightblue]
		feedback_integration [label="📊 Feedback Integration
• User Ratings
• Improvement Learning
• Quality Enhancement" fillcolor=lightblue]
	}
	user_input -> input_guardrails [label="1. Input" color=blue fontcolor=blue]
	input_guardrails -> query_analysis [label="2. Validated" color=blue fontcolor=blue]
	query_analysis -> context_retrieval [label="3. Analyze" color=blue fontcolor=blue]
	context_retrieval -> decision_router [label="4. Context" color=blue fontcolor=blue]
	decision_router -> llm_synthesis [label="Direct Response" color=green fontcolor=green]
	decision_router -> plan_creation [label="Planning Needed" color=purple fontcolor=purple]
	decision_router -> reasoning_router [label="Reasoning Needed" color=orange fontcolor=orange]
	plan_creation -> plan_execution [color=purple]
	plan_execution -> plan_evaluation [color=purple]
	plan_evaluation -> llm_synthesis [label="Plan Complete" color=purple fontcolor=purple]
	reasoning_router -> plan_execute [label=Systematic color=orange fontcolor=orange]
	reasoning_router -> react_reasoning [label=Dynamic color=orange fontcolor=orange]
	reasoning_router -> hybrid_reasoning [label=Complex color=orange fontcolor=orange]
	plan_execute -> llm_synthesis [color=orange]
	react_reasoning -> llm_synthesis [color=orange]
	hybrid_reasoning -> llm_synthesis [color=orange]
	context_retrieval -> short_term [color=purple dir=both style=dashed]
	context_retrieval -> semantic [color=purple dir=both style=dashed]
	context_retrieval -> behavioral [color=purple dir=both style=dashed]
	context_retrieval -> episodic [color=purple dir=both style=dashed]
	short_term -> long_term [color=purple style=dotted]
	semantic -> long_term [color=purple style=dotted]
	behavioral -> long_term [color=purple style=dotted]
	episodic -> long_term [color=purple style=dotted]
	query_analysis -> stock_data [color=green dir=both style=dashed]
	query_analysis -> economic_data [color=green dir=both style=dashed]
	query_analysis -> market_sentiment [color=green dir=both style=dashed]
	query_analysis -> country_data [color=green dir=both style=dashed]
	llm_synthesis -> output_guardrails [label="5. Generate" color=blue fontcolor=blue]
	output_guardrails -> feedback_integration [label="6. Validate" color=blue fontcolor=blue]
	feedback_integration -> final_output [label="7. Deliver" color=blue fontcolor=blue]
	final_output -> long_term [label="Store Interaction" color=gray style=dashed]
	final_output -> feedback_integration [label="User Feedback" color=gray style=dashed]
}
