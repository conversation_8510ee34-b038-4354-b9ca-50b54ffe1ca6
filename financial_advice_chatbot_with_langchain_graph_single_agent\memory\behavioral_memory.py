"""
Behavioral Memory System using MongoDB
Handles user behavioral patterns, preferences, and decision history
"""

from typing import Dict, List, Any, Optional
import os
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from enum import Enum

import pymongo
from pymongo import MongoClient
from mongoengine import Document, EmbeddedDocument, fields, connect
from dotenv import load_dotenv

load_dotenv()

class RiskTolerance(Enum):
    CONSERVATIVE = "conservative"
    MODERATE = "moderate"
    AGGRESSIVE = "aggressive"

class InvestmentGoal(Enum):
    RETIREMENT = "retirement"
    HOUSE_PURCHASE = "house_purchase"
    EDUCATION = "education"
    WEALTH_BUILDING = "wealth_building"
    EMERGENCY_FUND = "emergency_fund"

# MongoDB Document Models
class FinancialPreference(EmbeddedDocument):
    """User's financial preferences and settings"""
    risk_tolerance = fields.StringField(choices=[rt.value for rt in RiskTolerance])
    investment_horizon = fields.IntField()  # years
    preferred_sectors = fields.ListField(fields.StringField())
    avoided_sectors = fields.ListField(fields.StringField())
    geographic_preferences = fields.ListField(fields.StringField())
    updated_at = fields.DateTimeField(default=datetime.utcnow)

class DecisionPattern(EmbeddedDocument):
    """Pattern of user's financial decisions"""
    decision_type = fields.StringField()  # "investment", "withdrawal", "rebalance"
    frequency = fields.IntField()  # how often this pattern occurs
    typical_amount = fields.FloatField()
    conditions = fields.DictField()  # market conditions when decision made
    success_rate = fields.FloatField()  # 0.0 to 1.0
    last_occurrence = fields.DateTimeField()

class MarketBehavior(EmbeddedDocument):
    """User's behavior during different market conditions"""
    market_condition = fields.StringField()  # "bull", "bear", "volatile", "stable"
    typical_response = fields.StringField()  # "buy_more", "sell", "hold", "panic"
    confidence_level = fields.FloatField()  # 0.0 to 1.0
    response_speed = fields.StringField()  # "immediate", "delayed", "calculated"
    emotional_state = fields.StringField()  # "calm", "anxious", "excited", "fearful"

class UserBehaviorProfile(Document):
    """Main behavioral profile document for each user"""
    user_id = fields.StringField(required=True, unique=True)
    
    # Basic behavioral traits
    decision_making_style = fields.StringField()  # "analytical", "intuitive", "mixed"
    information_processing = fields.StringField()  # "detailed", "summary", "visual"
    communication_preference = fields.StringField()  # "formal", "casual", "technical"
    
    # Financial preferences
    preferences = fields.EmbeddedDocumentField(FinancialPreference)
    
    # Behavioral patterns
    decision_patterns = fields.ListField(fields.EmbeddedDocumentField(DecisionPattern))
    market_behaviors = fields.ListField(fields.EmbeddedDocumentField(MarketBehavior))
    
    # Learning and adaptation
    learning_speed = fields.FloatField(default=0.5)  # how quickly user adapts
    consistency_score = fields.FloatField(default=0.5)  # how consistent user's behavior is
    
    # Interaction patterns
    session_frequency = fields.IntField(default=0)  # sessions per week
    avg_session_duration = fields.IntField(default=0)  # minutes
    preferred_interaction_time = fields.StringField()  # "morning", "afternoon", "evening"
    
    # Goals and motivations
    primary_goals = fields.ListField(fields.StringField())
    goal_priorities = fields.DictField()  # goal -> priority score
    motivation_factors = fields.ListField(fields.StringField())
    
    # Metadata
    created_at = fields.DateTimeField(default=datetime.utcnow)
    updated_at = fields.DateTimeField(default=datetime.utcnow)
    last_interaction = fields.DateTimeField()
    
    meta = {
        'collection': 'user_behavior_profiles',
        'indexes': ['user_id', 'updated_at']
    }

class BehavioralMemory:
    """Behavioral memory system using MongoDB"""
    
    def __init__(self, 
                 mongodb_url: str = None,
                 database_name: str = None):
        
        self.mongodb_url = mongodb_url or os.getenv("MONGODB_URL", "mongodb://localhost:27017")
        self.database_name = database_name or os.getenv("MONGODB_DATABASE", "pennywise_financial_advisor")
        
        # Connect to MongoDB
        try:
            connect(
                db=self.database_name,
                host=self.mongodb_url,
                alias='behavioral_memory'
            )
            print(f"✅ Connected to MongoDB for behavioral memory: {self.database_name}")
        except Exception as e:
            print(f"⚠️ MongoDB connection warning: {e}")
    
    def get_user_behavior_profile(self, user_id: str) -> Optional[UserBehaviorProfile]:
        """Get user's behavioral profile"""
        try:
            return UserBehaviorProfile.objects(user_id=user_id).first()
        except Exception as e:
            print(f"⚠️ Error getting behavior profile: {e}")
            return None
    
    def create_user_behavior_profile(self, user_id: str, initial_data: Dict[str, Any] = None) -> UserBehaviorProfile:
        """Create new user behavioral profile"""
        try:
            # Check if profile already exists
            existing_profile = self.get_user_behavior_profile(user_id)
            if existing_profile:
                return existing_profile
            
            # Create new profile with defaults
            profile_data = {
                'user_id': user_id,
                'decision_making_style': 'mixed',
                'information_processing': 'summary',
                'communication_preference': 'casual',
                'preferences': FinancialPreference(),
                'decision_patterns': [],
                'market_behaviors': [],
                'primary_goals': [],
                'goal_priorities': {},
                'motivation_factors': []
            }
            
            # Update with provided initial data
            if initial_data:
                profile_data.update(initial_data)
            
            profile = UserBehaviorProfile(**profile_data)
            profile.save()
            
            print(f"✅ Created behavioral profile for user: {user_id}")
            return profile
            
        except Exception as e:
            print(f"⚠️ Error creating behavior profile: {e}")
            return None
    
    def update_decision_pattern(self, user_id: str, decision_type: str, 
                              amount: float, conditions: Dict[str, Any], 
                              success: bool = True):
        """Update user's decision patterns based on new decision"""
        try:
            profile = self.get_user_behavior_profile(user_id)
            if not profile:
                profile = self.create_user_behavior_profile(user_id)
            
            # Find existing pattern or create new one
            existing_pattern = None
            for pattern in profile.decision_patterns:
                if pattern.decision_type == decision_type:
                    existing_pattern = pattern
                    break
            
            if existing_pattern:
                # Update existing pattern
                existing_pattern.frequency += 1
                existing_pattern.typical_amount = (
                    existing_pattern.typical_amount * 0.8 + amount * 0.2
                )  # Weighted average
                existing_pattern.conditions.update(conditions)
                existing_pattern.success_rate = (
                    existing_pattern.success_rate * 0.9 + (1.0 if success else 0.0) * 0.1
                )
                existing_pattern.last_occurrence = datetime.utcnow()
            else:
                # Create new pattern
                new_pattern = DecisionPattern(
                    decision_type=decision_type,
                    frequency=1,
                    typical_amount=amount,
                    conditions=conditions,
                    success_rate=1.0 if success else 0.0,
                    last_occurrence=datetime.utcnow()
                )
                profile.decision_patterns.append(new_pattern)
            
            profile.updated_at = datetime.utcnow()
            profile.save()
            
            print(f"✅ Updated decision pattern for {user_id}: {decision_type}")
            
        except Exception as e:
            print(f"⚠️ Error updating decision pattern: {e}")
    
    def update_market_behavior(self, user_id: str, market_condition: str, 
                             response: str, confidence: float, emotional_state: str):
        """Update user's market behavior patterns"""
        try:
            profile = self.get_user_behavior_profile(user_id)
            if not profile:
                profile = self.create_user_behavior_profile(user_id)
            
            # Find existing behavior or create new one
            existing_behavior = None
            for behavior in profile.market_behaviors:
                if behavior.market_condition == market_condition:
                    existing_behavior = behavior
                    break
            
            if existing_behavior:
                # Update existing behavior (weighted average)
                existing_behavior.typical_response = response  # Most recent response
                existing_behavior.confidence_level = (
                    existing_behavior.confidence_level * 0.7 + confidence * 0.3
                )
                existing_behavior.emotional_state = emotional_state
            else:
                # Create new behavior
                new_behavior = MarketBehavior(
                    market_condition=market_condition,
                    typical_response=response,
                    confidence_level=confidence,
                    response_speed="calculated",  # Default
                    emotional_state=emotional_state
                )
                profile.market_behaviors.append(new_behavior)
            
            profile.updated_at = datetime.utcnow()
            profile.save()
            
            print(f"✅ Updated market behavior for {user_id}: {market_condition}")
            
        except Exception as e:
            print(f"⚠️ Error updating market behavior: {e}")
    
    def update_interaction_patterns(self, user_id: str, session_duration: int):
        """Update user's interaction patterns"""
        try:
            profile = self.get_user_behavior_profile(user_id)
            if not profile:
                profile = self.create_user_behavior_profile(user_id)
            
            # Update session statistics
            profile.session_frequency = self._calculate_weekly_frequency(user_id)
            profile.avg_session_duration = (
                profile.avg_session_duration * 0.8 + session_duration * 0.2
            )
            profile.last_interaction = datetime.utcnow()
            profile.updated_at = datetime.utcnow()
            
            # Determine preferred interaction time
            current_hour = datetime.now().hour
            if 6 <= current_hour < 12:
                time_preference = "morning"
            elif 12 <= current_hour < 18:
                time_preference = "afternoon"
            else:
                time_preference = "evening"
            
            profile.preferred_interaction_time = time_preference
            profile.save()
            
        except Exception as e:
            print(f"⚠️ Error updating interaction patterns: {e}")
    
    def _calculate_weekly_frequency(self, user_id: str) -> int:
        """Calculate user's weekly session frequency"""
        try:
            # This would typically query session logs
            # For now, return a placeholder
            return 3  # Default 3 sessions per week
        except Exception:
            return 1
    
    def get_behavioral_insights(self, user_id: str) -> Dict[str, Any]:
        """Get behavioral insights for user"""
        try:
            profile = self.get_user_behavior_profile(user_id)
            if not profile:
                return {"error": "No behavioral profile found"}
            
            insights = {
                "decision_making_style": profile.decision_making_style,
                "risk_tolerance": profile.preferences.risk_tolerance if profile.preferences else "moderate",
                "consistency_score": profile.consistency_score,
                "learning_speed": profile.learning_speed,
                "primary_goals": profile.primary_goals,
                "session_frequency": profile.session_frequency,
                "preferred_time": profile.preferred_interaction_time,
                "decision_patterns_count": len(profile.decision_patterns),
                "market_behaviors_count": len(profile.market_behaviors)
            }
            
            return insights
            
        except Exception as e:
            return {"error": str(e)}

# Global behavioral memory instance
behavioral_memory = BehavioralMemory()
