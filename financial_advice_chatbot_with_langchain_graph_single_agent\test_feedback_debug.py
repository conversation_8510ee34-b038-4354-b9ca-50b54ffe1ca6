#!/usr/bin/env python3
"""
Debug test for feedback system
"""

def test_feedback_storage_and_retrieval():
    """Test that feedback is stored and can be retrieved"""
    print("🔍 DEBUGGING FEEDBACK STORAGE AND RETRIEVAL")
    print("=" * 60)
    
    try:
        from memory.enhanced_memory import memory
        from graphs.enhanced_workflow import EnhancedFinancialWorkflow
        
        # Test user
        test_user_id = "debug_feedback_user"
        test_query = "What should I invest in?"
        test_response = "I recommend starting with a diversified portfolio of low-cost index funds."
        test_feedback = "This response was too vague and didn't consider my specific situation"
        
        print(f"1. Testing with user: {test_user_id}")
        
        # Clear any existing conversation
        memory.clear_conversation_history(test_user_id)
        print("2. Cleared conversation history")
        
        # Store interaction with feedback
        print("3. Storing interaction with feedback...")
        memory.store_interaction_with_feedback(test_user_id, test_query, test_response, test_feedback, 2)
        
        # Get conversation history directly
        print("4. Retrieving conversation history...")
        conversation = memory.get_conversation_history(test_user_id)
        print(f"   Conversation length: {len(conversation) if conversation else 0} characters")
        
        if conversation:
            print(f"   First 500 chars: {conversation[:500]}")
            print(f"   Contains FEEDBACK_ENTRY: {'[FEEDBACK_ENTRY]' in conversation}")
            print(f"   Contains FEEDBACK: {'[FEEDBACK]' in conversation}")
            print(f"   Contains test feedback: {test_feedback in conversation}")
        else:
            print("   ❌ No conversation history found!")
            return False
        
        # Test workflow retrieval
        print("5. Testing workflow feedback retrieval...")
        workflow = EnhancedFinancialWorkflow()
        
        state = {
            "user_id": test_user_id,
            "user_input": "What about bonds?",
            "vector_context": "",
            "comprehensive_context": {}
        }
        
        # This should trigger the debug prints
        updated_state = workflow.retrieve_context(state)
        vector_context = updated_state.get("vector_context", "")
        
        print(f"6. Vector context length: {len(vector_context)}")
        if "PREVIOUS USER FEEDBACK" in vector_context:
            print("   ✅ Feedback found in vector context!")
            print(f"   Feedback context: {vector_context[-500:]}")
            return True
        else:
            print("   ❌ Feedback not found in vector context")
            print(f"   Vector context: {vector_context[:300]}...")
            return False
            
    except Exception as e:
        print(f"❌ Error in debug test: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_simple_feedback_storage():
    """Test simple feedback storage"""
    print("\n🔍 TESTING SIMPLE FEEDBACK STORAGE")
    print("=" * 60)
    
    try:
        from memory.enhanced_memory import memory
        
        test_user_id = "simple_test_user"
        
        # Clear history
        memory.clear_conversation_history(test_user_id)
        
        # Store simple feedback
        memory.store_feedback(test_user_id, "Test query", "Test response", "Test feedback", 2)
        
        # Check conversation history
        conversation = memory.get_conversation_history(test_user_id)
        
        if conversation and "[FEEDBACK_ENTRY]" in conversation:
            print("✅ Simple feedback storage working!")
            return True
        else:
            print("❌ Simple feedback storage not working")
            print(f"Conversation: {conversation}")
            return False
            
    except Exception as e:
        print(f"❌ Error in simple test: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🧪 FEEDBACK SYSTEM DEBUG TEST")
    print("=" * 70)
    
    test1 = test_simple_feedback_storage()
    test2 = test_feedback_storage_and_retrieval()
    
    print("\n" + "=" * 70)
    print("📊 DEBUG TEST RESULTS")
    print("=" * 70)
    
    print(f"1. Simple Storage Test: {'✅ PASSED' if test1 else '❌ FAILED'}")
    print(f"2. Full Retrieval Test: {'✅ PASSED' if test2 else '❌ FAILED'}")
    
    if test1 and test2:
        print("\n🎉 FEEDBACK SYSTEM IS WORKING!")
        print("The issue might be in the UI or user session management.")
    else:
        print("\n⚠️ FEEDBACK SYSTEM HAS ISSUES!")
        print("Check the debug output above for details.")
