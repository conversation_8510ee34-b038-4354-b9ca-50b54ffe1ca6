"""
Short-Term Memory System for Multi-Agent Architecture
Handles session-based memory and conversation history
"""

import asyncio
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
import json

logger = logging.getLogger(__name__)

class ShortTermMemory:
    """Short-term memory system using Redis for session management"""
    
    def __init__(self):
        self.redis_client = None
        self.is_initialized = False
        self.session_timeout = 3600  # 1 hour
        self.in_memory_storage = {}  # Fallback storage

    async def initialize(self):
        """Initialize Redis connection"""
        try:
            import redis
            from config.dynamic_config import get_database_config

            config = get_database_config()
            self.redis_client = redis.from_url(config.redis_url, decode_responses=True, socket_connect_timeout=2)

            # Test connection
            self.redis_client.ping()

            self.is_initialized = True
            logger.info("✅ Short-term memory initialized with Redis")

        except Exception as e:
            logger.warning(f"⚠️ Redis not available, using in-memory storage: {e}")
            self.redis_client = None
            self.is_initialized = True  # Still mark as initialized, just using fallback
    
    async def get_context(self, user_id: str, query: str) -> Dict[str, Any]:
        """Get context for user query"""
        try:
            context = {
                'user_profile': await self.get_user_profile(user_id),
                'conversation_history': await self.get_conversation_history(user_id),
                'feedback_history': await self.get_feedback_history(user_id),
                'session_data': await self.get_session_data(user_id)
            }
            
            return context
            
        except Exception as e:
            logger.error(f"Failed to get context: {e}")
            return {}
    
    async def add_conversation(self, user_id: str, role: str, message: str) -> bool:
        """Add conversation message"""
        try:
            if not self.is_initialized:
                return False
            
            conversation_key = f"conversation:{user_id}"
            
            # Create message entry
            message_data = {
                'role': role,
                'content': message,
                'timestamp': datetime.now().isoformat()
            }
            
            # Add to conversation list
            self.redis_client.lpush(conversation_key, json.dumps(message_data))
            
            # Keep only last 50 messages
            self.redis_client.ltrim(conversation_key, 0, 49)
            
            # Set expiration
            self.redis_client.expire(conversation_key, self.session_timeout)
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to add conversation: {e}")
            return False
    
    async def get_conversation_history(self, user_id: str, limit: int = 20) -> List[Dict[str, Any]]:
        """Get conversation history"""
        try:
            if not self.is_initialized:
                return []
            
            conversation_key = f"conversation:{user_id}"
            messages = self.redis_client.lrange(conversation_key, 0, limit - 1)
            
            history = []
            for msg in messages:
                try:
                    message_data = json.loads(msg)
                    history.append(message_data)
                except json.JSONDecodeError:
                    continue
            
            # Return in chronological order (oldest first)
            return list(reversed(history))
            
        except Exception as e:
            logger.error(f"Failed to get conversation history: {e}")
            return []
    
    async def get_feedback_history(self, user_id: str, limit: int = 10) -> List[Dict[str, Any]]:
        """Get user feedback history"""
        try:
            history = await self.get_conversation_history(user_id, limit * 2)
            feedback_history = []
            
            for msg in history:
                content = msg.get('content', '')
                if '[FEEDBACK]' in content or 'FEEDBACK' in content:
                    feedback_history.append(msg)
                    if len(feedback_history) >= limit:
                        break
            
            return feedback_history
            
        except Exception as e:
            logger.error(f"Failed to get feedback history: {e}")
            return []
    
    async def save_user_profile(self, user_id: str, profile_data: Dict[str, Any]) -> bool:
        """Save user profile"""
        try:
            if not self.is_initialized:
                return False

            profile_key = f"profile:{user_id}"

            # Add timestamp
            profile_data['updated_at'] = datetime.now().isoformat()

            # Use in-memory storage if Redis not available
            if not self.redis_client:
                self.in_memory_storage[profile_key] = profile_data.copy()
                return True

            # Store profile in Redis
            self.redis_client.hset(profile_key, mapping={
                k: json.dumps(v) if isinstance(v, (dict, list)) else str(v)
                for k, v in profile_data.items()
            })

            # Set expiration (longer for profiles)
            self.redis_client.expire(profile_key, self.session_timeout * 24)  # 24 hours

            return True

        except Exception as e:
            logger.error(f"Failed to save user profile: {e}")
            # Fallback to in-memory
            profile_key = f"profile:{user_id}"
            self.in_memory_storage[profile_key] = profile_data.copy()
            return True
    
    async def get_user_profile(self, user_id: str) -> Dict[str, Any]:
        """Get user profile"""
        try:
            if not self.is_initialized:
                return {}

            # Use in-memory storage if Redis not available
            if not self.redis_client:
                profile_key = f"profile:{user_id}"
                return self.in_memory_storage.get(profile_key, {})

            profile_key = f"profile:{user_id}"
            profile_data = self.redis_client.hgetall(profile_key)

            if not profile_data:
                return {}

            # Parse JSON values
            parsed_profile = {}
            for key, value in profile_data.items():
                try:
                    parsed_profile[key] = json.loads(value)
                except json.JSONDecodeError:
                    parsed_profile[key] = value

            return parsed_profile
            
        except Exception as e:
            logger.error(f"Failed to get user profile: {e}")
            return {}
    
    async def get_session_data(self, user_id: str) -> Dict[str, Any]:
        """Get session-specific data"""
        try:
            if not self.is_initialized:
                return {}
            
            session_key = f"session:{user_id}"
            session_data = self.redis_client.hgetall(session_key)
            
            if not session_data:
                # Initialize new session
                session_data = {
                    'session_start': datetime.now().isoformat(),
                    'interaction_count': '0',
                    'last_activity': datetime.now().isoformat()
                }
                self.redis_client.hset(session_key, mapping=session_data)
                self.redis_client.expire(session_key, self.session_timeout)
            
            # Parse values
            parsed_session = {}
            for key, value in session_data.items():
                try:
                    parsed_session[key] = json.loads(value)
                except json.JSONDecodeError:
                    parsed_session[key] = value
            
            return parsed_session
            
        except Exception as e:
            logger.error(f"Failed to get session data: {e}")
            return {}
    
    async def update_session_activity(self, user_id: str) -> bool:
        """Update session activity"""
        try:
            if not self.is_initialized:
                return False
            
            session_key = f"session:{user_id}"
            
            # Update last activity and increment interaction count
            self.redis_client.hset(session_key, mapping={
                'last_activity': datetime.now().isoformat()
            })
            self.redis_client.hincrby(session_key, 'interaction_count', 1)
            self.redis_client.expire(session_key, self.session_timeout)
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to update session activity: {e}")
            return False
    
    async def store_feedback(self, user_id: str, query: str, response: str, feedback_data: Dict[str, Any]) -> bool:
        """Store user feedback"""
        try:
            # Format feedback for LLM consumption
            feedback_type = feedback_data.get('type', 'general')
            rating = feedback_data.get('rating', 3)
            comment = feedback_data.get('comment', '')
            
            if rating >= 4:
                feedback_label = "POSITIVE_FEEDBACK"
                instruction = "User liked this response - continue similar approach"
            elif rating <= 2:
                feedback_label = "NEGATIVE_FEEDBACK"
                instruction = "User disliked this response - improve by being more detailed"
            else:
                feedback_label = "NEUTRAL_FEEDBACK"
                instruction = "User was neutral - maintain approach with minor improvements"
            
            feedback_message = f"[{feedback_label}] Query: '{query}' | Rating: {rating}/5"
            if comment:
                feedback_message += f" | Comment: '{comment}'"
            feedback_message += f" | INSTRUCTION: {instruction}"
            
            # Store as conversation message
            await self.add_conversation(user_id, "SYSTEM_FEEDBACK", feedback_message)
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to store feedback: {e}")
            return False
    
    def update_config(self, config):
        """Update configuration"""
        try:
            if hasattr(config, 'redis_url'):
                # Reinitialize with new config
                asyncio.create_task(self.initialize())
            logger.info("Updated short-term memory configuration")
        except Exception as e:
            logger.error(f"Failed to update config: {e}")
