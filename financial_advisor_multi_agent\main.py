#!/usr/bin/env python3
"""
PennyWise Multi-Agent Financial Advisor
Main application entry point
"""

import os
import sys
import asyncio
import logging
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def setup_environment():
    """Setup environment variables and configuration"""
    from dotenv import load_dotenv
    
    # Load environment variables
    load_dotenv()
    
    # Set default values if not provided
    os.environ.setdefault('REDIS_URL', 'redis://localhost:6379')
    os.environ.setdefault('MONGODB_URL', 'mongodb://localhost:27017')
    os.environ.setdefault('QDRANT_URL', 'http://localhost:6333')
    os.environ.setdefault('GROQ_API_KEY', '')
    
    logger.info("Environment setup completed")

async def initialize_mcp_servers():
    """Initialize MCP servers for dynamic tool management"""
    try:
        # Initialize dynamic MCP manager
        from mcp.dynamic_server_manager import dynamic_mcp_manager
        
        logger.info("Dynamic MCP server manager initialized")
        return {"dynamic_manager": dynamic_mcp_manager}
        
    except Exception as e:
        logger.error(f"Failed to initialize MCP servers: {e}")
        return {}

async def initialize_agents():
    """Initialize all agents in the multi-agent system"""
    try:
        from agents.orchestrator_agent import OrchestratorAgent
        from agents.memory_agent import MemoryAgent
        from agents.planning_agent import PlanningAgent
        from agents.reasoning_agent import ReasoningAgent
        from agents.financial_agent import FinancialAgent
        from agents.geography_agent import GeographyAgent
        from agents.market_agent import MarketAgent
        from agents.guardrails_agent import GuardrailsAgent
        from agents.feedback_agent import FeedbackAgent
        from agents.response_agent import ResponseAgent
        
        # Initialize agents
        agents = {
            'orchestrator': OrchestratorAgent(),
            'memory': MemoryAgent(),
            'planning': PlanningAgent(),
            'reasoning': ReasoningAgent(),
            'financial': FinancialAgent(),
            'geography': GeographyAgent(),
            'market': MarketAgent(),
            'guardrails': GuardrailsAgent(),
            'feedback': FeedbackAgent(),
            'response': ResponseAgent()
        }
        
        # Initialize each agent
        for name, agent in agents.items():
            await agent.initialize()
            logger.info(f"Agent '{name}' initialized successfully")
        
        # Setup inter-agent communication
        orchestrator = agents['orchestrator']
        for name, agent in agents.items():
            if name != 'orchestrator':
                orchestrator.register_agent(name, agent)
        
        logger.info("All agents initialized and registered")
        return agents
        
    except Exception as e:
        logger.error(f"Failed to initialize agents: {e}")
        return {}

def run_streamlit_app():
    """Run the Streamlit frontend application"""
    try:
        import subprocess
        import sys
        import os

        # Set environment variable to indicate multi-agent mode
        os.environ['MULTI_AGENT_MODE'] = 'true'

        # Run Streamlit app
        cmd = [
            sys.executable, "-m", "streamlit", "run",
            "multi_agent_ui.py",
            "--server.port=8501",
            "--server.address=0.0.0.0",
            "--server.headless=true"
        ]

        logger.info("Starting Multi-Agent Streamlit application...")
        subprocess.run(cmd)

    except Exception as e:
        logger.error(f"Failed to start Streamlit app: {e}")

async def main():
    """Main application entry point"""
    logger.info("🤖 Starting PennyWise Multi-Agent Financial Advisor")
    
    try:
        # Setup environment
        setup_environment()
        
        # Initialize MCP servers
        logger.info("Initializing MCP servers...")
        mcp_servers = await initialize_mcp_servers()
        
        # Initialize agents
        logger.info("Initializing multi-agent system...")
        agents = await initialize_agents()
        
        if not agents:
            logger.error("Failed to initialize agents. Exiting.")
            return
        
        # Store agents globally for access by Streamlit
        from config.dynamic_config import config_manager
        config_manager.set_agents(agents)
        config_manager.set_mcp_servers(mcp_servers)
        
        logger.info("✅ Multi-agent system initialized successfully")
        logger.info("🚀 Starting frontend application...")
        
        # Run Streamlit app (blocking)
        run_streamlit_app()
        
    except KeyboardInterrupt:
        logger.info("Application interrupted by user")
    except Exception as e:
        logger.error(f"Application error: {e}")
    finally:
        logger.info("Shutting down application...")

if __name__ == "__main__":
    # Check if running in async context (for development)
    try:
        # If already in async context, just run main
        loop = asyncio.get_running_loop()
        logger.warning("Already in async context, running synchronously")
        # For Streamlit compatibility, run without async
        setup_environment()
        run_streamlit_app()
    except RuntimeError:
        # Not in async context, run normally
        asyncio.run(main())


