#!/usr/bin/env python3
"""
Test all fixes including syntax errors
"""

def test_syntax_errors():
    """Test that all files have valid syntax"""
    print("🔍 TESTING SYNTAX ERRORS")
    print("=" * 50)
    
    files_to_test = [
        "main.py",
        "feedback/feedback_system.py",
        "memory/enhanced_memory.py", 
        "graphs/enhanced_workflow.py"
    ]
    
    all_valid = True
    
    for file_path in files_to_test:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Try to compile the code
            compile(content, file_path, 'exec')
            print(f"✅ {file_path} - Syntax OK")
            
        except SyntaxError as e:
            print(f"❌ {file_path} - Syntax Error: {e}")
            print(f"   Line {e.lineno}: {e.text}")
            all_valid = False
        except FileNotFoundError:
            print(f"⚠️ {file_path} - File not found")
        except Exception as e:
            print(f"❌ {file_path} - Error: {e}")
            all_valid = False
    
    return all_valid

def test_feedback_system_fixes():
    """Test feedback system specific fixes"""
    print("\n🔍 TESTING FEEDBACK SYSTEM FIXES")
    print("=" * 50)
    
    try:
        with open("feedback/feedback_system.py", "r") as f:
            content = f.read()
        
        # Check for the fixed string literal
        if "User disliked this response - improve by being more detailed and careful'}" in content:
            print("✅ String literal termination fixed")
        else:
            print("❌ String literal termination not fixed")
            return False
        
        # Check for correct store_feedback call
        if "memory.store_feedback(user_id, query, response," in content:
            print("✅ store_feedback method call fixed")
        else:
            print("❌ store_feedback method call not fixed")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing feedback system: {e}")
        return False

def test_enhanced_feedback_storage():
    """Test enhanced feedback storage in main.py"""
    print("\n🔍 TESTING ENHANCED FEEDBACK STORAGE")
    print("=" * 50)
    
    try:
        with open("main.py", "r") as f:
            content = f.read()
        
        # Check for enhanced feedback storage calls
        checks = [
            ("Positive feedback storage", "memory.store_feedback(st.session_state.user_id, prompt, response, feedback_text, 4)"),
            ("Negative feedback storage", "memory.store_feedback(st.session_state.user_id, prompt, response, feedback_text, 2)"),
            ("Comment feedback storage", "memory.store_feedback(st.session_state.user_id, prompt, response, feedback_text, 3)"),
            ("Interaction storage", "memory.store_interaction_with_feedback(st.session_state.user_id, prompt, response)"),
            ("Close button removed", "Close Planning Steps" not in content)
        ]
        
        all_passed = True
        for check_name, check_condition in checks:
            if isinstance(check_condition, str):
                if check_condition in content:
                    print(f"✅ {check_name}")
                else:
                    print(f"❌ {check_name}")
                    all_passed = False
            else:  # Boolean condition
                if check_condition:
                    print(f"✅ {check_name}")
                else:
                    print(f"❌ {check_name}")
                    all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ Error testing enhanced feedback storage: {e}")
        return False

def test_thinking_process_variables():
    """Test thinking process variable fixes"""
    print("\n🔍 TESTING THINKING PROCESS VARIABLES")
    print("=" * 50)
    
    try:
        with open("main.py", "r") as f:
            content = f.read()
        
        variables = [
            "analysis_type = thinking_data.get('analysis_type'",
            "vector_context = thinking_data.get('vector_context'",
            "financial_plan = thinking_data.get('financial_plan'",
            "processing_time = thinking_data.get('processing_time'"
        ]
        
        all_found = True
        for var in variables:
            if var in content:
                print(f"✅ {var[:30]}... - Fixed")
            else:
                print(f"❌ {var[:30]}... - Not found")
                all_found = False
        
        return all_found
        
    except Exception as e:
        print(f"❌ Error testing thinking process variables: {e}")
        return False

def main():
    """Run all tests"""
    print("🧪 COMPREHENSIVE FIX TESTING")
    print("=" * 70)
    
    # Run all tests
    syntax_test = test_syntax_errors()
    feedback_test = test_feedback_system_fixes()
    storage_test = test_enhanced_feedback_storage()
    variables_test = test_thinking_process_variables()
    
    print("\n" + "=" * 70)
    print("📊 COMPREHENSIVE TEST RESULTS")
    print("=" * 70)
    
    print(f"1. Syntax Errors Fixed: {'✅ PASSED' if syntax_test else '❌ FAILED'}")
    print(f"2. Feedback System Fixed: {'✅ PASSED' if feedback_test else '❌ FAILED'}")
    print(f"3. Enhanced Storage Fixed: {'✅ PASSED' if storage_test else '❌ FAILED'}")
    print(f"4. Variable Errors Fixed: {'✅ PASSED' if variables_test else '❌ FAILED'}")
    
    overall_success = all([syntax_test, feedback_test, storage_test, variables_test])
    
    if overall_success:
        print("\n🎉 ALL FIXES SUCCESSFULLY IMPLEMENTED!")
        print("\n🔧 WHAT'S BEEN FIXED:")
        print("✅ Syntax error in feedback_system.py (unterminated string)")
        print("✅ Method signature mismatch in store_feedback call")
        print("✅ Enhanced feedback storage with query, response, feedback")
        print("✅ All thinking process variable errors (NameError)")
        print("✅ Removed non-working close planning button")
        print("✅ Bot now remembers feedback in structured format")
        
        print("\n🚀 READY FOR FINAL TESTING!")
        print("📋 TEST SEQUENCE:")
        print("1. streamlit run main.py")
        print("2. Ask: 'What should I invest in?'")
        print("3. Give feedback: 👎 'Too vague for my situation'")
        print("4. Ask: 'What feedback did I give you?'")
        print("5. Bot should remember your exact feedback!")
        print("6. Click 'Show My Thinking' - no more errors!")
        
        print("\n💡 EXPECTED BEHAVIOR:")
        print("- No syntax errors on startup")
        print("- Bot remembers feedback with original query and response")
        print("- Thinking process displays without NameError")
        print("- Clean UI without broken buttons")
        print("- Feedback influences future responses")
        
    else:
        print("\n⚠️ Some fixes failed. Please review the errors above.")
    
    return overall_success

if __name__ == "__main__":
    main()
