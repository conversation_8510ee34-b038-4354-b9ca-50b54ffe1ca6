#!/usr/bin/env python3
"""
Generate a detailed visualization of the LangGraph workflow
Creates a PNG file showing the complete workflow graph
"""

import os
import json
from typing import Dict, Any, List, Optional
import importlib
import inspect

# Import the workflow modules
from graphs.enhanced_workflow import enhanced_workflow
from graphs.planning_system import planning_system
from graphs.reasoning_system import FinancialReasoningSystem
from memory.enhanced_memory import memory
from guardrails.unified_guardrails import financial_guardrails

# Graphviz for visualization
try:
    import graphviz
except ImportError:
    print("Installing graphviz package...")
    import subprocess
    subprocess.check_call(["pip", "install", "graphviz"])
    import graphviz

def get_node_details(module) -> Dict[str, Any]:
    """Extract node details from a module or class"""
    details = {}
    
    # Get all functions and methods
    if inspect.ismodule(module):
        functions = inspect.getmembers(module, inspect.isfunction)
        for name, func in functions:
            if not name.startswith('_'):  # Skip private methods
                details[name] = {
                    'doc': inspect.getdoc(func),
                    'params': str(inspect.signature(func)),
                    'type': 'function'
                }
    
    # Get class methods if it's a class
    elif inspect.isclass(module):
        methods = inspect.getmembers(module, inspect.ismethod)
        for name, method in methods:
            if not name.startswith('_'):  # Skip private methods
                details[name] = {
                    'doc': inspect.getdoc(method),
                    'params': str(inspect.signature(method)),
                    'type': 'method'
                }
    
    return details

def create_workflow_diagram():
    """Create a detailed diagram of the entire workflow"""
    # Create a new directed graph
    dot = graphviz.Digraph(
        'PennyWise_Workflow', 
        comment='PennyWise Financial Advisor Workflow',
        format='png',
        engine='dot'
    )
    
    # Set graph attributes
    dot.attr(
        rankdir='TB',  # Top to bottom layout
        size='11,8',
        ratio='fill',
        fontname='Arial',
        fontsize='16',
        label='PennyWise Financial Advisor Workflow',
        labelloc='t',
        bgcolor='white'
    )
    
    # Node styling
    dot.attr('node', 
        shape='box', 
        style='filled,rounded',
        fontname='Arial',
        fontsize='12',
        margin='0.2,0.1'
    )
    
    # Edge styling
    dot.attr('edge', 
        fontname='Arial',
        fontsize='10',
        penwidth='1.2'
    )
    
    # Create subgraph for enhanced workflow
    with dot.subgraph(name='cluster_enhanced_workflow') as c:
        c.attr(label='Enhanced Workflow', style='filled', color='lightblue', fillcolor='aliceblue')
        
        # Add nodes for each step in the workflow
        c.node('user_input', 'User Input', fillcolor='lightgreen')
        c.node('input_validation', 'Input Validation\n(Guardrails)', fillcolor='lightyellow')
        c.node('memory_retrieval', 'Memory Retrieval\n(Context)', fillcolor='lightcyan')
        c.node('query_analysis', 'Query Analysis\n(Type Detection)', fillcolor='lightcyan')
        c.node('tool_selection', 'Tool Selection\n(Financial Tools)', fillcolor='lightcyan')
        c.node('planning', 'Financial Planning\n(Step-by-Step)', fillcolor='lightpink')
        c.node('reasoning', 'Reasoning System\n(Logic & Analysis)', fillcolor='lightpink')
        c.node('response_generation', 'Response Generation\n(LLM Synthesis)', fillcolor='lightyellow')
        c.node('response_validation', 'Response Validation\n(Guardrails)', fillcolor='lightyellow')
        c.node('memory_update', 'Memory Update\n(Store Interaction)', fillcolor='lightcyan')
        c.node('final_response', 'Final Response', fillcolor='lightgreen')
        
        # Add edges to show flow
        c.edge('user_input', 'input_validation')
        c.edge('input_validation', 'memory_retrieval')
        c.edge('memory_retrieval', 'query_analysis')
        c.edge('query_analysis', 'tool_selection')
        c.edge('tool_selection', 'planning', label='if planning needed')
        c.edge('tool_selection', 'reasoning', label='if reasoning needed')
        c.edge('planning', 'response_generation')
        c.edge('reasoning', 'response_generation')
        c.edge('tool_selection', 'response_generation', label='if direct response')
        c.edge('response_generation', 'response_validation')
        c.edge('response_validation', 'memory_update')
        c.edge('memory_update', 'final_response')
    
    # Create subgraph for memory systems
    with dot.subgraph(name='cluster_memory') as c:
        c.attr(label='Memory Systems', style='filled', color='lightblue', fillcolor='azure')
        
        # Add nodes for each memory type
        c.node('short_term', 'Short-Term Memory\n(Redis)', fillcolor='lightcyan')
        c.node('semantic', 'Semantic Memory\n(Qdrant Vector DB)', fillcolor='lightcyan')
        c.node('behavioral', 'Behavioral Memory\n(MongoDB)', fillcolor='lightcyan')
        c.node('episodic', 'Episodic Memory\n(MongoDB)', fillcolor='lightcyan')
        c.node('long_term', 'Long-Term Memory\n(Unified Storage)', fillcolor='lightcyan')
        
        # Add edges between memory systems
        c.edge('short_term', 'long_term', style='dashed')
        c.edge('semantic', 'long_term', style='dashed')
        c.edge('behavioral', 'long_term', style='dashed')
        c.edge('episodic', 'long_term', style='dashed')
        
        # Connect to main workflow
        dot.edge('memory_retrieval', 'short_term', style='dotted', dir='both')
        dot.edge('memory_retrieval', 'semantic', style='dotted', dir='both')
        dot.edge('memory_retrieval', 'behavioral', style='dotted', dir='both')
        dot.edge('memory_retrieval', 'episodic', style='dotted', dir='both')
        dot.edge('memory_update', 'long_term', style='dotted', dir='both')
    
    # Create subgraph for planning system
    with dot.subgraph(name='cluster_planning') as c:
        c.attr(label='Planning System', style='filled', color='lightblue', fillcolor='mistyrose')
        
        # Add nodes for planning steps
        c.node('plan_creation', 'Plan Creation\n(Step Generation)', fillcolor='lightpink')
        c.node('plan_execution', 'Plan Execution\n(Step-by-Step)', fillcolor='lightpink')
        c.node('plan_evaluation', 'Plan Evaluation\n(Quality Check)', fillcolor='lightpink')
        c.node('plan_refinement', 'Plan Refinement\n(Improvements)', fillcolor='lightpink')
        
        # Add edges between planning steps
        c.edge('plan_creation', 'plan_execution')
        c.edge('plan_execution', 'plan_evaluation')
        c.edge('plan_evaluation', 'plan_refinement')
        c.edge('plan_refinement', 'plan_execution', label='if needed', style='dashed')
        
        # Connect to main workflow
        dot.edge('planning', 'plan_creation', style='dotted')
        dot.edge('plan_evaluation', 'response_generation', style='dotted')
    
    # Create subgraph for reasoning system
    with dot.subgraph(name='cluster_reasoning') as c:
        c.attr(label='Reasoning System', style='filled', color='lightblue', fillcolor='lavenderblush')
        
        # Add nodes for reasoning steps
        c.node('reasoning_type', 'Reasoning Type\n(Selection)', fillcolor='lightpink')
        c.node('plan_and_execute', 'Plan-and-Execute\n(Systematic)', fillcolor='lightpink')
        c.node('reactive', 'ReAct Reasoning\n(Dynamic)', fillcolor='lightpink')
        c.node('hybrid', 'Hybrid Reasoning\n(Combined)', fillcolor='lightpink')
        
        # Add edges between reasoning steps
        c.edge('reasoning_type', 'plan_and_execute', label='if planning')
        c.edge('reasoning_type', 'reactive', label='if reactive')
        c.edge('reasoning_type', 'hybrid', label='if complex')
        
        # Connect to main workflow
        dot.edge('reasoning', 'reasoning_type', style='dotted')
        dot.edge('plan_and_execute', 'response_generation', style='dotted')
        dot.edge('reactive', 'response_generation', style='dotted')
        dot.edge('hybrid', 'response_generation', style='dotted')
    
    # Create subgraph for guardrails
    with dot.subgraph(name='cluster_guardrails') as c:
        c.attr(label='Guardrails System', style='filled', color='lightblue', fillcolor='papayawhip')
        
        # Add nodes for guardrail components
        c.node('input_validation_guardrails', 'Input Validation\n(Safety Checks)', fillcolor='lightyellow')
        c.node('relevance_classifier', 'Relevance Classifier\n(Topic Detection)', fillcolor='lightyellow')
        c.node('response_validation_guardrails', 'Response Validation\n(Safety & Quality)', fillcolor='lightyellow')
        c.node('pii_detection', 'PII Detection\n(Data Protection)', fillcolor='lightyellow')
        
        # Add edges between guardrail components
        c.edge('input_validation_guardrails', 'relevance_classifier')
        c.edge('response_validation_guardrails', 'pii_detection')
        
        # Connect to main workflow
        dot.edge('input_validation', 'input_validation_guardrails', style='dotted', dir='both')
        dot.edge('response_validation', 'response_validation_guardrails', style='dotted', dir='both')
    
    # Create subgraph for tools
    with dot.subgraph(name='cluster_tools') as c:
        c.attr(label='Financial Tools', style='filled', color='lightblue', fillcolor='honeydew')
        
        # Add nodes for tools
        c.node('stock_data', 'Stock Data\n(Real-Time)', fillcolor='palegreen')
        c.node('economic_indicators', 'Economic Indicators\n(Market Data)', fillcolor='palegreen')
        c.node('country_data', 'Country Data\n(Geographic)', fillcolor='palegreen')
        c.node('market_sentiment', 'Market Sentiment\n(Analysis)', fillcolor='palegreen')
        c.node('sector_performance', 'Sector Performance\n(Industry Data)', fillcolor='palegreen')
        
        # Connect to main workflow
        dot.edge('tool_selection', 'stock_data', style='dotted', dir='both')
        dot.edge('tool_selection', 'economic_indicators', style='dotted', dir='both')
        dot.edge('tool_selection', 'country_data', style='dotted', dir='both')
        dot.edge('tool_selection', 'market_sentiment', style='dotted', dir='both')
        dot.edge('tool_selection', 'sector_performance', style='dotted', dir='both')
    
    # Save the diagram
    filename = 'pennywise_workflow_diagram'
    dot.render(filename, cleanup=True)
    print(f"✅ Workflow diagram created: {filename}.png")
    
    return f"{filename}.png"

if __name__ == "__main__":
    diagram_path = create_workflow_diagram()
    print(f"Diagram saved to: {diagram_path}")
