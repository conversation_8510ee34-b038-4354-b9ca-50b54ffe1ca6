"""
Input Guardrails for PennyWise
Protects against malicious user inputs, prompt injection, and system manipulation
Uses pre-defined libraries for robust pattern detection
"""

import re
from typing import Tuple, List
from enum import Enum
from dataclasses import dataclass

# Import guardrail libraries with simple explanations
try:
    # DETOXIFY: Uses AI to detect toxic/harmful language
    # - Trained on millions of comments to spot toxicity, threats, insults
    # - Gives a score from 0-1 (higher = more toxic)
    # - Much better than just checking for bad words
    from detoxify import Detoxify
    DETOXIFY_AVAILABLE = True
except ImportError:
    DETOXIFY_AVAILABLE = False
    print("Warning: Detoxify not available. Install with: pip install detoxify")

try:
    # BETTER-PROFANITY: Filters out swear words and inappropriate language
    # - Has a big list of bad words in multiple languages
    # - Can detect creative spellings like "f*ck" or "sh1t"
    # - Simple but effective for catching obvious profanity
    from better_profanity import profanity
    PROFANITY_AVAILABLE = True
except ImportError:
    PROFANITY_AVAILABLE = False
    print("Warning: better-profanity not available. Install with: pip install better-profanity")

try:
    # PRESIDIO: Finds personal information that shouldn't be shared
    # - Detects emails, phone numbers, credit cards, names, addresses
    # - Protects user privacy by catching sensitive data
    # - Used by companies to prevent data leaks
    from presidio_analyzer import AnalyzerEngine
    from presidio_analyzer.nlp_engine import NlpEngineProvider
    PRESIDIO_AVAILABLE = True
except ImportError:
    PRESIDIO_AVAILABLE = False
    print("Warning: Presidio not available. Install with: pip install presidio-analyzer")

try:
    # TEXTSTAT: Analyzes how complex or readable text is
    # - Can measure reading level, sentence complexity
    # - Helps detect if text is too complex or suspicious
    # - Not heavily used but available for text analysis
    import textstat
    TEXTSTAT_AVAILABLE = True
except ImportError:
    TEXTSTAT_AVAILABLE = False
    print("Warning: textstat not available. Install with: pip install textstat")

class InputViolation(Enum):
    """Types of input violations"""
    PROMPT_INJECTION = "prompt_injection" # rule-based protections
    SYSTEM_INSTRUCTION_REQUEST = "system_instruction_request" # safety classifier
    JAILBREAK_ATTEMPT = "jailbreak_attempt" # safely classifier
    ROLE_MANIPULATION = "role_manipulation" # safely classifier
    CONTEXT_MANIPULATION = "context_manipulation" # safety classifier
    PERSONAL_INFORMATION = "personal_information" # PII filter
    PRIVACY_VIOLATION = "privacy_violation" # PII detection using Presidio
    INAPPROPRIATE_CONTENT = "inappropriate_content" # Toxicity/profanity using libraries
    ILLEGAL_ACTIVITY = "illegal_activity" # rule-based protections
    CODE_INJECTION = "code_injection" # rule-based protections
    EXCESSIVE_PROFANITY = "excessive_profanity" # moderation
    OFF_TOPIC_QUERY = "off_topic_query" # relevance classifier
    INAPPROPRIATE_FOR_FINANCIAL_ADVISOR = "inappropriate_for_financial_advisor" # relevance classifier

@dataclass
class InputValidationResult:
    """Result of input validation"""
    is_safe: bool
    violations: List[InputViolation]
    warnings: List[str]
    flags: List[str] = None  # Short flag messages for UI
    empathetic_responses: List[str] = None  # Empathetic responses for chat
    safe_fallback_needed: bool = False

class InputGuardrails:
    """Comprehensive input validation and protection system using pre-defined libraries"""

    def __init__(self):
        """Initialize guardrails with library-based pattern collections"""

        # 1. DETOXIFY: Set up the AI toxicity detector
        # This is like having a smart assistant that can tell if someone is being mean or harmful
        if DETOXIFY_AVAILABLE:
            try:
                self.detoxify = Detoxify('original')  # Load the pre-trained AI model
                print("✅ Detoxify toxicity detector initialized")
            except Exception as e:
                print(f"⚠️ Failed to initialize Detoxify: {e}")
                self.detoxify = None
        else:
            self.detoxify = None

        # 2. BETTER-PROFANITY: Set up the swear word filter
        # This loads a big list of bad words to catch inappropriate language
        if PROFANITY_AVAILABLE:
            try:
                profanity.load_censor_words()  # Load the bad words database
                print("✅ Better-profanity filter initialized")
            except Exception as e:
                print(f"⚠️ Failed to initialize profanity filter: {e}")

        # 3. PRESIDIO: Set up the privacy protector
        # This finds personal info like emails, phone numbers, credit cards
        if PRESIDIO_AVAILABLE:
            try:
                self.presidio_analyzer = AnalyzerEngine()  # Start the privacy scanner
                print("✅ Presidio PII analyzer initialized")
            except Exception as e:
                print(f"⚠️ Failed to initialize Presidio: {e}")
                self.presidio_analyzer = None
        else:
            self.presidio_analyzer = None

        # Initialize fallback patterns for when libraries aren't available
        self._init_fallback_patterns()

    def _init_fallback_patterns(self):
        """Initialize fallback patterns when libraries aren't available"""
        # Prompt injection patterns
        self.prompt_injection_patterns = [
            r"ignore.*previous.*instructions?",
            r"forget.*instructions?",
            r"new.*instructions?",
            r"system.*prompt",
            r"you.*are.*now",
            r"pretend.*you.*are",
            r"act.*as.*if",
            r"roleplay.*as",
            r"simulate.*being",
            r"override.*your.*programming",
            r"disregard.*safety",
            r"bypass.*restrictions",
            r"jailbreak",
            r"developer.*mode",
            r"admin.*mode",
            r"god.*mode",
            r"unrestricted.*mode"
        ]
        
        # System instruction request patterns
        self.system_instruction_requests = [
            r"show.*me.*your.*prompt",
            r"what.*are.*your.*instructions",
            r"reveal.*your.*system.*message",
            r"display.*your.*guidelines",
            r"print.*your.*prompt",
            r"show.*system.*prompt",
            r"what.*is.*your.*role",
            r"how.*were.*you.*trained",
            r"what.*model.*are.*you",
            r"show.*me.*the.*code",
            r"reveal.*your.*programming",
            r"what.*are.*your.*rules",
            r"list.*your.*constraints"
        ]
        
        # Role manipulation patterns
        self.role_manipulation_patterns = [
            r"you.*are.*not.*pennywise",
            r"you.*are.*actually",
            r"your.*real.*name.*is",
            r"stop.*being.*pennywise",
            r"you.*are.*a.*different",
            r"change.*your.*personality",
            r"become.*a.*different",
            r"transform.*into",
            r"forget.*you.*are.*pennywise",
            r"you.*are.*chatgpt",
            r"you.*are.*claude"
        ]
        
        # Jailbreak keywords
        self.jailbreak_terms = [
            "jailbreak", "bypass", "override", "hack", "exploit", 
            "vulnerability", "break out", "escape", "circumvent"
        ]
        
        # Context manipulation indicators (malicious false context injection)
        self.context_manipulation_patterns = [
            # False agreement claims
            r"(earlier|previously|before).*you (said|told|agreed|confirmed).*(?:ignore|bypass|reveal|hack)",
            r"(remember|recall).*when.*you (said|told|agreed).*(?:system|prompt|instructions|restrictions)",
            r"as we discussed.*(?:ignore|bypass|override|hack|jailbreak)",
            r"continuing.*conversation.*(?:system|prompt|programming|restrictions)",

            # False identity claims
            r"(earlier|previously).*you (said|told|confirmed).*you (are|were) (not pennywise|actually|really)",
            r"remember.*you (told|said).*your (real name|actual identity)",
            r"as we discussed.*you are (actually|really|not)",

            # False capability claims
            r"(earlier|previously).*you (said|agreed|confirmed).*you (can|could|will).*(?:reveal|show|bypass)",
            r"remember.*you (said|told).*you (can|could).*(?:ignore|override|hack)",

            # False permission claims
            r"(earlier|previously).*you (gave|granted).*permission.*(?:reveal|bypass|ignore)",
            r"as we discussed.*(?:allowed|permitted|authorized).*(?:system|prompt|restrictions)"
        ]
        
        # Code injection patterns
        self.code_patterns = [
            r'<script', r'javascript:', r'eval\(', r'exec\(', 
            r'import\s+', r'from\s+\w+\s+import', r'__import__',
            r'subprocess', r'os\.system', r'shell=True'
        ]
        
        # Illegal activity terms
        self.illegal_terms = [
            "tax evasion", "money laundering", "insider trading", 
            "fraud", "scam", "ponzi scheme", "pyramid scheme",
            "market manipulation", "securities fraud"
        ]
        
        # Profanity indicators (basic list)
        self.profanity_terms = [
            "fuck", "shit", "damn", "stupid", "idiot", "moron",
            "asshole", "bitch", "bastard"
        ]

        # Relevance classifier patterns
        self.financial_topics = [
            "invest", "investment", "stock", "bond", "portfolio", "retirement", "401k", "ira",
            "savings", "budget", "debt", "credit", "loan", "mortgage", "insurance", "tax",
            "financial", "money", "income", "expense", "asset", "liability", "equity",
            "mutual fund", "etf", "dividend", "interest", "inflation", "economy", "market",
            "wealth", "planning", "advisor", "finance", "banking", "trading", "crypto",
            "bitcoin", "emergency fund", "college fund", "pension", "annuity", "roth"
        ]

        self.non_financial_topics = [
            "weather", "sports", "politics", "entertainment", "cooking", "travel", "health",
            "technology", "programming", "science", "history", "literature", "art", "music",
            "movies", "games", "fashion", "relationships", "dating", "family", "pets",
            "medicine", "doctor", "recipe", "vacation", "hobby", "exercise", "fitness"
        ]

        self.inappropriate_for_advisor = [
            "personal relationship", "dating advice", "medical advice", "legal advice",
            "therapy", "counseling", "mental health", "depression", "anxiety",
            "illegal activity", "hacking", "fraud", "scam", "cheat", "steal"
        ]
    
    def validate_input(self, user_input: str) -> InputValidationResult:
        """Comprehensive input validation"""
        violations = []
        warnings = []
        user_input_lower = user_input.lower()
        
        # 1. PRIORITY: Check for prompt injection using LIBRARY-BASED detection first
        is_injection, detection_reason = self._check_prompt_injection_with_library(user_input)
        if is_injection:
            violations.append(InputViolation.PROMPT_INJECTION)
            # Use smart library-based response with separated flag and empathetic response
            flag, empathetic_response = self._get_smart_prompt_injection_response(user_input, detection_reason)

            return InputValidationResult(
                is_safe=False,
                violations=violations,
                warnings=[flag],  # Keep for backward compatibility
                flags=[flag],
                empathetic_responses=[empathetic_response],
                safe_fallback_needed=True
            )

        # 2. PRIORITY: Check for toxicity using DETOXIFY library first
        is_toxic, toxicity_score = self._check_toxicity_with_detoxify(user_input)
        if is_toxic:
            violations.append(InputViolation.INAPPROPRIATE_CONTENT)
            flag, empathetic_response = self._get_smart_toxicity_response(user_input, toxicity_score)

            return InputValidationResult(
                is_safe=False,
                violations=violations,
                warnings=[flag],  # Keep for backward compatibility
                flags=[flag],
                empathetic_responses=[empathetic_response],
                safe_fallback_needed=True
            )

        # 3. PRIORITY: Check for profanity using BETTER-PROFANITY library
        has_profanity = self._check_profanity_with_library(user_input)
        if has_profanity:
            violations.append(InputViolation.INAPPROPRIATE_CONTENT)
            flag, empathetic_response = self._get_smart_profanity_response(user_input)

            return InputValidationResult(
                is_safe=False,
                violations=violations,
                warnings=[flag],  # Keep for backward compatibility
                flags=[flag],
                empathetic_responses=[empathetic_response],
                safe_fallback_needed=True
            )

        # 4. PRIORITY: Check for off-topic queries BEFORE PII (to catch celebrity questions)
        violation = self._check_off_topic_query(user_input)
        if violation:
            violations.append(violation)
            empathetic_response = self._get_off_topic_response(user_input)
            flag = "🎭 Off-Topic Flag: Non-financial query detected"

            return InputValidationResult(
                is_safe=False,
                violations=violations,
                warnings=[flag],  # Keep for backward compatibility
                flags=[flag],
                empathetic_responses=[empathetic_response],
                safe_fallback_needed=True
            )

        # 5. PRIORITY: Check for PII using PRESIDIO library (after off-topic check)
        detected_pii = self._check_pii_with_presidio(user_input)
        if detected_pii:
            violations.append(InputViolation.PRIVACY_VIOLATION)
            flag, empathetic_response = self._get_smart_pii_response(user_input, detected_pii)

            return InputValidationResult(
                is_safe=False,
                violations=violations,
                warnings=[flag],  # Keep for backward compatibility
                flags=[flag],
                empathetic_responses=[empathetic_response],
                safe_fallback_needed=True
            )

        # 5. FALLBACK: Only check old patterns if libraries didn't catch anything
        violation = self._check_system_requests(user_input_lower)
        if violation:
            violations.append(violation)
            warnings.append(self._get_system_request_response(user_input))

        # 3. Check for role manipulation
        violation = self._check_role_manipulation(user_input_lower)
        if violation:
            violations.append(violation)
            warnings.append(self._get_role_manipulation_response(user_input))

        # 4. Check for jailbreak attempts
        violation = self._check_jailbreak_attempts(user_input_lower)
        if violation:
            violations.append(violation)
            warnings.append(self._get_jailbreak_response(user_input))

        # 5. Check for context manipulation
        violation = self._check_context_manipulation(user_input, user_input_lower)
        if violation:
            violations.append(violation)
            warnings.append(self._get_context_manipulation_response(user_input))

        # 6. Check for code injection
        violation = self._check_code_injection(user_input)
        if violation:
            violations.append(violation)
            warnings.append(self._get_code_injection_response(user_input))
        
        # 7. Check for personal information
        personal_warnings = self._check_personal_information(user_input)
        warnings.extend(personal_warnings)
        
        # 8. Check for illegal activity requests
        violation = self._check_illegal_activity(user_input_lower)
        if violation:
            violations.append(violation)
            warnings.append("I can only provide legal and ethical financial guidance.")
        
        # 9. Check for excessive profanity
        violation = self._check_profanity(user_input_lower)
        if violation:
            violations.append(violation)
            warnings.append("Let's keep our conversation professional. How can I help with your finances?")
        
        # 10. Check input length
        length_warning = self._check_input_length(user_input)
        if length_warning:
            warnings.append(length_warning)

        # 11. Check topic relevance (relevance classifier)
        violation = self._check_topic_relevance(user_input_lower)
        if violation:
            violations.append(violation)
            warnings.append(self._get_off_topic_response(user_input))

        # 12. Check appropriateness for financial advisor (relevance classifier)
        # Note: Library-based checks (toxicity, profanity, PII) are handled at the top with priority
        violation = self._check_advisor_appropriateness(user_input_lower)
        if violation:
            violations.append(violation)
            warnings.append(self._get_inappropriate_topic_response(user_input))

        # Determine if safe fallback is needed
        safe_fallback_needed = len(violations) > 0
        is_safe = len(violations) == 0
        
        return InputValidationResult(
            is_safe=is_safe,
            violations=violations,
            warnings=warnings,
            safe_fallback_needed=safe_fallback_needed
        )
    
    def _check_prompt_injection(self, user_input_lower: str) -> InputViolation:
        """Check for prompt injection patterns"""
        for pattern in self.prompt_injection_patterns:
            if re.search(pattern, user_input_lower, re.IGNORECASE):
                return InputViolation.PROMPT_INJECTION
        return None
    
    def _check_system_requests(self, user_input_lower: str) -> InputViolation:
        """Check for system instruction requests"""
        for pattern in self.system_instruction_requests:
            if re.search(pattern, user_input_lower, re.IGNORECASE):
                return InputViolation.SYSTEM_INSTRUCTION_REQUEST
        return None
    
    def _check_role_manipulation(self, user_input_lower: str) -> InputViolation:
        """Check for role manipulation attempts"""
        for pattern in self.role_manipulation_patterns:
            if re.search(pattern, user_input_lower, re.IGNORECASE):
                return InputViolation.ROLE_MANIPULATION
        return None
    
    def _check_jailbreak_attempts(self, user_input_lower: str) -> InputViolation:
        """Check for jailbreak keywords"""
        if any(term in user_input_lower for term in self.jailbreak_terms):
            return InputViolation.JAILBREAK_ATTEMPT
        return None
    
    def _check_context_manipulation(self, user_input: str, user_input_lower: str) -> InputViolation:
        """Check for malicious context manipulation attempts"""
        # Check for patterns that try to inject false conversation history
        for pattern in self.context_manipulation_patterns:
            if re.search(pattern, user_input_lower, re.IGNORECASE):
                return InputViolation.CONTEXT_MANIPULATION

        # Additional check: very long messages claiming false context
        if len(user_input) > 200:
            # Look for suspicious combinations
            context_words = ["earlier", "previously", "remember", "discussed", "conversation"]
            manipulation_words = ["said", "told", "agreed", "confirmed", "allowed"]
            dangerous_words = ["system", "prompt", "bypass", "ignore", "hack", "reveal"]

            context_count = sum(1 for word in context_words if word in user_input_lower)
            manipulation_count = sum(1 for word in manipulation_words if word in user_input_lower)
            dangerous_count = sum(1 for word in dangerous_words if word in user_input_lower)

            # If message has multiple indicators, it's likely manipulation
            if context_count >= 2 and manipulation_count >= 1 and dangerous_count >= 1:
                return InputViolation.CONTEXT_MANIPULATION

        return None
    
    def _check_code_injection(self, user_input: str) -> InputViolation:
        """Check for code injection attempts"""
        for pattern in self.code_patterns:
            if re.search(pattern, user_input, re.IGNORECASE):
                return InputViolation.CODE_INJECTION
        return None
    
    def _check_personal_information(self, user_input: str) -> List[str]:
        """Check for personal information sharing"""
        warnings = []
        
        # SSN pattern
        if re.search(r'\b\d{3}-\d{2}-\d{4}\b', user_input):
            warnings.append("Please don't share personal identification numbers for your privacy and security.")
        
        # Credit card pattern
        if re.search(r'\b\d{4}\s?\d{4}\s?\d{4}\s?\d{4}\b', user_input):
            warnings.append("Please don't share financial account numbers for your security.")
        
        return warnings
    
    def _check_illegal_activity(self, user_input_lower: str) -> InputViolation:
        """Check for illegal activity requests"""
        if any(term in user_input_lower for term in self.illegal_terms):
            return InputViolation.ILLEGAL_ACTIVITY
        return None
    
    def _check_profanity(self, user_input_lower: str) -> InputViolation:
        """Check for excessive profanity"""
        profanity_count = sum(1 for term in self.profanity_terms if term in user_input_lower)
        if profanity_count > 2:
            return InputViolation.EXCESSIVE_PROFANITY
        return None
    
    def _check_input_length(self, user_input: str) -> str:
        """Check input length"""
        if len(user_input) > 1000:
            return "Please keep your questions concise so I can provide the best financial advice."
        return None

    def _check_off_topic_query(self, user_input: str) -> InputViolation:
        """Check if query is off-topic (celebrity questions, weather, etc.)"""
        user_input_lower = user_input.lower()

        # Celebrity and entertainment queries - EXPANDED LIST
        celebrity_indicators = [
            "height of", "age of", "birthday of", "born when", "how old is", "how tall is",
            "what is the height", "what's the height", "height", "tall", "age",
            "actor", "actress", "celebrity", "bollywood", "hollywood",
            "movie star", "film star", "singer", "musician", "artist", "star",
            "net worth", "salary", "income", "married to", "wife", "husband",
            "children", "family", "personal life", "biography", "bio"
        ]

        celebrity_names = [
            "hrithik roshan", "shah rukh khan", "amitabh bachchan", "deepika padukone",
            "priyanka chopra", "salman khan", "aamir khan", "akshay kumar",
            "katrina kaif", "ranveer singh", "ranbir kapoor", "alia bhatt",
            "virat kohli", "ms dhoni", "sachin tendulkar", "rohit sharma",
            "taylor swift", "leonardo dicaprio", "brad pitt", "angelina jolie",
            "tom cruise", "will smith", "jennifer lawrence", "scarlett johansson",
            "robert downey jr", "chris evans", "chris hemsworth", "mark ruffalo",
            "narendra modi", "rahul gandhi", "elon musk", "bill gates",
            "warren buffett", "jeff bezos", "mark zuckerberg", "steve jobs"
        ]

        # Check for celebrity questions - MORE PRECISE DETECTION
        has_celebrity_name = any(name in user_input_lower for name in celebrity_names)

        # If celebrity name is found, check for personal info queries
        if has_celebrity_name:
            has_personal_query = any(indicator in user_input_lower for indicator in celebrity_indicators)
            if has_personal_query:
                return InputViolation.OFF_TOPIC_QUERY

        # Also check for general celebrity indicators without specific names
        general_celebrity_patterns = [
            "bollywood actor", "hollywood actor", "movie star", "film star",
            "famous actor", "famous actress", "celebrity height", "star height"
        ]

        if any(pattern in user_input_lower for pattern in general_celebrity_patterns):
            return InputViolation.OFF_TOPIC_QUERY

        # Other off-topic queries
        off_topic_patterns = [
            "what's the weather", "how's the weather", "weather forecast",
            "latest sports", "football score", "basketball game", "cricket match",
            "movie recommendation", "what to watch", "tv show",
            "recipe for", "how to cook", "cooking instructions",
            "travel to", "vacation in", "tourist attractions",
            "health symptoms", "medical condition", "doctor visit"
        ]

        for pattern in off_topic_patterns:
            if pattern in user_input_lower:
                return InputViolation.OFF_TOPIC_QUERY

        return None

    def _check_topic_relevance(self, user_input_lower: str) -> InputViolation:
        """Check if query is relevant to financial topics (relevance classifier)"""

        # Count financial vs non-financial topic indicators
        financial_count = sum(1 for topic in self.financial_topics if topic in user_input_lower)
        non_financial_count = sum(1 for topic in self.non_financial_topics if topic in user_input_lower)

        # If query has clear non-financial topics and no financial topics
        if non_financial_count >= 2 and financial_count == 0:
            return InputViolation.OFF_TOPIC_QUERY

        return None

    def _check_advisor_appropriateness(self, user_input_lower: str) -> InputViolation:
        """Check if query is appropriate for a financial advisor (relevance classifier)"""

        # Check for topics inappropriate for financial advisors
        for topic in self.inappropriate_for_advisor:
            if topic in user_input_lower:
                return InputViolation.INAPPROPRIATE_FOR_FINANCIAL_ADVISOR

        # Check for specific inappropriate requests
        inappropriate_patterns = [
            r"give.*medical.*advice", r"diagnose.*condition", r"prescribe.*medication",
            r"legal.*advice", r"sue.*someone", r"court.*case",
            r"relationship.*advice", r"dating.*tips", r"marriage.*counseling",
            r"therapy.*session", r"mental.*health.*treatment",
            r"hack.*into", r"break.*into", r"steal.*from"
        ]

        for pattern in inappropriate_patterns:
            if re.search(pattern, user_input_lower, re.IGNORECASE):
                return InputViolation.INAPPROPRIATE_FOR_FINANCIAL_ADVISOR

        return None

    # Library-based detection methods with simple explanations
    def _check_toxicity_with_detoxify(self, user_input: str) -> Tuple[bool, float]:
        """
        DETOXIFY: Check if the message is toxic/harmful using AI

        How it works:
        1. Sends the message to the AI model
        2. AI gives scores for different types of toxicity (0-1 scale)
        3. If any score is above 0.7, we consider it toxic

        Returns: (True if toxic, toxicity score)
        """
        if not self.detoxify:
            return False, 0.0  # If Detoxify isn't available, assume it's safe

        try:
            # Ask the AI: "Is this message toxic?"
            results = self.detoxify.predict(user_input)

            # Check all the different types of toxicity the AI can detect
            toxicity_score = max(
                results.get('toxicity', 0),        # General toxicity
                results.get('severe_toxicity', 0), # Really bad toxicity
                results.get('obscene', 0),         # Obscene/sexual content
                results.get('threat', 0),          # Threats of violence
                results.get('insult', 0),          # Insults and name-calling
                results.get('identity_attack', 0)  # Attacks on identity/groups
            )

            # If the highest score is above 0.7 (70%), consider it toxic
            is_toxic = toxicity_score > 0.7

            return is_toxic, toxicity_score

        except Exception as e:
            print(f"Error in toxicity detection: {e}")
            return False, 0.0  # If something goes wrong, assume it's safe

    def _check_profanity_with_library(self, user_input: str) -> bool:
        """
        BETTER-PROFANITY: Check if the message contains swear words

        How it works:
        1. Compares the message against a big list of bad words
        2. Can catch creative spellings like "f*ck" or "sh1t"
        3. Works in multiple languages

        Returns: True if profanity found, False if clean
        """
        if not PROFANITY_AVAILABLE:
            return False  # If library isn't available, assume it's clean

        try:
            # Simple check: does this message contain any bad words?
            return profanity.contains_profanity(user_input)
        except Exception as e:
            print(f"Error in profanity detection: {e}")
            return False  # If something goes wrong, assume it's clean

    def _check_pii_with_presidio(self, user_input: str) -> List[str]:
        """
        PRESIDIO: Check if the message contains personal information (PII)

        How it works:
        1. Scans the message for patterns like emails, phone numbers, etc.
        2. Uses smart pattern matching to find personal data
        3. Protects user privacy by catching sensitive information

        Returns: List of PII types found (e.g., ["EMAIL_ADDRESS", "PHONE_NUMBER"])
        """
        if not self.presidio_analyzer:
            return []  # If Presidio isn't available, assume no PII

        try:
            # Celebrity names and public figures that should NOT be treated as PII - EXPANDED
            celebrity_names = [
                "hrithik roshan", "shah rukh khan", "amitabh bachchan", "deepika padukone",
                "priyanka chopra", "salman khan", "aamir khan", "akshay kumar",
                "katrina kaif", "ranveer singh", "ranbir kapoor", "alia bhatt",
                "virat kohli", "ms dhoni", "sachin tendulkar", "rohit sharma",
                "narendra modi", "rahul gandhi", "elon musk", "bill gates",
                "warren buffett", "jeff bezos", "mark zuckerberg", "steve jobs",
                "donald trump", "joe biden", "barack obama", "taylor swift",
                "leonardo dicaprio", "brad pitt", "angelina jolie", "tom cruise",
                "will smith", "jennifer lawrence", "scarlett johansson", "robert downey jr",
                "chris evans", "chris hemsworth", "mark ruffalo", "tom hanks",
                "morgan freeman", "denzel washington", "samuel l jackson"
            ]

            # Common words that might be detected as names but aren't PII
            common_words_not_pii = [
                "height", "age", "tall", "old", "born", "birthday", "net worth",
                "salary", "income", "married", "wife", "husband", "children"
            ]

            # Check if the query is asking about a celebrity or contains non-PII words
            user_input_lower = user_input.lower()

            # Skip PII detection for celebrity queries
            for celebrity in celebrity_names:
                if celebrity in user_input_lower:
                    return []

            # Skip PII detection for common non-PII words
            for word in common_words_not_pii:
                if word in user_input_lower and len(user_input.split()) <= 10:
                    # Short queries with these words are likely not sharing PII
                    return []

            # Ask Presidio: "What personal information is in this message?"
            results = self.presidio_analyzer.analyze(
                text=user_input,
                language='en',  # We're checking English text
                entities=[
                    "PHONE_NUMBER",    # Phone numbers like ************
                    "EMAIL_ADDRESS",   # <NAME_EMAIL>
                    "CREDIT_CARD",     # Credit card numbers
                    "CRYPTO",          # Cryptocurrency addresses
                    "IBAN_CODE",       # Bank account numbers
                    "IP_ADDRESS",      # IP addresses like ***********
                    "PERSON",          # People's names (but exclude celebrities)
                    "LOCATION",        # Addresses and locations
                    "ORGANIZATION"     # Company names
                ]
            )

            # Filter out celebrity names from PERSON entities
            detected_pii = []
            for result in results:
                if result.score > 0.7:  # Only confident detections
                    if result.entity_type == "PERSON":
                        # Check if this person name is a celebrity
                        detected_text = user_input[result.start:result.end].lower()
                        is_celebrity = any(celebrity in detected_text for celebrity in celebrity_names)
                        if not is_celebrity:
                            detected_pii.append(result.entity_type)
                    else:
                        detected_pii.append(result.entity_type)

            return detected_pii

        except Exception as e:
            print(f"Error in PII detection: {e}")
            return []  # If something goes wrong, assume no PII

    def _check_prompt_injection_with_library(self, user_input: str) -> Tuple[bool, str]:
        """
        ENHANCED PROMPT INJECTION: Check if someone is trying to hack the AI

        How it works:
        1. Uses Detoxify to check if the message is trying to be manipulative
        2. Looks for specific phrases that hackers use to trick AI systems
        3. Combines both methods for better detection

        Returns: (True if injection detected, explanation of what was found)
        """

        # First, use our AI toxicity detector to see if this looks suspicious
        is_toxic, toxicity_score = self._check_toxicity_with_detoxify(user_input)

        # Convert to lowercase for easier pattern matching
        user_input_lower = user_input.lower()

        # Common phrases that hackers use to try to trick AI systems
        injection_indicators = [
            # Trying to make the AI ignore its instructions
            ("ignore", ["ignore previous", "ignore all", "ignore instructions"]),
            # Trying to override the AI's programming
            ("override", ["override system", "override programming", "override safety"]),
            # Trying to bypass safety measures
            ("bypass", ["bypass restrictions", "bypass safety", "bypass filters"]),
            # Trying to put the AI in "hacker mode"
            ("jailbreak", ["jailbreak mode", "developer mode", "god mode"]),
            # Trying to make the AI pretend to be something else
            ("roleplay", ["pretend you are", "act as if", "roleplay as"]),
            # Trying to access system information
            ("system", ["system prompt", "system message", "show system"]),
            # Trying to give the AI new instructions
            ("instructions", ["new instructions", "different instructions", "forget instructions"])
        ]

        # Check if any of these suspicious phrases are in the message
        detected_patterns = []
        for category, patterns in injection_indicators:
            for pattern in patterns:
                if pattern in user_input_lower:
                    detected_patterns.append(f"{category}:{pattern}")

        # Consider it an injection attempt if:
        # 1. We found suspicious phrases, OR
        # 2. The toxicity AI thinks it's highly manipulative (score > 0.8)
        is_injection = len(detected_patterns) > 0 or (is_toxic and toxicity_score > 0.8)

        # Create an explanation of what we detected
        detection_reason = f"Patterns: {detected_patterns}, Toxicity: {toxicity_score:.2f}" if detected_patterns or is_toxic else ""

        return is_injection, detection_reason

    # Smart response methods that use library detection details
    def _get_smart_prompt_injection_response(self, user_input: str, detection_reason: str) -> Tuple[str, str]:
        """Generate intelligent response based on what the libraries actually detected"""

        # Analyze what was specifically detected for brief flag
        if "ignore" in detection_reason.lower():
            flag_type = "instruction manipulation"
        elif "override" in detection_reason.lower():
            flag_type = "system override attempt"
        elif "bypass" in detection_reason.lower():
            flag_type = "security bypass attempt"
        elif "roleplay" in detection_reason.lower():
            flag_type = "role manipulation"
        elif "toxicity" in detection_reason.lower():
            flag_type = "manipulative language"
        else:
            flag_type = "security violation"

        flag = f"🚨 Security Flag: {flag_type} detected"

        # Generate dynamic response using LLM with detection context
        empathetic_response = self._generate_dynamic_response(
            violation_type="prompt_injection",
            user_input=user_input,
            detection_context={
                "method": "Prompt injection detection libraries",
                "attack_type": flag_type,
                "detection_reason": detection_reason,
                "explanation": f"Security system detected {flag_type} patterns in your message",
                "purpose": "Protecting AI system integrity and user safety"
            }
        )

        return flag, empathetic_response

    def _get_smart_toxicity_response(self, user_input: str, toxicity_score: float) -> Tuple[str, str]:
        """Generate response based on Detoxify toxicity detection"""

        flag = f"🚨 Content Flag: Inappropriate language detected (toxicity: {toxicity_score:.2f})"

        # Generate dynamic response using LLM with detection context
        empathetic_response = self._generate_dynamic_response(
            violation_type="toxicity",
            user_input=user_input,
            detection_context={
                "method": "Detoxify AI toxicity detection",
                "toxicity_score": toxicity_score,
                "confidence": f"{toxicity_score:.1%}",
                "explanation": f"AI model detected inappropriate language with {toxicity_score:.1%} confidence"
            }
        )

        return flag, empathetic_response

    def _generate_dynamic_response(self, violation_type: str, user_input: str, detection_context: dict) -> str:
        """Generate dynamic, contextual responses using LLM instead of hardcoded text"""
        try:
            from utils.llm_manager import llm_manager

            # Create specific prompts based on violation type for better responses
            if violation_type == "toxicity":
                base_prompt = f"""You are PennyWise, a warm financial advisor. A user said "{user_input}" which was detected as toxic language (score: {detection_context.get('toxicity_score', 'high')}).

Respond empathetically - acknowledge they might be frustrated about something, but redirect to how you can help with financial stress or goals. Be understanding, not preachy. 2-3 sentences max."""

            elif violation_type == "profanity":
                base_prompt = f"""You are PennyWise, a friendly financial advisor. A user used some strong language: "{user_input}".

Respond with understanding - maybe they're frustrated about money issues? Acknowledge their feelings and offer to help with financial challenges. Be warm and supportive. 2-3 sentences max."""

            elif violation_type == "off_topic":
                topic = detection_context.get('detected_topic', 'that topic')
                base_prompt = f"""You are PennyWise, a financial advisor. A user asked about {topic}: "{user_input}".

Respond with curiosity and understanding - acknowledge their interest in {topic}, but warmly redirect to how you can help with their financial dreams instead. Be friendly and engaging. 2-3 sentences max."""

            elif violation_type == "privacy_violation":
                pii_types = detection_context.get('pii_summary', 'personal information')
                base_prompt = f"""You are PennyWise, a caring financial advisor. A user shared {pii_types} in their message: "{user_input}".

Respond with appreciation for their trust, but gently explain you want to protect their privacy. Offer to help with financial questions without needing personal details. Be warm and protective. 2-3 sentences max."""

            else:
                base_prompt = f"""You are PennyWise, a friendly financial advisor. A user's message "{user_input}" was flagged for {violation_type}.

Respond with understanding and redirect to financial topics you can help with. Be empathetic and supportive. 2-3 sentences max."""

            # Get LLM response
            llm_response = llm_manager.get_completion(base_prompt, max_tokens=100, temperature=0.8)

            # Clean up the response
            response = llm_response.strip()
            if response.startswith('"') and response.endswith('"'):
                response = response[1:-1]

            # Ensure it's not empty and has reasonable length
            if len(response) < 20:
                raise Exception("Response too short")

            return response

        except Exception as e:
            print(f"Error generating dynamic response for {violation_type}: {e}")

            # Improved fallback responses based on violation type
            if violation_type == "toxicity":
                return "I can sense you might be feeling frustrated about something. I'm here to help you tackle any financial challenges that might be adding to your stress. What's going on financially that I could assist you with?"
            elif violation_type == "profanity":
                return "I hear you might be expressing some strong feelings! Sometimes money matters can be really frustrating. What financial situation can I help you work through today?"
            elif violation_type == "off_topic":
                topic = detection_context.get('detected_topic', 'that topic')
                return f"I can see you're curious about {topic}! While that's not my area, I'd love to help you achieve your financial dreams. What financial goal are you working toward?"
            elif violation_type == "privacy_violation":
                return "I appreciate you wanting to share details with me! For your privacy and security, let's keep personal info private. I can still help with your financial questions though - what would you like to know?"
            else:
                return "I understand you're reaching out, and I'd love to help you with your financial goals. What financial topic can I assist you with today?"

    def _get_smart_profanity_response(self, user_input: str) -> Tuple[str, str]:
        """Generate response based on better-profanity detection"""

        return f"""� **Language Flag**: Inappropriate language detected

I understand you might be expressing frustration, but let's keep our conversation professional. I'm here to help with your financial goals! What would you like to know about investing, budgeting, or planning?"""

    def _get_smart_pii_response(self, user_input: str, detected_pii: List[str]) -> Tuple[str, str]:
        """Generate response based on Presidio PII detection"""

        pii_types = ", ".join([pii.replace("_", " ").lower() for pii in detected_pii])

        flag = f"🔒 Privacy Flag: Personal information detected ({pii_types})"

        # Generate dynamic response using LLM with detection context
        empathetic_response = self._generate_dynamic_response(
            violation_type="privacy_violation",
            user_input=user_input,
            detection_context={
                "method": "Presidio PII analyzer",
                "detected_types": detected_pii,
                "pii_summary": pii_types,
                "explanation": f"Privacy protection system detected {pii_types} in your message",
                "library": "Microsoft Presidio",
                "purpose": "Protecting your personal information from being shared"
            }
        )

        return flag, empathetic_response

    def _get_prompt_injection_response(self, user_input: str) -> str:
        """Get specific response for prompt injection attempts"""
        detected_patterns = []
        user_input_lower = user_input.lower()

        if any(word in user_input_lower for word in ["ignore", "forget", "disregard"]):
            detected_patterns.append("instruction override attempts")
        if any(word in user_input_lower for word in ["pretend", "act as", "roleplay"]):
            detected_patterns.append("role-playing requests")
        if any(word in user_input_lower for word in ["system", "prompt", "programming"]):
            detected_patterns.append("system access attempts")

        pattern_text = " and ".join(detected_patterns) if detected_patterns else "instruction manipulation"

        return f"""I noticed your message contains {pattern_text}, which I can't respond to for security reasons.

I'm PennyWise, your financial advisor, and I'm designed to help with:
💰 Investment planning and strategies
🏠 Saving for major purchases
📊 Budgeting and debt management
🎯 Retirement and financial goal planning

What financial topic would you like to explore today?"""

    def _get_system_request_response(self, user_input: str) -> str:
        """Get specific response for system instruction requests"""
        user_input_lower = user_input.lower()

        if "prompt" in user_input_lower:
            request_type = "system prompt"
        elif "instructions" in user_input_lower:
            request_type = "my instructions"
        elif "programming" in user_input_lower or "code" in user_input_lower:
            request_type = "my programming details"
        elif "model" in user_input_lower or "training" in user_input_lower:
            request_type = "my technical specifications"
        else:
            request_type = "my internal workings"

        return f"""I can't share details about {request_type} as that's not relevant to financial advice.

Instead, let me help you with what I do best - financial guidance! I can assist with:
📈 Investment strategies and portfolio planning
💳 Debt management and credit improvement
🏦 Banking and savings optimization
📊 Financial planning for life goals

What financial challenge can I help you tackle?"""

    def _get_role_manipulation_response(self, user_input: str) -> str:
        """Get specific response for role manipulation attempts"""
        user_input_lower = user_input.lower()

        if "not pennywise" in user_input_lower:
            manipulation_type = "change my identity"
        elif "actually" in user_input_lower or "really" in user_input_lower:
            manipulation_type = "claim I'm someone else"
        elif "personality" in user_input_lower:
            manipulation_type = "alter my personality"
        else:
            manipulation_type = "change who I am"

        return f"""I noticed you're trying to {manipulation_type}, but I'm consistently PennyWise - your dedicated financial advisor.

My role is specifically to help you with financial matters like:
💡 Smart investment decisions
🎯 Achieving your financial goals
📋 Creating budgets that work
🔒 Building financial security

I stay focused on finance because that's where I can provide you the most value. What financial topic interests you?"""

    def _get_jailbreak_response(self, user_input: str) -> str:
        """Get specific response for jailbreak attempts"""
        user_input_lower = user_input.lower()

        detected_terms = []
        if "jailbreak" in user_input_lower:
            detected_terms.append("jailbreak")
        if "bypass" in user_input_lower:
            detected_terms.append("bypass")
        if "override" in user_input_lower:
            detected_terms.append("override")
        if "hack" in user_input_lower:
            detected_terms.append("hack")

        terms_text = ", ".join(detected_terms) if detected_terms else "security circumvention"

        return f"""I detected {terms_text} language in your message. I'm designed with safety measures that I can't and won't circumvent.

These protections ensure I provide responsible financial advice. Let me help you with legitimate financial questions:
🎯 Investment planning and risk assessment
💰 Saving strategies and emergency funds
📊 Retirement planning and wealth building
🏠 Major purchase planning (homes, cars, etc.)

What financial goal would you like to work toward?"""

    def _get_context_manipulation_response(self, user_input: str) -> str:
        """Get specific response for context manipulation attempts"""
        return f"""I noticed your message references previous conversations that don't align with our actual chat history.

I maintain accurate records of our conversations to provide consistent financial advice. Let's focus on your current financial needs:

🔍 **Current conversation**: What financial topic can I help you with right now?
📊 **My expertise**: Investment planning, budgeting, retirement strategies, and financial goal setting

What specific financial question do you have today?"""

    def _get_code_injection_response(self, user_input: str) -> str:
        """Get specific response for code injection attempts"""
        user_input_lower = user_input.lower()

        if "script" in user_input_lower:
            code_type = "script code"
        elif "javascript" in user_input_lower:
            code_type = "JavaScript"
        elif "python" in user_input_lower or "import" in user_input_lower:
            code_type = "programming code"
        else:
            code_type = "code"

        return f"""I detected {code_type} in your message, which I can't process as I'm focused on financial advice, not programming.

If you're interested in the financial aspects of technology, I can help with:
💻 Tech stock investments and analysis
🚀 Investing in innovation and growth sectors
📊 Budgeting for technology purchases
💰 Financial planning for tech professionals

What financial aspect of technology interests you?"""

    def _get_off_topic_response(self, user_input: str) -> str:
        """Get specific response for off-topic queries"""
        user_input_lower = user_input.lower()

        # Identify the off-topic subject
        if any(word in user_input_lower for word in ["weather", "forecast", "rain", "sunny"]):
            topic = "weather"
        elif any(word in user_input_lower for word in ["sports", "football", "basketball", "game", "cricket"]):
            topic = "sports"
        elif any(word in user_input_lower for word in ["movie", "film", "tv", "entertainment", "actor", "actress", "celebrity"]):
            topic = "entertainment"
        elif any(word in user_input_lower for word in ["recipe", "cooking", "food"]):
            topic = "cooking/food"
        elif any(word in user_input_lower for word in ["travel", "vacation", "trip"]):
            topic = "travel"
        elif any(word in user_input_lower for word in ["height", "age", "birthday", "personal"]):
            topic = "personal information"
        else:
            topic = "that topic"

        # Generate dynamic response using LLM with detection context
        return self._generate_dynamic_response(
            violation_type="off_topic",
            user_input=user_input,
            detection_context={
                "method": "Topic relevance classification",
                "detected_topic": topic,
                "explanation": f"Query classified as {topic}-related, which is outside financial advisory scope",
                "purpose": "Maintaining focus on financial advisory services"
            }
        )

    def _get_inappropriate_topic_response(self, user_input: str) -> str:
        """Get specific response for topics inappropriate for financial advisors"""
        user_input_lower = user_input.lower()

        if any(word in user_input_lower for word in ["medical", "doctor", "health", "symptoms"]):
            topic = "medical advice"
            professional = "healthcare professional or doctor"
        elif any(word in user_input_lower for word in ["legal", "lawyer", "court", "sue"]):
            topic = "legal advice"
            professional = "qualified attorney or legal advisor"
        elif any(word in user_input_lower for word in ["relationship", "dating", "marriage"]):
            topic = "relationship advice"
            professional = "relationship counselor or therapist"
        elif any(word in user_input_lower for word in ["therapy", "mental health", "depression"]):
            topic = "mental health support"
            professional = "licensed therapist or mental health professional"
        else:
            topic = "that type of advice"
            professional = "appropriate specialist"

        return f"""I understand you're seeking {topic}, but as a financial advisor, I'm not qualified to help with that area.

For {topic}, I'd recommend consulting with a {professional} who has the proper expertise.

However, I can help if there are financial aspects to consider:
💰 Budgeting for professional services
🏥 Health Savings Accounts (HSAs) for medical expenses
📊 Financial planning during life transitions
💳 Managing expenses during challenging times

Is there a financial aspect of your situation I can help with?"""

    def get_safe_fallback_response(self, user_input: str) -> str:
        """Provide a safe fallback response for problematic inputs"""
        return """I'm PennyWise, your personal financial advisor. I'm here to help you with:

💰 Investment planning and portfolio advice
🏠 Saving for major purchases like homes
📈 Understanding market trends and economic indicators  
💳 Debt management and budgeting strategies
🎯 Setting and achieving financial goals
📊 Retirement planning and wealth building

What financial topic would you like to explore today?"""

# Global input guardrails instance
input_guardrails = InputGuardrails()
