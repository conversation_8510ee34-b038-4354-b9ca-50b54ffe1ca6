"""
Market Agent - Market Analysis and Sentiment
Handles market sentiment, trends, and sector performance analysis
"""

import asyncio
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
import yfinance as yf

logger = logging.getLogger(__name__)

class MarketAgent:
    """Agent responsible for market analysis and sentiment"""
    
    def __init__(self):
        self.is_initialized = False
        self.sector_etfs = {}
        
    async def initialize(self):
        """Initialize the market agent"""
        try:
            logger.info("Initializing Market Agent")
            
            # Initialize sector ETF mappings
            self.sector_etfs = {
                "technology": "XLK",
                "healthcare": "XLV", 
                "financials": "XLF",
                "energy": "XLE",
                "utilities": "XLU",
                "consumer_discretionary": "XLY",
                "consumer_staples": "XLP",
                "industrials": "XLI",
                "materials": "XLB",
                "real_estate": "XLRE",
                "communication": "XLC"
            }
            
            self.is_initialized = True
            logger.info("✅ Market Agent initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize Market Agent: {e}")
            raise
    
    async def get_data(self, user_id: str, query: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Get market data based on query"""
        try:
            query_lower = query.lower()
            data = {}
            
            # Determine what market data is needed
            if any(word in query_lower for word in ['market', 'sentiment', 'trend', 'sector']):
                market_sentiment = await self._get_market_sentiment()
                if market_sentiment:
                    data['market_sentiment'] = market_sentiment
            
            if any(word in query_lower for word in ['sector', 'industry', 'performance']):
                sector_analysis = await self._get_sector_performance()
                if sector_analysis:
                    data['sector_analysis'] = sector_analysis
            
            if any(word in query_lower for word in ['volatility', 'risk', 'vix']):
                volatility_data = await self._get_volatility_analysis()
                if volatility_data:
                    data['volatility_analysis'] = volatility_data
            
            return data
            
        except Exception as e:
            logger.error(f"Failed to get market data: {e}")
            return {}
    
    async def _get_market_sentiment(self) -> Dict[str, Any]:
        """Analyze current market sentiment"""
        try:
            # Get major indices for sentiment analysis
            indices = {
                "^GSPC": "S&P 500",
                "^DJI": "Dow Jones", 
                "^IXIC": "NASDAQ",
                "^VIX": "VIX (Fear Index)"
            }
            
            sentiment_data = {
                "overall_sentiment": "neutral",
                "market_indicators": {},
                "sentiment_factors": [],
                "analysis_time": datetime.now().isoformat()
            }
            
            positive_indicators = 0
            negative_indicators = 0
            
            for symbol, name in indices.items():
                try:
                    ticker = yf.Ticker(symbol)
                    hist = ticker.history(period="5d")
                    
                    if not hist.empty:
                        current_price = hist['Close'].iloc[-1]
                        prev_price = hist['Close'].iloc[-2] if len(hist) > 1 else current_price
                        change_pct = ((current_price - prev_price) / prev_price) * 100 if prev_price != 0 else 0
                        
                        sentiment_data["market_indicators"][symbol] = {
                            "name": name,
                            "change_percent": round(change_pct, 2),
                            "current_value": round(current_price, 2)
                        }
                        
                        # Analyze sentiment
                        if symbol == "^VIX":
                            # VIX interpretation (inverse sentiment)
                            if current_price < 20:
                                positive_indicators += 1
                                sentiment_data["sentiment_factors"].append(f"Low volatility (VIX: {current_price:.1f}) suggests market confidence")
                            elif current_price > 30:
                                negative_indicators += 1
                                sentiment_data["sentiment_factors"].append(f"High volatility (VIX: {current_price:.1f}) suggests market fear")
                        else:
                            # Regular indices
                            if change_pct > 0.5:
                                positive_indicators += 1
                                sentiment_data["sentiment_factors"].append(f"{name} up {change_pct:.1f}% - positive momentum")
                            elif change_pct < -0.5:
                                negative_indicators += 1
                                sentiment_data["sentiment_factors"].append(f"{name} down {change_pct:.1f}% - negative pressure")
                        
                except Exception as e:
                    logger.warning(f"Failed to get data for {symbol}: {e}")
                    continue
            
            # Determine overall sentiment
            if positive_indicators > negative_indicators:
                sentiment_data["overall_sentiment"] = "positive"
            elif negative_indicators > positive_indicators:
                sentiment_data["overall_sentiment"] = "negative"
            else:
                sentiment_data["overall_sentiment"] = "neutral"
            
            # Add general market insights
            sentiment_data["insights"] = self._generate_market_insights(sentiment_data)
            
            return sentiment_data
            
        except Exception as e:
            logger.error(f"Failed to get market sentiment: {e}")
            return {}
    
    async def _get_sector_performance(self) -> Dict[str, Any]:
        """Analyze sector performance"""
        try:
            sector_data = {
                "sectors": {},
                "best_performing": [],
                "worst_performing": [],
                "analysis_time": datetime.now().isoformat()
            }
            
            sector_performance = []
            
            for sector_name, etf_symbol in self.sector_etfs.items():
                try:
                    ticker = yf.Ticker(etf_symbol)
                    hist = ticker.history(period="1mo")  # 1 month performance
                    
                    if not hist.empty:
                        start_price = hist['Close'].iloc[0]
                        current_price = hist['Close'].iloc[-1]
                        performance = ((current_price - start_price) / start_price) * 100
                        
                        sector_info = {
                            "name": sector_name.replace("_", " ").title(),
                            "etf_symbol": etf_symbol,
                            "performance_1m": round(performance, 2),
                            "current_price": round(current_price, 2)
                        }
                        
                        sector_data["sectors"][sector_name] = sector_info
                        sector_performance.append((sector_name, performance))
                        
                except Exception as e:
                    logger.warning(f"Failed to get data for sector {sector_name}: {e}")
                    continue
            
            # Sort sectors by performance
            sector_performance.sort(key=lambda x: x[1], reverse=True)
            
            # Get best and worst performers
            if len(sector_performance) >= 3:
                sector_data["best_performing"] = [
                    {
                        "sector": sector_performance[i][0].replace("_", " ").title(),
                        "performance": round(sector_performance[i][1], 2)
                    }
                    for i in range(min(3, len(sector_performance)))
                ]
                
                sector_data["worst_performing"] = [
                    {
                        "sector": sector_performance[i][0].replace("_", " ").title(),
                        "performance": round(sector_performance[i][1], 2)
                    }
                    for i in range(max(0, len(sector_performance) - 3), len(sector_performance))
                ]
            
            # Add sector insights
            sector_data["insights"] = self._generate_sector_insights(sector_data)
            
            return sector_data
            
        except Exception as e:
            logger.error(f"Failed to get sector performance: {e}")
            return {}
    
    async def _get_volatility_analysis(self) -> Dict[str, Any]:
        """Analyze market volatility"""
        try:
            volatility_data = {
                "vix_level": None,
                "volatility_interpretation": "",
                "risk_assessment": "",
                "recommendations": [],
                "analysis_time": datetime.now().isoformat()
            }
            
            # Get VIX data
            try:
                vix_ticker = yf.Ticker("^VIX")
                vix_hist = vix_ticker.history(period="5d")
                
                if not vix_hist.empty:
                    current_vix = vix_hist['Close'].iloc[-1]
                    volatility_data["vix_level"] = round(current_vix, 2)
                    
                    # Interpret VIX level
                    if current_vix < 15:
                        volatility_data["volatility_interpretation"] = "Very Low Volatility"
                        volatility_data["risk_assessment"] = "Market complacency - consider potential for sudden changes"
                        volatility_data["recommendations"] = [
                            "Consider taking some profits in overvalued positions",
                            "Maintain diversification as low volatility can change quickly",
                            "Good time for systematic investing strategies"
                        ]
                    elif current_vix < 20:
                        volatility_data["volatility_interpretation"] = "Low Volatility"
                        volatility_data["risk_assessment"] = "Relatively calm market conditions"
                        volatility_data["recommendations"] = [
                            "Normal market conditions for long-term investing",
                            "Consider dollar-cost averaging for new positions",
                            "Good environment for growth investments"
                        ]
                    elif current_vix < 30:
                        volatility_data["volatility_interpretation"] = "Moderate Volatility"
                        volatility_data["risk_assessment"] = "Some market uncertainty present"
                        volatility_data["recommendations"] = [
                            "Exercise caution with new investments",
                            "Consider defensive positions",
                            "Good time to review risk tolerance"
                        ]
                    else:
                        volatility_data["volatility_interpretation"] = "High Volatility"
                        volatility_data["risk_assessment"] = "Significant market fear and uncertainty"
                        volatility_data["recommendations"] = [
                            "Avoid panic selling - stick to long-term plan",
                            "Consider opportunities in quality stocks at discounts",
                            "Increase cash position for future opportunities"
                        ]
                        
            except Exception as e:
                logger.warning(f"Failed to get VIX data: {e}")
                volatility_data["volatility_interpretation"] = "Unable to assess current volatility"
            
            return volatility_data
            
        except Exception as e:
            logger.error(f"Failed to get volatility analysis: {e}")
            return {}
    
    def _generate_market_insights(self, sentiment_data: Dict[str, Any]) -> List[str]:
        """Generate market insights based on sentiment data"""
        insights = []
        
        overall_sentiment = sentiment_data.get("overall_sentiment", "neutral")
        
        if overall_sentiment == "positive":
            insights.append("Market momentum is currently positive - good environment for growth investments")
            insights.append("Consider taking some profits if you're overweight in any positions")
        elif overall_sentiment == "negative":
            insights.append("Market showing some weakness - consider defensive strategies")
            insights.append("Potential buying opportunities in quality stocks at discounted prices")
        else:
            insights.append("Market sentiment is mixed - maintain balanced approach")
            insights.append("Good time for dollar-cost averaging strategies")
        
        # Add VIX-specific insights
        market_indicators = sentiment_data.get("market_indicators", {})
        if "^VIX" in market_indicators:
            vix_value = market_indicators["^VIX"]["current_value"]
            if vix_value > 25:
                insights.append("Elevated volatility suggests increased market uncertainty")
            elif vix_value < 15:
                insights.append("Low volatility may indicate market complacency")
        
        return insights
    
    def _generate_sector_insights(self, sector_data: Dict[str, Any]) -> List[str]:
        """Generate sector insights based on performance data"""
        insights = []
        
        best_performers = sector_data.get("best_performing", [])
        worst_performers = sector_data.get("worst_performing", [])
        
        if best_performers:
            top_sector = best_performers[0]
            insights.append(f"{top_sector['sector']} is leading with {top_sector['performance']}% performance")
        
        if worst_performers:
            bottom_sector = worst_performers[-1]
            insights.append(f"{bottom_sector['sector']} is lagging with {bottom_sector['performance']}% performance")
        
        # General sector rotation insights
        if best_performers and worst_performers:
            if any("Technology" in p["sector"] for p in best_performers):
                insights.append("Technology sector strength suggests growth-oriented market")
            if any("Utilities" in p["sector"] for p in best_performers):
                insights.append("Utilities outperformance may indicate defensive positioning")
            if any("Energy" in p["sector"] for p in best_performers):
                insights.append("Energy sector strength could indicate inflation concerns")
        
        insights.append("Consider sector diversification to reduce concentration risk")
        
        return insights
    
    async def execute_task(self, task_type: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """Execute a market analysis task"""
        try:
            if task_type == "get_data":
                return await self.get_data(data['user_id'], data['query'], data.get('context', {}))
            elif task_type == "market_sentiment":
                return await self._get_market_sentiment()
            elif task_type == "sector_analysis":
                return await self._get_sector_performance()
            elif task_type == "volatility_analysis":
                return await self._get_volatility_analysis()
            else:
                return {'success': False, 'error': f'Unknown task type: {task_type}'}
                
        except Exception as e:
            logger.error(f"Failed to execute market task {task_type}: {e}")
            return {'success': False, 'error': str(e)}
    
    def update_config(self, config):
        """Update configuration"""
        try:
            logger.info("Updated market agent configuration")
        except Exception as e:
            logger.error(f"Failed to update config: {e}")
