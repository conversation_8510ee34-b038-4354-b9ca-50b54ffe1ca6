"""
Dynamic Configuration Management
Handles runtime configuration of database connections and services
"""

import os
import logging
from typing import Dict, Any, Optional
from dataclasses import dataclass, field
from threading import Lock

logger = logging.getLogger(__name__)

@dataclass
class DatabaseConfig:
    """Database configuration settings"""
    mongodb_url: str = "mongodb://localhost:27017"
    redis_url: str = "redis://localhost:6379"
    qdrant_url: str = "http://localhost:6333"
    groq_api_key: str = ""

@dataclass
class APIKeysConfig:
    """Real API keys for external services"""
    # Search & Information
    google_search_api_key: str = ""
    google_search_engine_id: str = ""
    serpapi_key: str = ""

    # Travel & Hotels
    booking_com_api_key: str = ""
    expedia_api_key: str = ""
    amadeus_api_key: str = ""
    amadeus_api_secret: str = ""

    # Financial Data
    alpha_vantage_api_key: str = ""
    finnhub_api_key: str = ""
    polygon_api_key: str = ""
    yahoo_finance_api_key: str = ""

    # Weather
    openweather_api_key: str = ""
    weatherapi_key: str = ""

    # News
    newsapi_key: str = ""

    # Maps & Places
    google_places_api_key: str = ""
    mapbox_api_key: str = ""

    # Currency
    exchangerate_api_key: str = ""
    fixer_api_key: str = ""

@dataclass
class AgentConfig:
    """Agent configuration settings"""
    max_retries: int = 3
    timeout_seconds: int = 30
    enable_monitoring: bool = True
    log_level: str = "INFO"

@dataclass
class MCPConfig:
    """MCP server configuration"""
    memory_server_port: int = 8001
    financial_server_port: int = 8002
    market_server_port: int = 8003
    config_server_port: int = 8004
    enable_auto_discovery: bool = True

class DynamicConfigManager:
    """Manages dynamic configuration for the multi-agent system"""
    
    def __init__(self):
        self._lock = Lock()
        self._agents = {}
        self._mcp_servers = {}
        self._db_config = DatabaseConfig()
        self._agent_config = AgentConfig()
        self._mcp_config = MCPConfig()
        self._api_keys_config = APIKeysConfig()
        self._load_from_environment()
    
    def _load_from_environment(self):
        """Load configuration from environment variables"""
        # Database config
        self._db_config.mongodb_url = os.getenv('MONGODB_URL', self._db_config.mongodb_url)
        self._db_config.redis_url = os.getenv('REDIS_URL', self._db_config.redis_url)
        self._db_config.qdrant_url = os.getenv('QDRANT_URL', self._db_config.qdrant_url)
        self._db_config.groq_api_key = os.getenv('GROQ_API_KEY', self._db_config.groq_api_key)

        # API keys config
        self._api_keys_config.google_search_api_key = os.getenv('GOOGLE_SEARCH_API_KEY', '')
        self._api_keys_config.google_search_engine_id = os.getenv('GOOGLE_SEARCH_ENGINE_ID', '')
        self._api_keys_config.serpapi_key = os.getenv('SERPAPI_KEY', '')
        self._api_keys_config.booking_com_api_key = os.getenv('BOOKING_COM_API_KEY', '')
        self._api_keys_config.expedia_api_key = os.getenv('EXPEDIA_API_KEY', '')
        self._api_keys_config.amadeus_api_key = os.getenv('AMADEUS_API_KEY', '')
        self._api_keys_config.amadeus_api_secret = os.getenv('AMADEUS_API_SECRET', '')
        self._api_keys_config.alpha_vantage_api_key = os.getenv('ALPHA_VANTAGE_API_KEY', '')
        self._api_keys_config.finnhub_api_key = os.getenv('FINNHUB_API_KEY', '')
        self._api_keys_config.polygon_api_key = os.getenv('POLYGON_API_KEY', '')
        self._api_keys_config.yahoo_finance_api_key = os.getenv('YAHOO_FINANCE_API_KEY', '')
        self._api_keys_config.openweather_api_key = os.getenv('OPENWEATHER_API_KEY', '')
        self._api_keys_config.weatherapi_key = os.getenv('WEATHERAPI_KEY', '')
        self._api_keys_config.newsapi_key = os.getenv('NEWSAPI_KEY', '')
        self._api_keys_config.google_places_api_key = os.getenv('GOOGLE_PLACES_API_KEY', '')
        self._api_keys_config.mapbox_api_key = os.getenv('MAPBOX_API_KEY', '')
        self._api_keys_config.exchangerate_api_key = os.getenv('EXCHANGERATE_API_KEY', '')
        self._api_keys_config.fixer_api_key = os.getenv('FIXER_API_KEY', '')

        logger.info("Configuration loaded from environment")
    
    def get_database_config(self) -> DatabaseConfig:
        """Get current database configuration"""
        with self._lock:
            return self._db_config
    
    def update_database_config(self, **kwargs) -> bool:
        """Update database configuration"""
        with self._lock:
            try:
                for key, value in kwargs.items():
                    if hasattr(self._db_config, key):
                        setattr(self._db_config, key, value)
                        # Update environment variable
                        os.environ[key.upper()] = value
                        logger.info(f"Updated {key} configuration")
                
                # Notify agents of configuration change
                self._notify_agents_config_change()
                return True
                
            except Exception as e:
                logger.error(f"Failed to update database config: {e}")
                return False
    
    def get_agent_config(self) -> AgentConfig:
        """Get current agent configuration"""
        with self._lock:
            return self._agent_config
    
    def update_agent_config(self, **kwargs) -> bool:
        """Update agent configuration"""
        with self._lock:
            try:
                for key, value in kwargs.items():
                    if hasattr(self._agent_config, key):
                        setattr(self._agent_config, key, value)
                        logger.info(f"Updated agent {key} configuration")
                
                return True
                
            except Exception as e:
                logger.error(f"Failed to update agent config: {e}")
                return False
    
    def get_mcp_config(self) -> MCPConfig:
        """Get current MCP configuration"""
        with self._lock:
            return self._mcp_config

    def get_api_keys_config(self) -> APIKeysConfig:
        """Get current API keys configuration"""
        with self._lock:
            return self._api_keys_config

    def update_api_key(self, service: str, api_key: str) -> bool:
        """Update a specific API key"""
        with self._lock:
            try:
                if hasattr(self._api_keys_config, service):
                    setattr(self._api_keys_config, service, api_key)
                    # Update environment variable
                    os.environ[service.upper()] = api_key
                    logger.info(f"Updated {service} API key")
                    return True
                else:
                    logger.error(f"Unknown API key service: {service}")
                    return False
            except Exception as e:
                logger.error(f"Failed to update API key for {service}: {e}")
                return False

    def test_api_key(self, service: str) -> Dict[str, Any]:
        """Test if an API key is working"""
        try:
            api_key = getattr(self._api_keys_config, service, "")
            if not api_key:
                return {"valid": False, "error": "API key not configured"}

            # Test different services
            if service == "google_search_api_key":
                return self._test_google_search_api(api_key)
            elif service == "openweather_api_key":
                return self._test_openweather_api(api_key)
            elif service == "newsapi_key":
                return self._test_newsapi(api_key)
            elif service == "alpha_vantage_api_key":
                return self._test_alpha_vantage_api(api_key)
            else:
                return {"valid": False, "error": f"No test available for {service}"}

        except Exception as e:
            return {"valid": False, "error": str(e)}

    def _test_google_search_api(self, api_key: str) -> Dict[str, Any]:
        """Test Google Search API"""
        try:
            import requests
            engine_id = self._api_keys_config.google_search_engine_id
            if not engine_id:
                return {"valid": False, "error": "Google Search Engine ID not configured"}

            url = f"https://www.googleapis.com/customsearch/v1"
            params = {
                "key": api_key,
                "cx": engine_id,
                "q": "test",
                "num": 1
            }
            response = requests.get(url, params=params, timeout=10)

            if response.status_code == 200:
                return {"valid": True, "message": "Google Search API is working"}
            else:
                return {"valid": False, "error": f"API returned status {response.status_code}"}

        except Exception as e:
            return {"valid": False, "error": str(e)}

    def _test_openweather_api(self, api_key: str) -> Dict[str, Any]:
        """Test OpenWeather API"""
        try:
            import requests
            url = f"https://api.openweathermap.org/data/2.5/weather"
            params = {
                "q": "London",
                "appid": api_key
            }
            response = requests.get(url, params=params, timeout=10)

            if response.status_code == 200:
                return {"valid": True, "message": "OpenWeather API is working"}
            else:
                return {"valid": False, "error": f"API returned status {response.status_code}"}

        except Exception as e:
            return {"valid": False, "error": str(e)}

    def _test_newsapi(self, api_key: str) -> Dict[str, Any]:
        """Test News API"""
        try:
            import requests
            url = "https://newsapi.org/v2/top-headlines"
            headers = {"X-API-Key": api_key}
            params = {
                "country": "us",
                "pageSize": 1
            }
            response = requests.get(url, headers=headers, params=params, timeout=10)

            if response.status_code == 200:
                return {"valid": True, "message": "News API is working"}
            else:
                return {"valid": False, "error": f"API returned status {response.status_code}"}

        except Exception as e:
            return {"valid": False, "error": str(e)}

    def _test_alpha_vantage_api(self, api_key: str) -> Dict[str, Any]:
        """Test Alpha Vantage API"""
        try:
            import requests
            url = "https://www.alphavantage.co/query"
            params = {
                "function": "GLOBAL_QUOTE",
                "symbol": "AAPL",
                "apikey": api_key
            }
            response = requests.get(url, params=params, timeout=10)

            if response.status_code == 200:
                data = response.json()
                if "Global Quote" in data:
                    return {"valid": True, "message": "Alpha Vantage API is working"}
                else:
                    return {"valid": False, "error": "Invalid API response"}
            else:
                return {"valid": False, "error": f"API returned status {response.status_code}"}

        except Exception as e:
            return {"valid": False, "error": str(e)}
    
    def set_agents(self, agents: Dict[str, Any]):
        """Set agent references for configuration updates"""
        with self._lock:
            self._agents = agents
            logger.info(f"Registered {len(agents)} agents")
    
    def set_mcp_servers(self, servers: Dict[str, Any]):
        """Set MCP server references"""
        with self._lock:
            self._mcp_servers = servers
            logger.info(f"Registered {len(servers)} MCP servers")
    
    def get_agents(self) -> Dict[str, Any]:
        """Get agent references"""
        with self._lock:
            return self._agents.copy()
    
    def get_mcp_servers(self) -> Dict[str, Any]:
        """Get MCP server references"""
        with self._lock:
            return self._mcp_servers.copy()
    
    def _notify_agents_config_change(self):
        """Notify all agents of configuration changes"""
        try:
            for agent_name, agent in self._agents.items():
                if hasattr(agent, 'update_config'):
                    agent.update_config(self._db_config)
                    logger.info(f"Notified {agent_name} of config change")
        except Exception as e:
            logger.error(f"Failed to notify agents of config change: {e}")
    
    def test_database_connections(self) -> Dict[str, bool]:
        """Test all database connections"""
        results = {}
        
        # Test MongoDB
        try:
            from pymongo import MongoClient
            client = MongoClient(self._db_config.mongodb_url, serverSelectionTimeoutMS=5000)
            client.server_info()
            results['mongodb'] = True
            client.close()
        except Exception as e:
            logger.error(f"MongoDB connection failed: {e}")
            results['mongodb'] = False
        
        # Test Redis
        try:
            import redis
            r = redis.from_url(self._db_config.redis_url, socket_timeout=5)
            r.ping()
            results['redis'] = True
            r.close()
        except Exception as e:
            logger.error(f"Redis connection failed: {e}")
            results['redis'] = False
        
        # Test Qdrant
        try:
            from qdrant_client import QdrantClient
            client = QdrantClient(url=self._db_config.qdrant_url, timeout=5)
            client.get_collections()
            results['qdrant'] = True
        except Exception as e:
            logger.error(f"Qdrant connection failed: {e}")
            results['qdrant'] = False
        
        # Test Groq API
        try:
            if self._db_config.groq_api_key:
                from langchain_groq import ChatGroq
                llm = ChatGroq(groq_api_key=self._db_config.groq_api_key, model_name="llama3-70b-8192")
                # Simple test call
                response = llm.invoke("Test")
                results['groq'] = True
            else:
                results['groq'] = False
        except Exception as e:
            logger.error(f"Groq API connection failed: {e}")
            results['groq'] = False
        
        return results
    
    def get_system_status(self) -> Dict[str, Any]:
        """Get overall system status"""
        return {
            'agents': {name: 'active' for name in self._agents.keys()},
            'mcp_servers': {name: 'active' for name in self._mcp_servers.keys()},
            'database_connections': self.test_database_connections(),
            'configuration': {
                'mongodb_url': self._db_config.mongodb_url,
                'redis_url': self._db_config.redis_url,
                'qdrant_url': self._db_config.qdrant_url,
                'groq_api_configured': bool(self._db_config.groq_api_key)
            }
        }

# Global configuration manager instance
config_manager = DynamicConfigManager()

# Convenience functions for global access
def get_database_config() -> DatabaseConfig:
    return config_manager.get_database_config()

def update_database_config(**kwargs) -> bool:
    return config_manager.update_database_config(**kwargs)

def get_agents() -> Dict[str, Any]:
    return config_manager.get_agents()

def set_agents(agents: Dict[str, Any]):
    config_manager.set_agents(agents)

def get_mcp_servers() -> Dict[str, Any]:
    return config_manager.get_mcp_servers()

def set_mcp_servers(servers: Dict[str, Any]):
    config_manager.set_mcp_servers(servers)

def test_database_connections() -> Dict[str, bool]:
    return config_manager.test_database_connections()

def get_system_status() -> Dict[str, Any]:
    return config_manager.get_system_status()
