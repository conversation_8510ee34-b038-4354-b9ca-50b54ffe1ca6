"""
Dynamic Configuration Management
Handles runtime configuration of database connections and services
"""

import os
import logging
from typing import Dict, Any, Optional
from dataclasses import dataclass, field
from threading import Lock

logger = logging.getLogger(__name__)

@dataclass
class DatabaseConfig:
    """Database configuration settings"""
    mongodb_url: str = "mongodb://localhost:27017"
    redis_url: str = "redis://localhost:6379"
    qdrant_url: str = "http://localhost:6333"
    groq_api_key: str = ""

@dataclass
class AgentConfig:
    """Agent configuration settings"""
    max_retries: int = 3
    timeout_seconds: int = 30
    enable_monitoring: bool = True
    log_level: str = "INFO"

@dataclass
class MCPConfig:
    """MCP server configuration"""
    memory_server_port: int = 8001
    financial_server_port: int = 8002
    market_server_port: int = 8003
    config_server_port: int = 8004
    enable_auto_discovery: bool = True

class DynamicConfigManager:
    """Manages dynamic configuration for the multi-agent system"""
    
    def __init__(self):
        self._lock = Lock()
        self._agents = {}
        self._mcp_servers = {}
        self._db_config = DatabaseConfig()
        self._agent_config = AgentConfig()
        self._mcp_config = MCPConfig()
        self._load_from_environment()
    
    def _load_from_environment(self):
        """Load configuration from environment variables"""
        self._db_config.mongodb_url = os.getenv('MONGODB_URL', self._db_config.mongodb_url)
        self._db_config.redis_url = os.getenv('REDIS_URL', self._db_config.redis_url)
        self._db_config.qdrant_url = os.getenv('QDRANT_URL', self._db_config.qdrant_url)
        self._db_config.groq_api_key = os.getenv('GROQ_API_KEY', self._db_config.groq_api_key)
        
        logger.info("Configuration loaded from environment")
    
    def get_database_config(self) -> DatabaseConfig:
        """Get current database configuration"""
        with self._lock:
            return self._db_config
    
    def update_database_config(self, **kwargs) -> bool:
        """Update database configuration"""
        with self._lock:
            try:
                for key, value in kwargs.items():
                    if hasattr(self._db_config, key):
                        setattr(self._db_config, key, value)
                        # Update environment variable
                        os.environ[key.upper()] = value
                        logger.info(f"Updated {key} configuration")
                
                # Notify agents of configuration change
                self._notify_agents_config_change()
                return True
                
            except Exception as e:
                logger.error(f"Failed to update database config: {e}")
                return False
    
    def get_agent_config(self) -> AgentConfig:
        """Get current agent configuration"""
        with self._lock:
            return self._agent_config
    
    def update_agent_config(self, **kwargs) -> bool:
        """Update agent configuration"""
        with self._lock:
            try:
                for key, value in kwargs.items():
                    if hasattr(self._agent_config, key):
                        setattr(self._agent_config, key, value)
                        logger.info(f"Updated agent {key} configuration")
                
                return True
                
            except Exception as e:
                logger.error(f"Failed to update agent config: {e}")
                return False
    
    def get_mcp_config(self) -> MCPConfig:
        """Get current MCP configuration"""
        with self._lock:
            return self._mcp_config
    
    def set_agents(self, agents: Dict[str, Any]):
        """Set agent references for configuration updates"""
        with self._lock:
            self._agents = agents
            logger.info(f"Registered {len(agents)} agents")
    
    def set_mcp_servers(self, servers: Dict[str, Any]):
        """Set MCP server references"""
        with self._lock:
            self._mcp_servers = servers
            logger.info(f"Registered {len(servers)} MCP servers")
    
    def get_agents(self) -> Dict[str, Any]:
        """Get agent references"""
        with self._lock:
            return self._agents.copy()
    
    def get_mcp_servers(self) -> Dict[str, Any]:
        """Get MCP server references"""
        with self._lock:
            return self._mcp_servers.copy()
    
    def _notify_agents_config_change(self):
        """Notify all agents of configuration changes"""
        try:
            for agent_name, agent in self._agents.items():
                if hasattr(agent, 'update_config'):
                    agent.update_config(self._db_config)
                    logger.info(f"Notified {agent_name} of config change")
        except Exception as e:
            logger.error(f"Failed to notify agents of config change: {e}")
    
    def test_database_connections(self) -> Dict[str, bool]:
        """Test all database connections"""
        results = {}
        
        # Test MongoDB
        try:
            from pymongo import MongoClient
            client = MongoClient(self._db_config.mongodb_url, serverSelectionTimeoutMS=5000)
            client.server_info()
            results['mongodb'] = True
            client.close()
        except Exception as e:
            logger.error(f"MongoDB connection failed: {e}")
            results['mongodb'] = False
        
        # Test Redis
        try:
            import redis
            r = redis.from_url(self._db_config.redis_url, socket_timeout=5)
            r.ping()
            results['redis'] = True
            r.close()
        except Exception as e:
            logger.error(f"Redis connection failed: {e}")
            results['redis'] = False
        
        # Test Qdrant
        try:
            from qdrant_client import QdrantClient
            client = QdrantClient(url=self._db_config.qdrant_url, timeout=5)
            client.get_collections()
            results['qdrant'] = True
        except Exception as e:
            logger.error(f"Qdrant connection failed: {e}")
            results['qdrant'] = False
        
        # Test Groq API
        try:
            if self._db_config.groq_api_key:
                from langchain_groq import ChatGroq
                llm = ChatGroq(groq_api_key=self._db_config.groq_api_key, model_name="llama3-70b-8192")
                # Simple test call
                response = llm.invoke("Test")
                results['groq'] = True
            else:
                results['groq'] = False
        except Exception as e:
            logger.error(f"Groq API connection failed: {e}")
            results['groq'] = False
        
        return results
    
    def get_system_status(self) -> Dict[str, Any]:
        """Get overall system status"""
        return {
            'agents': {name: 'active' for name in self._agents.keys()},
            'mcp_servers': {name: 'active' for name in self._mcp_servers.keys()},
            'database_connections': self.test_database_connections(),
            'configuration': {
                'mongodb_url': self._db_config.mongodb_url,
                'redis_url': self._db_config.redis_url,
                'qdrant_url': self._db_config.qdrant_url,
                'groq_api_configured': bool(self._db_config.groq_api_key)
            }
        }

# Global configuration manager instance
config_manager = DynamicConfigManager()

# Convenience functions for global access
def get_database_config() -> DatabaseConfig:
    return config_manager.get_database_config()

def update_database_config(**kwargs) -> bool:
    return config_manager.update_database_config(**kwargs)

def get_agents() -> Dict[str, Any]:
    return config_manager.get_agents()

def set_agents(agents: Dict[str, Any]):
    config_manager.set_agents(agents)

def get_mcp_servers() -> Dict[str, Any]:
    return config_manager.get_mcp_servers()

def set_mcp_servers(servers: Dict[str, Any]):
    config_manager.set_mcp_servers(servers)

def test_database_connections() -> Dict[str, bool]:
    return config_manager.test_database_connections()

def get_system_status() -> Dict[str, Any]:
    return config_manager.get_system_status()
