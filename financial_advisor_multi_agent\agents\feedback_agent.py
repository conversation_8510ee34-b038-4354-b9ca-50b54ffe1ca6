"""
Feedback Agent - User Feedback Collection and Analysis
Handles feedback collection, analysis, and learning from user interactions
"""

import asyncio
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class FeedbackEntry:
    """Single feedback entry"""
    feedback_id: str
    user_id: str
    query: str
    response: str
    feedback_type: str
    rating: Optional[int]
    comment: Optional[str]
    timestamp: datetime
    context: Dict[str, Any]

class FeedbackAgent:
    """Agent responsible for feedback collection and analysis"""
    
    def __init__(self):
        self.is_initialized = False
        self.feedback_storage = []
        
    async def initialize(self):
        """Initialize the feedback agent"""
        try:
            logger.info("Initializing Feedback Agent")
            
            self.is_initialized = True
            logger.info("✅ Feedback Agent initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize Feedback Agent: {e}")
            raise
    
    async def collect_feedback(self, user_id: str, query: str, response: str, feedback_data: Dict[str, Any]) -> Dict[str, Any]:
        """Collect user feedback"""
        try:
            import uuid
            
            # Create feedback entry
            feedback_entry = FeedbackEntry(
                feedback_id=str(uuid.uuid4()),
                user_id=user_id,
                query=query,
                response=response,
                feedback_type=feedback_data.get('type', 'general'),
                rating=feedback_data.get('rating'),
                comment=feedback_data.get('comment'),
                timestamp=datetime.now(),
                context=feedback_data.get('context', {})
            )
            
            # Store feedback
            self.feedback_storage.append(feedback_entry)
            
            # Analyze feedback for immediate insights
            analysis = await self._analyze_feedback(feedback_entry)
            
            # Store in memory systems through memory agent
            memory_result = await self._store_in_memory(user_id, query, response, feedback_data)
            
            return {
                "success": True,
                "feedback_id": feedback_entry.feedback_id,
                "analysis": analysis,
                "memory_stored": memory_result.get("success", False),
                "recommendations": self._generate_recommendations(feedback_entry, analysis)
            }
            
        except Exception as e:
            logger.error(f"Failed to collect feedback: {e}")
            return {"success": False, "error": str(e)}
    
    async def _analyze_feedback(self, feedback_entry: FeedbackEntry) -> Dict[str, Any]:
        """Analyze individual feedback entry"""
        try:
            analysis = {
                "sentiment": "neutral",
                "satisfaction_level": "medium",
                "key_themes": [],
                "improvement_areas": [],
                "positive_aspects": []
            }
            
            # Analyze rating if provided
            if feedback_entry.rating is not None:
                if feedback_entry.rating >= 4:
                    analysis["sentiment"] = "positive"
                    analysis["satisfaction_level"] = "high"
                elif feedback_entry.rating <= 2:
                    analysis["sentiment"] = "negative"
                    analysis["satisfaction_level"] = "low"
                else:
                    analysis["sentiment"] = "neutral"
                    analysis["satisfaction_level"] = "medium"
            
            # Analyze comment if provided
            if feedback_entry.comment:
                comment_analysis = self._analyze_comment(feedback_entry.comment)
                analysis["key_themes"].extend(comment_analysis["themes"])
                analysis["improvement_areas"].extend(comment_analysis["improvements"])
                analysis["positive_aspects"].extend(comment_analysis["positives"])
            
            # Analyze query-response alignment
            alignment_analysis = self._analyze_query_response_alignment(
                feedback_entry.query, 
                feedback_entry.response
            )
            analysis.update(alignment_analysis)
            
            return analysis
            
        except Exception as e:
            logger.error(f"Failed to analyze feedback: {e}")
            return {}
    
    def _analyze_comment(self, comment: str) -> Dict[str, List[str]]:
        """Analyze feedback comment for themes and insights"""
        comment_lower = comment.lower()
        
        themes = []
        improvements = []
        positives = []
        
        # Positive indicators
        positive_words = ['good', 'great', 'helpful', 'clear', 'useful', 'excellent', 'perfect', 'love', 'like']
        if any(word in comment_lower for word in positive_words):
            positives.append("User found response helpful")
        
        # Negative indicators
        negative_words = ['bad', 'poor', 'unclear', 'confusing', 'wrong', 'useless', 'hate', 'dislike']
        if any(word in comment_lower for word in negative_words):
            improvements.append("Response quality needs improvement")
        
        # Specific themes
        if any(word in comment_lower for word in ['detail', 'specific', 'more information']):
            themes.append("detail_level")
            if any(word in comment_lower for word in ['more', 'need', 'want']):
                improvements.append("Provide more detailed information")
            else:
                positives.append("Good level of detail provided")
        
        if any(word in comment_lower for word in ['simple', 'easy', 'understand', 'clear']):
            themes.append("clarity")
            if any(word in comment_lower for word in ['too', 'very', 'overly']):
                improvements.append("Adjust complexity level")
            else:
                positives.append("Clear and understandable explanation")
        
        if any(word in comment_lower for word in ['personal', 'relevant', 'situation']):
            themes.append("personalization")
            if any(word in comment_lower for word in ['not', 'doesn\'t', 'irrelevant']):
                improvements.append("Better personalization needed")
            else:
                positives.append("Well personalized advice")
        
        if any(word in comment_lower for word in ['fast', 'quick', 'slow', 'time']):
            themes.append("response_time")
        
        return {
            "themes": themes,
            "improvements": improvements,
            "positives": positives
        }
    
    def _analyze_query_response_alignment(self, query: str, response: str) -> Dict[str, Any]:
        """Analyze how well the response aligns with the query"""
        alignment_score = 0.5  # Default neutral alignment
        
        query_lower = query.lower()
        response_lower = response.lower()
        
        # Extract key terms from query
        query_words = set(query_lower.split())
        response_words = set(response_lower.split())
        
        # Calculate word overlap
        common_words = query_words.intersection(response_words)
        if len(query_words) > 0:
            word_overlap = len(common_words) / len(query_words)
            alignment_score = min(1.0, word_overlap * 2)  # Scale to 0-1
        
        # Check for specific financial topics
        financial_topics = ['invest', 'retirement', 'budget', 'debt', 'save', 'stock', 'bond']
        query_topics = [topic for topic in financial_topics if topic in query_lower]
        response_topics = [topic for topic in financial_topics if topic in response_lower]
        
        topic_alignment = len(set(query_topics).intersection(set(response_topics))) / max(1, len(query_topics))
        
        # Combine scores
        final_alignment = (alignment_score + topic_alignment) / 2
        
        return {
            "query_response_alignment": round(final_alignment, 2),
            "query_topics": query_topics,
            "response_topics": response_topics,
            "word_overlap_score": round(word_overlap if 'word_overlap' in locals() else 0, 2)
        }
    
    def _generate_recommendations(self, feedback_entry: FeedbackEntry, analysis: Dict[str, Any]) -> List[str]:
        """Generate recommendations based on feedback analysis"""
        recommendations = []
        
        # Rating-based recommendations
        if feedback_entry.rating is not None:
            if feedback_entry.rating <= 2:
                recommendations.append("Review response quality and accuracy")
                recommendations.append("Consider providing more detailed explanations")
                recommendations.append("Ensure better personalization to user context")
            elif feedback_entry.rating >= 4:
                recommendations.append("Continue using similar approach for this type of query")
                recommendations.append("Note successful patterns for future responses")
        
        # Theme-based recommendations
        themes = analysis.get("key_themes", [])
        if "detail_level" in themes:
            if "Provide more detailed information" in analysis.get("improvement_areas", []):
                recommendations.append("Increase detail level in responses")
            else:
                recommendations.append("Maintain current detail level")
        
        if "clarity" in themes:
            if "Adjust complexity level" in analysis.get("improvement_areas", []):
                recommendations.append("Simplify language and explanations")
            else:
                recommendations.append("Continue with clear explanations")
        
        if "personalization" in themes:
            if "Better personalization needed" in analysis.get("improvement_areas", []):
                recommendations.append("Improve use of user profile information")
                recommendations.append("Ask clarifying questions when context is unclear")
        
        # Alignment-based recommendations
        alignment_score = analysis.get("query_response_alignment", 0.5)
        if alignment_score < 0.3:
            recommendations.append("Improve response relevance to user query")
            recommendations.append("Focus more directly on answering the specific question asked")
        
        return recommendations
    
    async def _store_in_memory(self, user_id: str, query: str, response: str, feedback_data: Dict[str, Any]) -> Dict[str, Any]:
        """Store feedback in memory systems"""
        try:
            # This would integrate with the memory agent
            # For now, return success
            return {"success": True, "stored_in_memory": True}
            
        except Exception as e:
            logger.error(f"Failed to store feedback in memory: {e}")
            return {"success": False, "error": str(e)}
    
    async def get_feedback_summary(self, user_id: str) -> Dict[str, Any]:
        """Get feedback summary for a user"""
        try:
            user_feedback = [f for f in self.feedback_storage if f.user_id == user_id]
            
            if not user_feedback:
                return {"total_feedback": 0, "summary": "No feedback available"}
            
            # Calculate statistics
            total_feedback = len(user_feedback)
            rated_feedback = [f for f in user_feedback if f.rating is not None]
            
            summary = {
                "total_feedback": total_feedback,
                "rated_feedback": len(rated_feedback),
                "average_rating": 0,
                "positive_feedback": 0,
                "negative_feedback": 0,
                "recent_trends": [],
                "common_themes": []
            }
            
            if rated_feedback:
                ratings = [f.rating for f in rated_feedback]
                summary["average_rating"] = round(sum(ratings) / len(ratings), 1)
                summary["positive_feedback"] = len([r for r in ratings if r >= 4])
                summary["negative_feedback"] = len([r for r in ratings if r <= 2])
            
            # Analyze recent trends (last 5 feedback entries)
            recent_feedback = user_feedback[-5:]
            if len(recent_feedback) >= 2:
                recent_ratings = [f.rating for f in recent_feedback if f.rating is not None]
                if len(recent_ratings) >= 2:
                    if recent_ratings[-1] > recent_ratings[0]:
                        summary["recent_trends"].append("Improving satisfaction")
                    elif recent_ratings[-1] < recent_ratings[0]:
                        summary["recent_trends"].append("Declining satisfaction")
                    else:
                        summary["recent_trends"].append("Stable satisfaction")
            
            return summary
            
        except Exception as e:
            logger.error(f"Failed to get feedback summary: {e}")
            return {"error": str(e)}
    
    async def execute_task(self, task_type: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """Execute a feedback task"""
        try:
            if task_type == "collect_feedback":
                return await self.collect_feedback(
                    data['user_id'], 
                    data['query'], 
                    data['response'], 
                    data['feedback_data']
                )
            elif task_type == "get_summary":
                return await self.get_feedback_summary(data['user_id'])
            else:
                return {'success': False, 'error': f'Unknown task type: {task_type}'}
                
        except Exception as e:
            logger.error(f"Failed to execute feedback task {task_type}: {e}")
            return {'success': False, 'error': str(e)}
    
    def update_config(self, config):
        """Update configuration"""
        try:
            logger.info("Updated feedback agent configuration")
        except Exception as e:
            logger.error(f"Failed to update config: {e}")
