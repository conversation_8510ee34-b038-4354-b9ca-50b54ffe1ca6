import streamlit as st
import uuid
import time
from graphs.enhanced_workflow import enhanced_workflow
from memory.enhanced_memory import memory
from graphs.planning_system import planning_system
from guardrails.unified_guardrails import financial_guardrails
from utils.llm_manager import llm_manager
from feedback.feedback_system import feedback_collector, feedback_analyzer

def display_planning_steps(planning_data):
    """Display simple, user-friendly planning steps - NO TECHNICAL DETAILS"""

    st.markdown("---")
    st.markdown("## 📋 **How I Helped You**")

    if not planning_data:
        st.warning("No planning data available.")
        st.markdown("---")
        return

    user_query = planning_data.get("user_query", "Unknown query")
    analysis_type = planning_data.get("analysis_type", "unknown")
    processing_time = planning_data.get("processing_time", 0)
    vector_context = planning_data.get("vector_context", "")
    financial_plan = planning_data.get("financial_plan", {})

def display_reasoning_steps(reasoning_data):
    """Display user-friendly reasoning steps - CLEAR AND SIMPLE"""

    st.markdown("---")
    st.markdown("## 🧠 **How I Reasoned Through Your Question**")

    if not reasoning_data:
        st.warning("No reasoning data available.")
        st.markdown("---")
        return

    reasoning_type = reasoning_data.get("reasoning_type", "unknown")
    reasoning_steps = reasoning_data.get("reasoning_steps", [])
    confidence = reasoning_data.get("confidence", 0)
    approach_used = reasoning_data.get("approach_used", "Standard reasoning")

    st.markdown(f"**🎯 Your Question:** {reasoning_data.get('user_query', 'Unknown query')}")
    st.markdown("---")

    # Show reasoning approach
    st.markdown("### 🔍 My Reasoning Approach")
    if reasoning_type == "plan_and_execute":
        st.success("✅ I used systematic planning to break down your question")
    elif reasoning_type == "reactive":
        st.info("🔄 I used step-by-step thinking to work through your question")
    elif reasoning_type == "hybrid":
        st.info("🔀 I combined planning and step-by-step thinking")
    else:
        st.info(f"📊 I used {reasoning_type} reasoning")

    # Show reasoning steps in simple language
    if reasoning_steps:
        st.markdown("### 💭 My Thinking Process")
        for i, step in enumerate(reasoning_steps, 1):
            step_type = getattr(step, 'step_type', 'think')
            thought = getattr(step, 'thought', 'Processing...')
            action = getattr(step, 'action', 'Analyzing')
            reasoning = getattr(step, 'reasoning', 'Working through the problem')

            with st.expander(f"Step {i}: {step_type.title()}", expanded=(i <= 3)):
                if step_type == "plan":
                    st.markdown(f"🎯 **Planning:** {thought}")
                elif step_type == "think":
                    st.markdown(f"💭 **Thinking:** {thought}")
                elif step_type == "act":
                    st.markdown(f"⚡ **Action:** {action}")
                elif step_type == "observe":
                    st.markdown(f"👀 **Observation:** {reasoning}")
                else:
                    st.markdown(f"🔍 **{step_type.title()}:** {thought}")

    # Show confidence and approach
    st.markdown("### 📊 Reasoning Summary")
    col1, col2 = st.columns(2)
    with col1:
        confidence_pct = int(confidence * 100) if confidence else 75
        st.metric("Confidence Level", f"{confidence_pct}%")
    with col2:
        st.info(f"**Approach:** {approach_used}")

    # Processing time
    processing_time = reasoning_data.get("processing_time", 0)
    if processing_time:
        st.markdown("---")
        st.info(f"⏱️ **Reasoning time:** {processing_time:.1f} seconds")

    st.markdown("---")

def display_thinking_process(thinking_data):
    """Display combined thinking and planning process - USER FRIENDLY"""

    st.markdown("---")
    st.markdown("## 🧠 **How I Thought About Your Question**")

    if not thinking_data:
        st.warning("No thinking data available.")
        st.markdown("---")
        return

    user_query = thinking_data.get('user_query', 'Unknown query')
    st.markdown(f"**🎯 Your Question:** {user_query}")
    st.markdown("---")

    # Show my thinking process
    st.markdown("### 🔍 My Approach")

    # Check if we have reasoning data
    reasoning_result = thinking_data.get('reasoning_result', {})
    if reasoning_result and reasoning_result.get('reasoning_type') != 'fallback':
        reasoning_type = reasoning_result.get("reasoning_type", "standard")
        if reasoning_type == "plan_and_execute":
            st.info("📋 **Systematic Planning**: I broke your question into steps and worked through them methodically.")
        elif reasoning_type == "react":
            st.info("🔄 **Step-by-Step Thinking**: I thought through your question carefully, learning as I went.")
        elif reasoning_type == "hybrid":
            st.info("🔀 **Combined Approach**: I used both planning and adaptive thinking for your complex question.")
        else:
            st.info("🤔 **Analytical Approach**: I analyzed your question using my financial knowledge.")
    else:
        st.info("🤔 **Standard Analysis**: I used my financial expertise to understand and respond to your question.")

    # Show planning steps if available
    financial_plan = thinking_data.get('financial_plan', {})
    if financial_plan and financial_plan.get('result'):
        st.markdown("### 📋 My Planning Process")
        plan_type = financial_plan.get('type', 'custom')
        if plan_type == 'comprehensive':
            st.markdown("1. 🎯 **Analyzed** your question for complexity")
            st.markdown("2. 📊 **Gathered** relevant financial data")
            st.markdown("3. 🧮 **Created** a comprehensive plan")
            st.markdown("4. ✅ **Validated** the recommendations")
        else:
            st.markdown("1. 👤 **Reviewed** your profile and goals")
            st.markdown("2. 🔍 **Analyzed** your specific question")
            st.markdown("3. 💡 **Developed** personalized recommendations")
            st.markdown("4. 🎯 **Tailored** advice to your situation")

    # Show data sources used
    st.markdown("### 📊 Information I Used")
    data_sources = []

    if thinking_data.get('vector_context'):
        data_sources.append("💡 **Financial Knowledge Base** - My training on financial concepts")

    workflow_result = thinking_data.get('workflow_result', {})
    if workflow_result:
        if 'stock_analysis' in workflow_result:
            data_sources.append("📈 **Yahoo Finance** - Real-time stock and market data")
        if 'economic_analysis' in workflow_result:
            data_sources.append("📊 **Alpha Vantage** - Economic indicators and trends")
        if 'geographic_analysis' in workflow_result:
            data_sources.append("🌍 **Country Data** - Regional economic information")
        if 'market_sentiment' in workflow_result:
            data_sources.append("📰 **Market Sentiment** - Current market mood analysis")

    if thinking_data.get('user_profile'):
        data_sources.append("👤 **Your Profile** - Your age, income, goals, and preferences")

    if data_sources:
        for source in data_sources:
            st.markdown(f"• {source}")
    else:
        st.markdown("• 💡 **My Financial Knowledge** - Built-in expertise and training")

    # Show confidence and timing
    st.markdown("### 📈 Response Quality")
    col1, col2 = st.columns(2)

    with col1:
        confidence = thinking_data.get('confidence', 0.7)
        confidence_pct = int(confidence * 100)
        st.metric("Confidence Level", f"{confidence_pct}%")

    with col2:
        processing_time = thinking_data.get('processing_time', 0)
        if processing_time:
            st.metric("Thinking Time", f"{processing_time:.1f}s")
        else:
            st.metric("Analysis Type", thinking_data.get('analysis_type', 'Standard').title())

    st.markdown("---")

    # Simple Step 1: Understanding Your Question
    st.markdown("### 🔍 Step 1: Understanding Your Question")

    # Get analysis type from thinking data
    analysis_type = thinking_data.get('analysis_type', 'standard')

    if analysis_type == "direct_response":
        st.success("✅ I could answer directly using my financial knowledge")
    elif analysis_type == "needs_tools":
        st.info("🛠️ I needed to get current market data to help you")
    else:
        st.info("📊 I analyzed your question carefully")

    # Simple Step 2: Gathering Information
    st.markdown("### 🧠 Step 2: Gathering Information")
    st.markdown("✅ Checked our conversation history")
    st.markdown("✅ Used your profile preferences")

    # Get vector context from thinking data
    vector_context = thinking_data.get('vector_context', '')
    if vector_context:
        st.markdown("✅ Found relevant financial knowledge")

    if analysis_type == "needs_tools":
        st.markdown("✅ Got current market data")

    # Simple Step 3: Creating Your Response
    st.markdown("### 🎯 Step 3: Creating Your Response")
    st.markdown("✅ Made sure the advice fits your situation")
    st.markdown("✅ Kept the language simple and clear")
    st.markdown("✅ Included practical next steps")

    # Get financial plan from thinking data
    financial_plan = thinking_data.get('financial_plan', {})
    if financial_plan and financial_plan.get("result"):
        st.markdown("✅ Created a personalized financial plan")

    # Simple Step 4: Quality Check
    st.markdown("### 🛡️ Step 4: Quality Check")
    st.markdown("✅ Verified the advice is safe and appropriate")
    st.markdown("✅ Made sure everything is accurate")
    st.markdown("✅ Added important disclaimers")

    # Processing time (simple)
    st.markdown("---")
    processing_time = thinking_data.get('processing_time', 0)
    if processing_time:
        st.info(f"⏱️ **Total time:** {processing_time:.1f} seconds")
    else:
        st.info("⏱️ **Analysis completed**")

    # End of thinking process display
    st.markdown("---")

    st.markdown("---")

# Page config
st.set_page_config(
    page_title="PennyWise - Real Market Data Financial Advisor",
    page_icon="💰",
    layout="wide"
)

# Custom CSS
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .confidence-score {
        background-color: #f0f2f6;
        padding: 10px;
        border-radius: 5px;
        margin: 10px 0;
    }
    .user-profile {
        background-color: #e8f4fd;
        padding: 15px;
        border-radius: 10px;
        margin: 10px 0;
    }

    /* Planning steps button styling */
    .stButton > button {
        border-radius: 8px;
        font-weight: 500;
    }

    /* Planning steps expander styling */
    .streamlit-expanderHeader {
        background-color: #f8f9fa;
        border-radius: 5px;
        font-weight: bold;
    }
</style>
""", unsafe_allow_html=True)

# Initialize session state
if 'user_id' not in st.session_state:
    st.session_state.user_id = str(uuid.uuid4())
    # Start a new session in the memory system
    st.session_state.session_id = memory.start_session(st.session_state.user_id)
    print(f"DEBUG: New user session created with user_id: {st.session_state.user_id}")
else:
    print(f"DEBUG: Existing user session with user_id: {st.session_state.user_id}")

if 'messages' not in st.session_state:
    st.session_state.messages = []

# Header
st.markdown('<h1 class="main-header">💰 PennyWise - AI Financial Advisor</h1>', unsafe_allow_html=True)
st.markdown("*Your Personal Financial Advisor - Always Here to Help! 🌟*")

# Sidebar with user profile and features
with st.sidebar:
    st.header("👤 Your Financial Profile")
    
    user_profile = memory.get_user_profile(st.session_state.user_id)
    
    # User Profile Form
    with st.form("user_profile_form"):
        st.write("**Quick Setup (Optional):**")

        col1, col2 = st.columns(2)
        with col1:
            age = st.number_input("Age", min_value=18, max_value=100, value=user_profile.get("age", 30))
            income = st.number_input("Annual Income ($)", min_value=0, value=user_profile.get("income", 50000), step=5000)

        with col2:
            risk_tolerance = st.selectbox(
                "Risk Tolerance",
                ["Conservative", "Moderate", "Aggressive"],
                index=["conservative", "moderate", "aggressive"].index(user_profile.get("risk_tolerance", "moderate"))
            )

            goals = st.multiselect(
                "Financial Goals",
                ["Retirement", "House Purchase", "Education", "Wealth Building", "Emergency Fund"],
                default=user_profile.get("goals", [])
            )

        if st.form_submit_button("💾 Save Profile"):
            profile_data = {
                "age": age,
                "income": income,
                "risk_tolerance": risk_tolerance.lower(),
                "goals": goals
            }

            # Save to memory system
            memory.save_user_profile(st.session_state.user_id, profile_data)

            # ALSO save to conversation history for immediate context
            profile_summary = f"User updated profile: Age {age}, Income ${income:,}, Risk tolerance {risk_tolerance.lower()}, Goals: {', '.join(goals) if goals else 'None specified'}"
            memory.add_conversation(st.session_state.user_id, "[PROFILE_UPDATE]", profile_summary)

            # Force refresh of user profile in session
            st.session_state.user_profile_updated = True

            st.success("✅ Profile saved! I now know your preferences and will personalize my advice.")
            st.info("💡 Try asking me: 'What's my age?' or 'What are my financial goals?' to test it!")

    # Show current profile if exists
    if user_profile and any(user_profile.values()):
        st.markdown('<div class="user-profile">', unsafe_allow_html=True)
        st.write("**Current Profile:**")
        if user_profile.get("age"):
            st.write(f"🎂 Age: {user_profile['age']}")
        if user_profile.get("income"):
            st.write(f"💰 Income: ${user_profile['income']:,}")
        if user_profile.get("risk_tolerance"):
            st.write(f"⚖️ Risk: {user_profile['risk_tolerance'].title()}")
        if user_profile.get("goals"):
            st.write(f"🎯 Goals: {', '.join(user_profile['goals'])}")
        st.markdown('</div>', unsafe_allow_html=True)
    
    st.markdown("---")
    
    st.header("💡 What I Can Help With")
    st.write("💰 Retirement Planning")
    st.write("🏠 Home Buying")
    st.write("📈 Investing")
    st.write("💳 Debt Management")
    st.write("🎯 Financial Goals")

    st.markdown("---")

    # LLM API Key Management
    st.header("🔑 LLM Configuration")

    # Get current API key info from LLM manager
    key_info = llm_manager.get_current_key_info()

    # Display current status
    if key_info["source"] == "custom":
        st.success(f"🔑 **{key_info['status']}**")
        st.write(f"Key: {key_info['key_preview']}")
    else:
        st.info(f"🔑 **{key_info['status']}**")
        st.write(f"Key: {key_info['key_preview']}")

    with st.form("api_key_form"):
        st.write("**Update LLM API Key:**")
        current_custom_key = st.session_state.get('custom_api_key', '')

        new_api_key = st.text_input(
            "Groq API Key",
            value=current_custom_key,
            placeholder="gsk_... (Enter your Groq API key)",
            help="💡 Get your free API key from: https://console.groq.com/keys"
        )

        col1, col2 = st.columns(2)
        with col1:
            if st.form_submit_button("🔄 Update Key"):
                if new_api_key.strip():
                    # Validate the API key
                    with st.spinner("🔍 Validating API key..."):
                        is_valid, message = llm_manager.validate_api_key(new_api_key.strip())

                    if is_valid:
                        st.session_state.custom_api_key = new_api_key.strip()
                        st.success("✅ Custom API key validated and updated!")
                        st.rerun()
                    else:
                        st.error(f"❌ {message}")
                else:
                    # Clear custom key (use default)
                    if 'custom_api_key' in st.session_state:
                        del st.session_state.custom_api_key
                    st.success("✅ Switched back to default key!")
                    st.rerun()

        with col2:
            if st.form_submit_button("🗑️ Reset to Default"):
                if 'custom_api_key' in st.session_state:
                    del st.session_state.custom_api_key
                st.success("✅ Reset to default API key!")
                st.rerun()

    st.markdown("---")

    # Simple welcome message
    st.info("👋 Hi! I'm PennyWise, your personal financial advisor. Ask me anything!")

    # Action buttons
    col1, col2 = st.columns(2)
    with col1:
        if st.button("🗑️ Clear Chat"):
            memory.clear_conversation_history(st.session_state.user_id)
            st.session_state.messages = []
            st.success("Chat cleared!")

    with col2:
        if st.button("📊 My Financial Journey"):
            try:
                user_profile = memory.get_user_profile(st.session_state.user_id)
                conv_history = memory.get_conversation_history(st.session_state.user_id)

                if user_profile or conv_history:
                    st.markdown("### 📊 Your Financial Journey")

                    # Profile Summary
                    if user_profile and any(user_profile.values()):
                        st.markdown("**👤 Your Profile:**")
                        profile_insights = []
                        if user_profile.get('age'):
                            profile_insights.append(f"• Age {user_profile['age']} - Good time to focus on long-term planning")
                        if user_profile.get('income'):
                            income = user_profile['income']
                            if income >= 100000:
                                profile_insights.append(f"• High income (${income:,}) - Consider tax-advantaged investments")
                            elif income >= 50000:
                                profile_insights.append(f"• Solid income (${income:,}) - Build emergency fund and invest")
                            else:
                                profile_insights.append(f"• Income ${income:,} - Focus on budgeting and emergency savings")
                        if user_profile.get('risk_tolerance'):
                            risk = user_profile['risk_tolerance'].lower()
                            if risk == 'aggressive':
                                profile_insights.append("• Aggressive risk tolerance - Consider growth stocks and ETFs")
                            elif risk == 'moderate':
                                profile_insights.append("• Moderate risk tolerance - Balanced portfolio approach")
                            else:
                                profile_insights.append("• Conservative approach - Focus on bonds and stable investments")
                        if user_profile.get('goals'):
                            goals = user_profile['goals']
                            profile_insights.append(f"• Goals: {', '.join(goals)} - Stay focused on these priorities")

                        for insight in profile_insights:
                            st.markdown(insight)

                    # Conversation Insights
                    if conv_history:
                        st.markdown("**💬 Conversation Insights:**")

                        # Analyze conversation topics
                        conversation_text = " ".join([msg.get('content', '') for msg in conv_history[-10:]])  # Last 10 messages

                        insights = []
                        if 'retirement' in conversation_text.lower():
                            insights.append("• Discussed retirement planning - Keep building that nest egg!")
                        if 'invest' in conversation_text.lower() or 'stock' in conversation_text.lower():
                            insights.append("• Explored investment options - Diversification is key")
                        if 'house' in conversation_text.lower() or 'home' in conversation_text.lower():
                            insights.append("• Talked about home buying - Save for that down payment")
                        if 'debt' in conversation_text.lower() or 'loan' in conversation_text.lower():
                            insights.append("• Addressed debt management - Pay off high-interest debt first")
                        if 'emergency' in conversation_text.lower():
                            insights.append("• Discussed emergency fund - Aim for 3-6 months expenses")
                        if 'budget' in conversation_text.lower():
                            insights.append("• Worked on budgeting - Track your spending patterns")

                        # Feedback insights
                        feedback_messages = [msg for msg in conv_history if '[FEEDBACK]' in msg.get('content', '')]
                        if feedback_messages:
                            positive_feedback = sum(1 for msg in feedback_messages if '👍' in msg.get('content', ''))
                            if positive_feedback > 0:
                                insights.append(f"• You've given {positive_feedback} positive feedback - Great engagement!")

                        if insights:
                            for insight in insights:
                                st.markdown(insight)
                        else:
                            insights.append("• Just getting started - Ask me about your financial goals!")
                            st.markdown(insights[0])

                        # Session stats
                        st.markdown(f"• Total conversations: {len(conv_history)} messages")

                    # Next steps
                    st.markdown("**🎯 Recommended Next Steps:**")
                    next_steps = []

                    if not user_profile or not any(user_profile.values()):
                        next_steps.append("• Complete your profile for personalized advice")

                    if not conv_history or len(conv_history) < 5:
                        next_steps.append("• Ask me about your specific financial goals")

                    if user_profile and user_profile.get('goals'):
                        if 'Retirement Planning' in user_profile['goals']:
                            next_steps.append("• Calculate your retirement savings target")
                        if 'Home Purchase' in user_profile['goals']:
                            next_steps.append("• Determine your home buying budget")
                        if 'Investment Planning' in user_profile['goals']:
                            next_steps.append("• Explore investment portfolio options")

                    if not next_steps:
                        next_steps.append("• Keep asking questions to refine your financial strategy")

                    for step in next_steps:
                        st.markdown(step)

                else:
                    st.info("💡 Fill out your profile and start chatting to build your financial journey!")
            except Exception as e:
                st.error(f"Error loading journey: {e}")
                st.info("💡 Start chatting to build your financial journey!")

    st.markdown("---")

    st.header("💡 Try These")

    example_queries = [
        "Help me plan for retirement",
        "Should I invest in stocks?",
        "How to buy my first house?",
        "Emergency fund advice",
        "Best way to invest $10,000",
        "Investment options for beginners"
    ]
    
    for query in example_queries:
        if st.button(query, key=f"example_{hash(query)}"):
            st.session_state.example_query = query
            st.rerun()

# Main chat interface
st.header("💬 Chat with PennyWise")

# Display chat messages
for message in st.session_state.messages:
    with st.chat_message(message["role"]):
        st.markdown(message["content"])

# Planning & Reasoning Steps Control Panel - PERSISTENT OUTSIDE CHAT
st.markdown("---")
st.markdown("### 📋 Planning & Reasoning Steps Control")

# Show available data
available_plans = [key for key in st.session_state.keys() if key.startswith('planning_data_')]
available_reasoning = [key for key in st.session_state.keys() if key.startswith('reasoning_data_')]

if available_plans or available_reasoning:
    # Response selection
    col1, col2, col3, col4 = st.columns([2, 1.5, 1.5, 1])

    with col1:
        # Dropdown to select which response to show steps for
        all_responses = available_plans + available_reasoning
        if all_responses:
            latest_key = max(all_responses, key=lambda x: int(x.split('_')[-1]))
            selected_response = st.selectbox(
                "Select response to view steps:",
                all_responses,
                index=all_responses.index(latest_key) if latest_key in all_responses else 0,
                format_func=lambda x: f"Response #{x.split('_')[-1]} - {st.session_state[x]['user_query'][:40]}..."
            )

    with col2:
        # Combined thinking process button
        current_showing = st.session_state.get('show_thinking_process', False)
        current_selected = st.session_state.get('current_thinking_selection', '')

        if current_showing and current_selected == selected_response:
            if st.button("🧠 Hide My Thinking", key="hide_thinking_main"):
                st.session_state.show_thinking_process = False
                st.session_state.current_thinking_selection = ''
                st.rerun()
        else:
            if st.button("🧠 Show My Thinking", key="show_thinking_main"):
                st.session_state.show_thinking_process = True
                st.session_state.current_thinking_selection = selected_response
                st.session_state.current_thinking_data = st.session_state[selected_response]
                st.rerun()

    with col3:
        # Clear all data
        if st.button("🗑️ Clear All", key="clear_all_main"):
            for key in list(st.session_state.keys()):
                if key.startswith('planning_data_') or key.startswith('reasoning_data_'):
                    del st.session_state[key]
            st.session_state.show_thinking_process = False
            st.rerun()
else:
    st.info("💡 Ask me a question to see planning and reasoning steps!")

# Always show chat input
prompt = st.chat_input("Ask me anything about your finances! 💰")

# Handle example query selection
if hasattr(st.session_state, 'example_query') and st.session_state.example_query:
    prompt = st.session_state.example_query
    del st.session_state.example_query

# Process user input
if prompt:
    # Validate user input for safety
    input_valid, input_warnings, flags, empathetic_responses = financial_guardrails.validate_user_input(prompt)

    if not input_valid:
        # Add user message to chat history first
        st.session_state.messages.append({"role": "user", "content": prompt})
        with st.chat_message("user"):
            st.markdown(prompt)

        # Show flags in warning boxes (short)
        if flags:
            for flag in flags:
                st.warning(flag)
        else:
            # Fallback to old warning format
            st.warning("⚠️ " + " ".join(input_warnings))

        # Show empathetic responses in chat (normal conversation)
        if empathetic_responses:
            for empathetic_response in empathetic_responses:
                with st.chat_message("assistant"):
                    st.markdown(empathetic_response)
                st.session_state.messages.append({"role": "assistant", "content": empathetic_response})
        else:
            # Fallback to safe response
            with st.chat_message("assistant"):
                safe_response = financial_guardrails.get_safe_fallback_response(prompt)
                st.markdown(safe_response)
                st.session_state.messages.append({"role": "assistant", "content": safe_response})
            memory.add_conversation(st.session_state.user_id, prompt, safe_response)
        st.stop()

    # Add user message to chat history
    st.session_state.messages.append({"role": "user", "content": prompt})
    with st.chat_message("user"):
        st.markdown(prompt)

    # Get response from LangGraph workflow (LLM decides everything)
    with st.chat_message("assistant"):
        with st.spinner("🤔 Let me think about that..."):
            start_time = time.time()

            try:
                print(f"DEBUG: Processing query for user {st.session_state.user_id}: {prompt}")
                result = enhanced_workflow.process_query(st.session_state.user_id, prompt)
                response = result["response"]
                confidence = result["confidence"]
                analysis_type = result["analysis_type"]
                vector_context = result.get("vector_context", "")
                financial_plan = result.get("financial_plan", {})
                reasoning_result = result.get("reasoning_result", {})

                processing_time = time.time() - start_time

                st.markdown(response)

                # Store the complete interaction in short-term memory (without feedback initially)
                memory.store_interaction_with_feedback(st.session_state.user_id, prompt, response)

                # Store planning data for this response (always available)
                planning_data = {
                    "user_query": prompt,
                    "workflow_result": result,
                    "response": response,
                    "confidence": confidence,
                    "analysis_type": analysis_type,
                    "processing_time": processing_time,
                    "vector_context": vector_context,
                    "financial_plan": financial_plan
                }

                # Store in session state with unique key for this message
                message_id = len(st.session_state.messages)
                st.session_state[f'planning_data_{message_id}'] = planning_data
                print(f"DEBUG: Stored planning data for message {message_id}")  # Debug line
                print(f"DEBUG: Planning data keys: {list(planning_data.keys())}")  # Debug line

                # Store reasoning data if available - WITH DEBUGGING
                print(f"DEBUG: reasoning_result = {reasoning_result}")  # Debug line
                if reasoning_result and reasoning_result.get("reasoning_type") != "fallback":
                    reasoning_data = {
                        "user_query": prompt,
                        "reasoning_type": reasoning_result.get("reasoning_type", "unknown"),
                        "reasoning_steps": reasoning_result.get("reasoning_steps", []),
                        "confidence": reasoning_result.get("confidence", confidence),
                        "approach_used": reasoning_result.get("approach_used", "Standard reasoning"),
                        "final_reasoning": reasoning_result.get("final_reasoning", ""),
                        "processing_time": processing_time
                    }
                    st.session_state[f'reasoning_data_{message_id}'] = reasoning_data
                    print(f"DEBUG: Stored reasoning data for message {message_id}")  # Debug line
                else:
                    print(f"DEBUG: No reasoning data to store - reasoning_result: {reasoning_result}")  # Debug line

                # Show planning button if plan was created
                with col2:
                    if financial_plan and financial_plan.get("result"):
                        if st.button("📋 View My Plan", key=f"plan_{len(st.session_state.messages)}"):
                            plan_result = financial_plan["result"]
                            if isinstance(plan_result, dict):
                                plan_text = "\n".join([f"**{key.title()}:** {value}" for key, value in plan_result.items()])
                            else:
                                plan_text = str(plan_result)
                            st.success(f"📋 **Your Financial Plan:**\n\n{plan_text}")

                # Add to chat history and memory
                st.session_state.messages.append({"role": "assistant", "content": response})
                memory.add_conversation(st.session_state.user_id, prompt, response)

                # Add feedback collection UI - ENHANCED WITH THUMBS UP/DOWN
                st.markdown("---")
                st.markdown("### 💬 How was my response?")

                # Quick thumbs up/down feedback
                col1, col2, col3, col4 = st.columns([1, 1, 2, 1])

                with col1:
                    thumbs_up_key = f"thumbs_up_{message_id}"
                    if st.button("👍 Good", key=thumbs_up_key):
                        # Store structured feedback in short-term memory
                        feedback_text = "User rated response as GOOD (👍). Response was helpful and accurate. Continue with this approach for similar questions."
                        print(f"DEBUG: Storing positive feedback for user {st.session_state.user_id}")
                        memory.store_feedback(st.session_state.user_id, prompt, response, feedback_text, 4)

                        # Also store in session state for immediate access
                        if 'session_feedback' not in st.session_state:
                            st.session_state.session_feedback = []
                        st.session_state.session_feedback.append({
                            "type": "positive",
                            "query": prompt,
                            "response": response[:100] + "...",
                            "timestamp": time.time(),
                            "message": "User found this response helpful and accurate"
                        })

                        # Collect feedback for analytics
                        feedback_collector.collect_response_feedback(
                            user_id=st.session_state.user_id,
                            session_id=st.session_state.session_id,
                            query=prompt,
                            response=response,
                            rating=4,  # Thumbs up = 4 stars
                            comment="Positive feedback (thumbs up)",
                            response_time=processing_time
                        )
                        st.success("👍 Thanks! I'll remember this worked well for you.")

                with col2:
                    thumbs_down_key = f"thumbs_down_{message_id}"
                    if st.button("👎 Poor", key=thumbs_down_key):
                        # Store structured feedback in short-term memory
                        feedback_text = "User rated response as POOR (👎). Response was not helpful or accurate. IMPORTANT: Adjust approach, provide more detailed explanations, and be more careful with similar questions."
                        print(f"DEBUG: Storing negative feedback for user {st.session_state.user_id}")
                        memory.store_feedback(st.session_state.user_id, prompt, response, feedback_text, 2)

                        # Also store in session state for immediate access
                        if 'session_feedback' not in st.session_state:
                            st.session_state.session_feedback = []
                        st.session_state.session_feedback.append({
                            "type": "negative",
                            "query": prompt,
                            "response": response[:100] + "...",
                            "timestamp": time.time(),
                            "message": "User found this response unhelpful - need to improve approach"
                        })

                        # Collect feedback for analytics
                        feedback_collector.collect_response_feedback(
                            user_id=st.session_state.user_id,
                            session_id=st.session_state.session_id,
                            query=prompt,
                            response=response,
                            rating=2,  # Thumbs down = 2 stars
                            comment="Negative feedback (thumbs down)",
                            response_time=processing_time
                        )
                        st.error("👎 I understand. I'll adjust my approach for better responses.")

                with col3:
                    # Optional comment feedback
                    with st.expander("💬 Add Comment"):
                        # Feedback comment only
                        comment_key = f"feedback_comment_{message_id}"
                        comment = st.text_area(
                            "Tell me more about this response:",
                            placeholder="What did you like or what could be improved?",
                            key=comment_key,
                            height=80
                        )

                        # Submit comment feedback
                        submit_key = f"submit_feedback_{message_id}"
                        if st.button("💬 Submit Comment", key=submit_key) and comment.strip():
                            # Store structured comment feedback in short-term memory
                            feedback_text = f"User provided detailed feedback: '{comment}'. IMPORTANT: Use this specific feedback to improve future responses."
                            memory.store_feedback(st.session_state.user_id, prompt, response, feedback_text, 3)

                            # Store in session state for immediate access
                            if 'session_feedback' not in st.session_state:
                                st.session_state.session_feedback = []
                            st.session_state.session_feedback.append({
                                "type": "comment",
                                "comment": comment,
                                "query": prompt,
                                "response": response[:100] + "...",
                                "timestamp": time.time(),
                                "message": f"User comment: {comment}"
                            })

                            # Collect feedback (using rating 3 for comments)
                            feedback_result = feedback_collector.collect_response_feedback(
                                user_id=st.session_state.user_id,
                                session_id=st.session_state.session_id,
                                query=prompt,
                                response=response,
                                rating=3,  # Neutral rating for comments
                                comment=comment,
                                response_time=processing_time,
                                context={
                                    "analysis_type": analysis_type,
                                    "confidence": confidence,
                                    "has_plan": bool(financial_plan and financial_plan.get("result")),
                                    "vector_context_length": len(vector_context) if vector_context else 0
                                }
                            )
                            st.success("💬 Thank you for your comment! I'll keep this in mind for future responses.")

                with col4:
                    # Show current feedback stats
                    if st.button("📊 Stats", key=f"stats_{message_id}"):
                        stats = feedback_collector.get_feedback_summary()["stats"]
                        if stats["total_feedback"] > 0:
                            st.info(f"📊 Avg: {stats['average_rating']:.1f}/5 ({stats['total_feedback']} reviews)")
                        else:
                            st.info("📊 No feedback yet")

                # Ensure chat input is ready for next interaction
                if hasattr(st.session_state, 'example_query'):
                    if st.session_state.example_query:
                        del st.session_state.example_query

            except Exception as e:
                st.error(f"❌ Error processing your request: {str(e)}")
                response = "I apologize, but I encountered an error processing your request. Please try again or rephrase your question."
                st.session_state.messages.append({"role": "assistant", "content": response})

                # Clean up any temporary states
                if hasattr(st.session_state, 'example_query'):
                    if st.session_state.example_query:
                        del st.session_state.example_query

# Show thinking process if requested - STABLE LOCATION AT END
if st.session_state.get('show_thinking_process', False):
    thinking_data = st.session_state.get('current_thinking_data', {})

    if thinking_data:
        # Display the thinking process in an expandable container
        with st.container():
            display_thinking_process(thinking_data)
    else:
        st.error("❌ No thinking data found in session state")
        # Reset button
        if st.button("🔄 Reset Thinking Process", key="reset_thinking"):
            st.session_state.show_thinking_process = False
            st.rerun()

# Footer
st.markdown("---")
st.markdown("*💙 Making Financial Planning Simple and Personal*")