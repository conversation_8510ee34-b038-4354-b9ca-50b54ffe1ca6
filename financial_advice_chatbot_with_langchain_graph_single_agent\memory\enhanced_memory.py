from typing import Dict, List, Any, Optional
import json
from datetime import datetime
import os
import uuid

# Lang<PERSON>hain Built-in Memory Imports
from langchain.schema import BaseMessage, HumanMessage, AIMessage
from langchain_groq import ChatGroq
from dotenv import load_dotenv

# Import our memory systems
from .short_term_memory import short_term_memory
from .semantic_memory import semantic_memory
from .behavioral_memory import behavioral_memory
from .episodic_memory import episodic_memory
from .long_term_memory import long_term_memory

load_dotenv()

class LangChainEnhancedMemory:
    """
    Unified Memory System coordinating four types of memory:
    - Short-Term Memory: Redis + LangChain (conversation history)
    - Semantic Memory: Qdrant (financial knowledge, concepts)
    - Behavioral Memory: MongoDB (user patterns, preferences)
    - Episodic Memory: MongoDB (conversations, experiences)
    """

    def __init__(self):
        # Initialize LLM for memory summarization
        self.llm = ChatGroq(
            model="llama3-8b-8192",
            api_key=os.getenv("GROQ_API_KEY"),
            temperature=0.1
        )

        # Current session tracking
        self.current_sessions = {}  # user_id -> session_id

        # Memory system references
        self.short_term = short_term_memory
        self.semantic = semantic_memory
        self.behavioral = behavioral_memory
        self.episodic = episodic_memory
        self.long_term = long_term_memory

        print("✅ Unified Memory System initialized with Short-Term (Redis) + Semantic (Qdrant) + Behavioral/Episodic (MongoDB)")

    def start_session(self, user_id: str) -> str:
        """Start a new session and return session_id"""
        session_id = str(uuid.uuid4())
        self.current_sessions[user_id] = session_id

        # Start episodic memory episode
        self.episodic.start_conversation_episode(user_id, session_id)

        # Ensure behavioral profile exists
        self.behavioral.create_user_behavior_profile(user_id)

        return session_id

    def get_conversation_memory(self, user_id: str):
        """Get conversation memory from short-term memory system"""
        return self.short_term.get_conversation_memory(user_id)

    def save_user_profile(self, user_id: str, profile: Dict[str, Any]):
        """Save user profile to behavioral memory (MongoDB)"""
        try:
            # Create or update behavioral profile
            behavior_profile = self.behavioral.get_user_behavior_profile(user_id)
            if not behavior_profile:
                behavior_profile = self.behavioral.create_user_behavior_profile(user_id, profile)
            else:
                # Update existing profile
                for key, value in profile.items():
                    if hasattr(behavior_profile, key):
                        setattr(behavior_profile, key, value)
                behavior_profile.updated_at = datetime.utcnow()
                behavior_profile.save()

            print(f"✅ Saved user profile to behavioral memory: {user_id}")

        except Exception as e:
            print(f"⚠️ Error saving user profile: {e}")

    def get_user_profile(self, user_id: str) -> Dict[str, Any]:
        """Get user profile from behavioral memory"""
        try:
            behavior_profile = self.behavioral.get_user_behavior_profile(user_id)
            if behavior_profile:
                return {
                    'age': getattr(behavior_profile, 'age', None),
                    'income': getattr(behavior_profile, 'income', None),
                    'risk_tolerance': behavior_profile.preferences.risk_tolerance if behavior_profile.preferences else 'moderate',
                    'goals': behavior_profile.primary_goals,
                    'decision_style': behavior_profile.decision_making_style,
                    'last_updated': behavior_profile.updated_at.isoformat() if behavior_profile.updated_at else None
                }
            return {}
        except Exception as e:
            print(f"⚠️ Error getting user profile: {e}")
            return {}

    def add_conversation(self, user_id: str, user_message: str, assistant_message: str,
                        intent: str = None, confidence: float = None):
        """Add conversation to both short-term (Redis) and episodic (MongoDB) memory"""

        # Add to short-term memory (Redis)
        self.short_term.add_conversation(user_id, user_message, assistant_message)

        # Add to episodic memory (MongoDB) for long-term storage
        session_id = self.current_sessions.get(user_id)
        if session_id:
            # Add user turn
            self.episodic.add_conversation_turn(
                session_id=session_id,
                speaker="user",
                message=user_message,
                intent=intent
            )

            # Add assistant turn
            self.episodic.add_conversation_turn(
                session_id=session_id,
                speaker="assistant",
                message=assistant_message,
                confidence=confidence
            )

    def get_conversation_history(self, user_id: str) -> str:
        """Get conversation history from short-term memory"""
        return self.short_term.get_conversation_history(user_id)

    def get_conversation_messages(self, user_id: str) -> List[BaseMessage]:
        """Get conversation messages from short-term memory"""
        return self.short_term.get_conversation_messages(user_id)

    def get_user_feedback_history(self, user_id: str, limit: int = 5) -> List[str]:
        """Get user's feedback history"""
        try:
            history = self.get_conversation_history(user_id)
            if not history:
                return []

            # Extract feedback messages
            feedback_messages = []
            lines = history.split('\n') if isinstance(history, str) else []

            for line in lines:
                if '[FEEDBACK]' in line or 'FEEDBACK' in line:
                    feedback_messages.append(line.strip())
                    if len(feedback_messages) >= limit:
                        break

            return feedback_messages
        except Exception as e:
            print(f"Error getting feedback history: {e}")
            return []

    def store_feedback(self, user_id: str, query: str, response: str, feedback: str, rating: int):
        """Store user feedback in memory with query, response, and feedback"""
        try:
            print(f"DEBUG: Storing feedback for user {user_id}")
            print(f"DEBUG: Query: {query}")
            print(f"DEBUG: Feedback: {feedback}")
            print(f"DEBUG: Rating: {rating}")

            # Use the simple feedback method
            self.short_term.add_feedback(user_id, query, response, feedback, rating)

            print(f"DEBUG: Successfully stored feedback for user {user_id}")

            # DEBUG: Check what's in conversation history after storing
            history = self.get_conversation_history(user_id)
            print(f"DEBUG: Conversation history after storing feedback (length: {len(history) if history else 0})")
            if history and len(history) > 0:
                print(f"DEBUG: Last 500 chars of history: {history[-500:]}")

        except Exception as e:
            print(f"Error storing feedback: {e}")
            import traceback
            traceback.print_exc()

    def store_interaction_with_feedback(self, user_id: str, query: str, response: str, feedback: str = None, rating: int = None):
        """Store complete interaction including optional feedback"""
        try:
            # Store the main interaction using the correct method signature
            self.add_conversation(user_id, query, response)

            # Store feedback if provided
            if feedback and rating:
                self.store_feedback(user_id, query, response, feedback, rating)
            elif feedback:  # Comment without rating
                self.store_feedback(user_id, query, response, feedback, 3)  # Neutral rating for comments

            print(f"DEBUG: Stored complete interaction for user {user_id}")
        except Exception as e:
            print(f"Error storing interaction: {e}")
            import traceback
            traceback.print_exc()

    def clear_conversation_history(self, user_id: str):
        """Clear conversation history for user"""
        self.short_term.clear_conversation_history(user_id)

    def end_session(self, user_id: str, summary: str = None, satisfaction: float = None):
        """End current session and finalize episodic memory"""
        session_id = self.current_sessions.get(user_id)
        if session_id:
            self.episodic.end_conversation_episode(session_id, summary, satisfaction)
            del self.current_sessions[user_id]

    def get_comprehensive_context(self, user_id: str, query: str) -> Dict[str, Any]:
        """Get comprehensive context from all three memory systems"""
        context = {
            'semantic': {},
            'behavioral': {},
            'episodic': {}
        }

        try:
            # Semantic context (financial knowledge)
            semantic_context = self.semantic.get_financial_context(query)
            context['semantic'] = {
                'relevant_knowledge': semantic_context,
                'knowledge_available': bool(semantic_context)
            }

            # Behavioral context (user patterns)
            behavioral_insights = self.behavioral.get_behavioral_insights(user_id)
            context['behavioral'] = behavioral_insights

            # Episodic context (recent experiences)
            recent_conversations = self.episodic.get_recent_conversations(user_id, days=7)
            relevant_experiences = self.episodic.get_relevant_experiences(user_id, query)

            context['episodic'] = {
                'recent_conversations_count': len(recent_conversations),
                'recent_experiences': [exp.title for exp in relevant_experiences[:3]],
                'last_interaction': recent_conversations[0].start_time.isoformat() if recent_conversations else None
            }

            # Long-term context (complete user profile)
            long_term_summary = self.long_term.get_user_summary(user_id)
            context['long_term'] = long_term_summary

        except Exception as e:
            context['error'] = str(e)

        return context

    def record_financial_decision(self, user_id: str, decision_type: str,
                                amount: float, reasoning: str, success: bool = True):
        """Record a financial decision across behavioral and episodic memory"""
        try:
            # Update behavioral patterns
            self.behavioral.update_decision_pattern(
                user_id=user_id,
                decision_type=decision_type,
                amount=amount,
                conditions={'reasoning': reasoning},
                success=success
            )

            # Add to episodic memory as experience
            self.episodic.add_financial_experience(
                user_id=user_id,
                experience_type=decision_type,
                title=f"{decision_type.title()} Decision",
                description=reasoning,
                financial_impact=amount
            )

        except Exception as e:
            print(f"⚠️ Error recording financial decision: {e}")

    def get_memory_stats(self, user_id: str) -> Dict[str, Any]:
        """Get statistics from all memory systems"""
        try:
            stats = {
                'semantic': self.semantic.get_collection_stats(),
                'behavioral': self.behavioral.get_behavioral_insights(user_id),
                'episodic': self.episodic.get_episode_summary(user_id),
                'redis_active': self.use_redis,
                'current_session': self.current_sessions.get(user_id)
            }
            return stats
        except Exception as e:
            return {'error': str(e)}
    
    def extract_user_info(self, query: str, current_profile: Dict[str, Any]) -> Dict[str, Any]:
        """Enhanced extraction of user information from query"""
        profile = current_profile.copy()
        query_lower = query.lower()
        
        # Extract age
        age_patterns = ["i'm ", "i am ", "age ", "years old"]
        for pattern in age_patterns:
            if pattern in query_lower:
                words = query_lower.split()
                for i, word in enumerate(words):
                    if pattern.strip() in word and i + 1 < len(words):
                        try:
                            age = int(words[i + 1])
                            if 18 <= age <= 100:
                                profile["age"] = age
                                break
                        except ValueError:
                            continue
        
        # Extract income with better patterns
        income_patterns = ["earn", "make", "income", "salary"]
        for pattern in income_patterns:
            if pattern in query_lower:
                words = query_lower.replace("$", "").replace(",", "").split()
                for i, word in enumerate(words):
                    if pattern in word:
                        # Look for numbers after income keywords
                        for j in range(i + 1, min(i + 4, len(words))):
                            if j < len(words):
                                if words[j].endswith("k"):
                                    try:
                                        income = int(words[j][:-1]) * 1000
                                        profile["income"] = income
                                        break
                                    except ValueError:
                                        continue
                                elif words[j].isdigit() and len(words[j]) >= 4:
                                    try:
                                        income = int(words[j])
                                        if income >= 10000:
                                            profile["income"] = income
                                            break
                                    except ValueError:
                                        continue
        
        # Extract investment goals
        if "retirement" in query_lower or "retire" in query_lower:
            profile["goals"] = profile.get("goals", [])
            if "retirement" not in profile["goals"]:
                profile["goals"].append("retirement")
        
        if "house" in query_lower or "home" in query_lower:
            profile["goals"] = profile.get("goals", [])
            if "home_purchase" not in profile["goals"]:
                profile["goals"].append("home_purchase")
        
        # Extract risk preferences
        if any(word in query_lower for word in ["conservative", "safe", "low risk"]):
            profile["risk_tolerance"] = "conservative"
        elif any(word in query_lower for word in ["aggressive", "high risk", "growth"]):
            profile["risk_tolerance"] = "aggressive"
        elif "moderate" in query_lower:
            profile["risk_tolerance"] = "moderate"
        
        # Extract geographic interests
        geographic_mentions = []
        regions = ["europe", "european", "asia", "asian", "america", "us", "international", "global"]
        for region in regions:
            if region in query_lower:
                geographic_mentions.append(region)
        
        if geographic_mentions:
            profile["geographic_interests"] = list(set(profile.get("geographic_interests", []) + geographic_mentions))
        
        return profile
    
    def save_market_context(self, context_type: str, data: Dict[str, Any]):
        """Save market context for reference"""
        self.market_context[context_type] = {
            "data": data,
            "timestamp": datetime.now().isoformat()
        }
    
    def get_market_context(self, context_type: str) -> Dict[str, Any]:
        """Get saved market context"""
        return self.market_context.get(context_type, {})

    # Long-Term Memory Methods
    def update_long_term_profile(self, user_id: str, profile_data: Dict[str, Any]):
        """Update long-term user profile with any new information"""
        try:
            # Update personal info if provided
            if 'personal_info' in profile_data:
                self.long_term.update_personal_info(user_id, profile_data['personal_info'])

            # Update financial profile if provided
            if 'financial_profile' in profile_data:
                self.long_term.update_financial_profile(user_id, profile_data['financial_profile'])

            # Update behavioral patterns if provided
            if 'behavioral_patterns' in profile_data:
                self.long_term.update_behavioral_patterns(user_id, profile_data['behavioral_patterns'])

        except Exception as e:
            print(f"Error updating long-term profile: {e}")

    def add_life_event(self, user_id: str, event_type: str, description: str,
                      financial_impact: str = "", emotional_context: str = "",
                      related_goals: List[str] = None):
        """Add a significant life event to long-term memory"""
        from .long_term_memory import UserLifeEvent
        from datetime import datetime, timezone

        if related_goals is None:
            related_goals = []

        event = UserLifeEvent(
            event_type=event_type,
            description=description,
            date=datetime.now(timezone.utc),
            financial_impact=financial_impact,
            emotional_context=emotional_context,
            related_goals=related_goals
        )

        self.long_term.add_life_event(user_id, event)

    def add_financial_milestone(self, user_id: str, milestone_type: str, amount: float,
                               description: str, lessons_learned: str = "",
                               next_steps: List[str] = None):
        """Add a financial milestone to long-term memory"""
        from .long_term_memory import FinancialMilestone
        from datetime import datetime, timezone

        if next_steps is None:
            next_steps = []

        milestone = FinancialMilestone(
            milestone_type=milestone_type,
            amount=amount,
            description=description,
            date=datetime.now(timezone.utc),
            lessons_learned=lessons_learned,
            next_steps=next_steps
        )

        self.long_term.add_financial_milestone(user_id, milestone)

    def get_complete_user_profile(self, user_id: str) -> Dict[str, Any]:
        """Get the complete long-term memory for a user"""
        return self.long_term.get_complete_user_profile(user_id)

    def search_user_history(self, user_id: str, query: str) -> List[Dict[str, Any]]:
        """Search through all user data for specific information"""
        return self.long_term.search_user_history(user_id, query)

    def get_user_analytics(self, user_id: str) -> Dict[str, Any]:
        """Get long-term analytics for the user"""
        return self.long_term.get_user_analytics(user_id)

# Global memory instance
memory = LangChainEnhancedMemory()