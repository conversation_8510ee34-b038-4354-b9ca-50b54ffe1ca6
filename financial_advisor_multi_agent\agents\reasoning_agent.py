"""
Reasoning Agent - Logic and Decision Making
Handles complex reasoning, analysis, and decision support
"""

import asyncio
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime
from enum import Enum

logger = logging.getLogger(__name__)

class ReasoningType(Enum):
    """Types of reasoning approaches"""
    ANALYTICAL = "analytical"
    COMPARATIVE = "comparative"
    CAUSAL = "causal"
    PROBABILISTIC = "probabilistic"
    STRATEGIC = "strategic"

class ReasoningAgent:
    """Agent responsible for logical reasoning and analysis"""
    
    def __init__(self):
        self.is_initialized = False
        
    async def initialize(self):
        """Initialize the reasoning agent"""
        try:
            logger.info("Initializing Reasoning Agent")
            
            self.is_initialized = True
            logger.info("✅ Reasoning Agent initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize Reasoning Agent: {e}")
            raise
    
    async def analyze_query(self, user_id: str, query: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze query and provide reasoning"""
        try:
            # Determine reasoning type needed
            reasoning_type = self._determine_reasoning_type(query)
            
            # Perform reasoning based on type
            reasoning_steps = []
            confidence = 0.7
            
            if reasoning_type == ReasoningType.ANALYTICAL:
                reasoning_steps, confidence = await self._analytical_reasoning(query, context)
            elif reasoning_type == ReasoningType.COMPARATIVE:
                reasoning_steps, confidence = await self._comparative_reasoning(query, context)
            elif reasoning_type == ReasoningType.CAUSAL:
                reasoning_steps, confidence = await self._causal_reasoning(query, context)
            elif reasoning_type == ReasoningType.PROBABILISTIC:
                reasoning_steps, confidence = await self._probabilistic_reasoning(query, context)
            elif reasoning_type == ReasoningType.STRATEGIC:
                reasoning_steps, confidence = await self._strategic_reasoning(query, context)
            else:
                reasoning_steps, confidence = await self._general_reasoning(query, context)
            
            return {
                "reasoning_type": reasoning_type.value,
                "reasoning_steps": reasoning_steps,
                "confidence": confidence,
                "approach_used": f"{reasoning_type.value.title()} reasoning",
                "analysis_time": datetime.now().isoformat(),
                "query_complexity": self._assess_complexity(query)
            }
            
        except Exception as e:
            logger.error(f"Failed to analyze query: {e}")
            return {
                "reasoning_type": "fallback",
                "reasoning_steps": [],
                "confidence": 0.3,
                "error": str(e)
            }
    
    def _determine_reasoning_type(self, query: str) -> ReasoningType:
        """Determine the type of reasoning needed"""
        query_lower = query.lower()
        
        # Comparative reasoning
        if any(word in query_lower for word in ['vs', 'versus', 'compare', 'better', 'best', 'which', 'or']):
            return ReasoningType.COMPARATIVE
        
        # Causal reasoning
        if any(word in query_lower for word in ['why', 'because', 'cause', 'reason', 'impact', 'effect']):
            return ReasoningType.CAUSAL
        
        # Probabilistic reasoning
        if any(word in query_lower for word in ['risk', 'chance', 'probability', 'likely', 'might', 'could']):
            return ReasoningType.PROBABILISTIC
        
        # Strategic reasoning
        if any(word in query_lower for word in ['strategy', 'plan', 'approach', 'should', 'recommend', 'advice']):
            return ReasoningType.STRATEGIC
        
        # Default to analytical
        return ReasoningType.ANALYTICAL
    
    async def _analytical_reasoning(self, query: str, context: Dict[str, Any]) -> tuple[List[Dict[str, Any]], float]:
        """Perform analytical reasoning"""
        steps = []
        
        steps.append({
            "step_type": "analysis",
            "thought": "Breaking down the financial question into key components",
            "action": "Identifying main financial concepts and user context"
        })
        
        # Analyze user profile
        user_profile = context.get('user_profile', {})
        if user_profile:
            steps.append({
                "step_type": "context",
                "thought": f"Considering user profile: age {user_profile.get('age', 'unknown')}, income ${user_profile.get('income', 'unknown'):,}, risk tolerance {user_profile.get('risk_tolerance', 'unknown')}",
                "action": "Personalizing analysis based on user situation"
            })
        
        # Analyze query components
        query_lower = query.lower()
        financial_topics = []
        
        if any(word in query_lower for word in ['invest', 'investment', 'stock', 'bond']):
            financial_topics.append("investment planning")
        if any(word in query_lower for word in ['retire', 'retirement']):
            financial_topics.append("retirement planning")
        if any(word in query_lower for word in ['budget', 'save', 'expense']):
            financial_topics.append("budgeting")
        
        if financial_topics:
            steps.append({
                "step_type": "categorization",
                "thought": f"This question involves: {', '.join(financial_topics)}",
                "action": "Applying relevant financial principles and best practices"
            })
        
        steps.append({
            "step_type": "synthesis",
            "thought": "Combining financial knowledge with user context to provide personalized advice",
            "action": "Formulating comprehensive response with actionable recommendations"
        })
        
        confidence = 0.8 if user_profile else 0.6
        return steps, confidence
    
    async def _comparative_reasoning(self, query: str, context: Dict[str, Any]) -> tuple[List[Dict[str, Any]], float]:
        """Perform comparative reasoning"""
        steps = []
        
        steps.append({
            "step_type": "identification",
            "thought": "Identifying the options or alternatives being compared",
            "action": "Extracting comparison subjects from the query"
        })
        
        steps.append({
            "step_type": "criteria",
            "thought": "Establishing comparison criteria based on financial best practices",
            "action": "Considering factors like risk, return, liquidity, tax implications, and fees"
        })
        
        user_profile = context.get('user_profile', {})
        if user_profile:
            risk_tolerance = user_profile.get('risk_tolerance', 'moderate')
            steps.append({
                "step_type": "personalization",
                "thought": f"Weighing options based on user's {risk_tolerance} risk tolerance",
                "action": "Adjusting recommendations to match user preferences and situation"
            })
        
        steps.append({
            "step_type": "evaluation",
            "thought": "Evaluating each option against the established criteria",
            "action": "Providing pros and cons for each alternative"
        })
        
        steps.append({
            "step_type": "recommendation",
            "thought": "Determining the best option based on analysis",
            "action": "Making clear recommendation with reasoning"
        })
        
        confidence = 0.75
        return steps, confidence
    
    async def _causal_reasoning(self, query: str, context: Dict[str, Any]) -> tuple[List[Dict[str, Any]], float]:
        """Perform causal reasoning"""
        steps = []
        
        steps.append({
            "step_type": "cause_identification",
            "thought": "Identifying the underlying causes or factors in the financial situation",
            "action": "Analyzing root causes rather than just symptoms"
        })
        
        steps.append({
            "step_type": "chain_analysis",
            "thought": "Tracing the cause-and-effect relationships in financial decisions",
            "action": "Understanding how financial choices lead to specific outcomes"
        })
        
        steps.append({
            "step_type": "impact_assessment",
            "thought": "Evaluating the potential impacts and consequences",
            "action": "Considering both short-term and long-term effects"
        })
        
        steps.append({
            "step_type": "solution_mapping",
            "thought": "Connecting causes to appropriate solutions and interventions",
            "action": "Recommending actions that address root causes"
        })
        
        confidence = 0.7
        return steps, confidence
    
    async def _probabilistic_reasoning(self, query: str, context: Dict[str, Any]) -> tuple[List[Dict[str, Any]], float]:
        """Perform probabilistic reasoning"""
        steps = []
        
        steps.append({
            "step_type": "risk_identification",
            "thought": "Identifying potential risks and uncertainties in the financial scenario",
            "action": "Cataloging known and unknown risk factors"
        })
        
        steps.append({
            "step_type": "probability_assessment",
            "thought": "Estimating likelihood of different outcomes based on historical data and trends",
            "action": "Using financial market history and statistical analysis"
        })
        
        user_profile = context.get('user_profile', {})
        risk_tolerance = user_profile.get('risk_tolerance', 'moderate') if user_profile else 'moderate'
        
        steps.append({
            "step_type": "risk_tolerance",
            "thought": f"Considering user's {risk_tolerance} risk tolerance in probability assessment",
            "action": "Adjusting recommendations based on user's comfort with uncertainty"
        })
        
        steps.append({
            "step_type": "scenario_planning",
            "thought": "Developing scenarios for best case, worst case, and most likely outcomes",
            "action": "Preparing recommendations for different probability scenarios"
        })
        
        confidence = 0.65  # Lower confidence due to uncertainty inherent in probabilistic reasoning
        return steps, confidence
    
    async def _strategic_reasoning(self, query: str, context: Dict[str, Any]) -> tuple[List[Dict[str, Any]], float]:
        """Perform strategic reasoning"""
        steps = []
        
        steps.append({
            "step_type": "goal_clarification",
            "thought": "Clarifying the user's financial goals and objectives",
            "action": "Understanding what the user wants to achieve"
        })
        
        steps.append({
            "step_type": "resource_assessment",
            "thought": "Evaluating available resources and constraints",
            "action": "Considering income, time horizon, existing assets, and limitations"
        })
        
        steps.append({
            "step_type": "strategy_development",
            "thought": "Developing a strategic approach to achieve the financial goals",
            "action": "Creating a logical sequence of actions and decisions"
        })
        
        steps.append({
            "step_type": "implementation_planning",
            "thought": "Planning the practical implementation of the strategy",
            "action": "Breaking down strategy into actionable steps with timelines"
        })
        
        steps.append({
            "step_type": "monitoring_framework",
            "thought": "Establishing how to monitor progress and adjust strategy",
            "action": "Setting up review points and success metrics"
        })
        
        confidence = 0.8
        return steps, confidence
    
    async def _general_reasoning(self, query: str, context: Dict[str, Any]) -> tuple[List[Dict[str, Any]], float]:
        """Perform general reasoning"""
        steps = []
        
        steps.append({
            "step_type": "understanding",
            "thought": "Understanding the core financial question being asked",
            "action": "Parsing the query for key financial concepts and user intent"
        })
        
        steps.append({
            "step_type": "knowledge_application",
            "thought": "Applying relevant financial knowledge and best practices",
            "action": "Drawing from financial planning principles and market knowledge"
        })
        
        steps.append({
            "step_type": "personalization",
            "thought": "Tailoring the response to the user's specific situation",
            "action": "Considering user context and providing personalized advice"
        })
        
        confidence = 0.6
        return steps, confidence
    
    def _assess_complexity(self, query: str) -> str:
        """Assess the complexity of the query"""
        word_count = len(query.split())
        
        # Count financial concepts
        financial_concepts = 0
        concepts = ['invest', 'retire', 'budget', 'debt', 'tax', 'insurance', 'mortgage', 'loan', 'portfolio', 'risk']
        for concept in concepts:
            if concept in query.lower():
                financial_concepts += 1
        
        if word_count > 30 or financial_concepts > 3:
            return "high"
        elif word_count > 15 or financial_concepts > 1:
            return "medium"
        else:
            return "low"
    
    async def execute_task(self, task_type: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """Execute a reasoning task"""
        try:
            if task_type == "analyze_query":
                return await self.analyze_query(data['user_id'], data['query'], data.get('context', {}))
            else:
                return {'success': False, 'error': f'Unknown task type: {task_type}'}
                
        except Exception as e:
            logger.error(f"Failed to execute reasoning task {task_type}: {e}")
            return {'success': False, 'error': str(e)}
    
    def update_config(self, config):
        """Update configuration"""
        try:
            logger.info("Updated reasoning agent configuration")
        except Exception as e:
            logger.error(f"Failed to update config: {e}")
