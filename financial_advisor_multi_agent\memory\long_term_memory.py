"""
Long-Term Memory for PennyWise Multi-Agent System
Stores EVERYTHING about a user in one unified document
Unlike specialized memories, this captures the complete user journey
"""

from datetime import datetime, timezone
from typing import Dict, List, Any, Optional
from pymongo import MongoClient
import os
from dataclasses import dataclass, asdict
import json

@dataclass
class UserLifeEvent:
    """Represents a significant life event"""
    event_type: str  # career_change, marriage, birth, purchase, etc.
    description: str
    date: datetime
    financial_impact: str
    emotional_context: str
    related_goals: List[str]

@dataclass
class FinancialMilestone:
    """Represents financial achievements or setbacks"""
    milestone_type: str  # first_investment, debt_free, house_purchase, etc.
    amount: float
    description: str
    date: datetime
    lessons_learned: str
    next_steps: List[str]

@dataclass
class ConversationInsight:
    """Deep insights from conversations"""
    date: datetime
    topic: str
    user_sentiment: str
    key_concerns: List[str]
    advice_given: str
    user_response: str
    follow_up_needed: bool
    confidence_level: float

class LongTermMemory:
    """Unified long-term memory storing complete user profile"""
    
    def __init__(self):
        self.client = None
        self.db = None
        self.collection = None
        self.initialized = False

    async def initialize(self):
        """Initialize the long-term memory system"""
        try:
            self.client = MongoClient(os.getenv("MONGODB_URL", "mongodb://localhost:27017/"))
            self.db = self.client.pennywise_financial_advisor
            self.collection = self.db.long_term_memory

            # Create indexes for efficient querying (with error handling)
            try:
                self.collection.create_index("user_id", unique=True)
                self.collection.create_index("last_updated")
                self.collection.create_index("financial_profile.risk_tolerance")
            except Exception as e:
                print(f"Warning: Could not create indexes: {e}")

            self.initialized = True
        except Exception as e:
            print(f"Error initializing long-term memory: {e}")
            # Create a fallback in-memory storage
            self.initialized = False

    def get_or_create_user_memory(self, user_id: str) -> Dict[str, Any]:
        """Get existing long-term memory or create new one"""

        if not self.initialized or not self.collection:
            # Return default memory structure if not initialized
            return self._create_default_memory(user_id)

        try:
            memory = self.collection.find_one({"user_id": user_id})
        except Exception as e:
            print(f"Error accessing MongoDB: {e}")
            return self._create_default_memory(user_id)
        
        if not memory:
            memory = self._create_default_memory(user_id)
            try:
                self.collection.insert_one(memory)
            except Exception as e:
                print(f"Error inserting memory to MongoDB: {e}")

        return memory

    def _create_default_memory(self, user_id: str) -> Dict[str, Any]:
        """Create default memory structure"""
        return {
                "user_id": user_id,
                "created_at": datetime.now(timezone.utc),
                "last_updated": datetime.now(timezone.utc),
                
                # Personal Information
                "personal_info": {
                    "age": None,
                    "location": None,
                    "occupation": None,
                    "family_status": None,
                    "education_level": None,
                    "life_stage": None  # student, young_professional, family_building, pre_retirement, retired
                },
                
                # Financial Profile
                "financial_profile": {
                    "income": None,
                    "net_worth": None,
                    "debt_total": None,
                    "emergency_fund": None,
                    "risk_tolerance": "moderate",
                    "investment_experience": "beginner",
                    "financial_knowledge_level": "basic",
                    "preferred_investment_types": [],
                    "financial_goals": [],
                    "time_horizons": {}
                },
                
                # Behavioral Patterns
                "behavioral_patterns": {
                    "communication_style": "balanced",  # detailed, concise, visual, analytical
                    "decision_making_style": "collaborative",  # quick, analytical, collaborative, cautious
                    "learning_preference": "explanatory",  # examples, theory, step_by_step, visual
                    "question_patterns": [],
                    "engagement_level": "medium",
                    "preferred_response_length": "medium",
                    "topics_of_interest": [],
                    "avoidance_topics": []
                },
                
                # Life Events & Milestones
                "life_events": [],
                "financial_milestones": [],
                
                # Conversation History & Insights
                "conversation_insights": [],
                "key_concerns": [],
                "recurring_themes": [],
                "advice_effectiveness": {},
                
                # Goals & Planning
                "short_term_goals": [],  # < 1 year
                "medium_term_goals": [],  # 1-5 years
                "long_term_goals": [],   # 5+ years
                "completed_goals": [],
                "abandoned_goals": [],
                
                # Financial Journey
                "financial_journey": {
                    "starting_point": None,
                    "current_stage": "assessment",
                    "progress_markers": [],
                    "setbacks": [],
                    "achievements": [],
                    "lessons_learned": []
                },
                
                # Preferences & Settings
                "preferences": {
                    "notification_frequency": "weekly",
                    "preferred_contact_method": "chat",
                    "privacy_level": "standard",
                    "data_sharing_consent": False,
                    "personalization_level": "high"
                },
                
                # Analytics & Insights
                "analytics": {
                    "total_conversations": 0,
                    "total_advice_given": 0,
                    "most_discussed_topics": {},
                    "engagement_score": 0.0,
                    "satisfaction_score": 0.0,
                    "progress_score": 0.0,
                    "last_active": datetime.now(timezone.utc)
                },
                
                # Metadata
                "metadata": {
                    "data_version": "1.0",
                    "last_backup": None,
                    "data_quality_score": 0.0,
                    "completeness_percentage": 0.0
                }
            }

    def update_personal_info(self, user_id: str, info: Dict[str, Any]) -> bool:
        """Update personal information"""
        if not self.initialized or not self.collection:
            print("Long-term memory not initialized, skipping update")
            return False

        try:
            self.collection.update_one(
                {"user_id": user_id},
                {
                    "$set": {
                        f"personal_info.{key}": value for key, value in info.items()
                    },
                    "$set": {"last_updated": datetime.now(timezone.utc)}
                }
            )
            return True
        except Exception as e:
            print(f"Error updating personal info: {e}")
            return False

    def update_financial_profile(self, user_id: str, profile: Dict[str, Any]) -> bool:
        """Update financial profile"""
        try:
            self.collection.update_one(
                {"user_id": user_id},
                {
                    "$set": {
                        f"financial_profile.{key}": value for key, value in profile.items()
                    },
                    "$set": {"last_updated": datetime.now(timezone.utc)}
                }
            )
            return True
        except Exception as e:
            print(f"Error updating financial profile: {e}")
            return False

    def add_life_event(self, user_id: str, event: UserLifeEvent) -> bool:
        """Add a significant life event"""
        try:
            self.collection.update_one(
                {"user_id": user_id},
                {
                    "$push": {"life_events": asdict(event)},
                    "$set": {"last_updated": datetime.now(timezone.utc)}
                }
            )
            return True
        except Exception as e:
            print(f"Error adding life event: {e}")
            return False

    def add_financial_milestone(self, user_id: str, milestone: FinancialMilestone) -> bool:
        """Add a financial milestone"""
        try:
            self.collection.update_one(
                {"user_id": user_id},
                {
                    "$push": {"financial_milestones": asdict(milestone)},
                    "$set": {"last_updated": datetime.now(timezone.utc)}
                }
            )
            return True
        except Exception as e:
            print(f"Error adding financial milestone: {e}")
            return False

    def add_conversation_insight(self, user_id: str, insight: ConversationInsight) -> bool:
        """Add deep conversation insight"""
        try:
            self.collection.update_one(
                {"user_id": user_id},
                {
                    "$push": {"conversation_insights": asdict(insight)},
                    "$inc": {"analytics.total_conversations": 1},
                    "$set": {
                        "last_updated": datetime.now(timezone.utc),
                        "analytics.last_active": datetime.now(timezone.utc)
                    }
                }
            )
            return True
        except Exception as e:
            print(f"Error adding conversation insight: {e}")
            return False

    def update_behavioral_patterns(self, user_id: str, patterns: Dict[str, Any]) -> bool:
        """Update behavioral patterns based on interactions"""
        try:
            self.collection.update_one(
                {"user_id": user_id},
                {
                    "$set": {
                        f"behavioral_patterns.{key}": value for key, value in patterns.items()
                    },
                    "$set": {"last_updated": datetime.now(timezone.utc)}
                }
            )
            return True
        except Exception as e:
            print(f"Error updating behavioral patterns: {e}")
            return False

    def get_complete_user_profile(self, user_id: str) -> Dict[str, Any]:
        """Get the complete long-term memory for a user"""
        return self.get_or_create_user_memory(user_id)

    def get_user_summary(self, user_id: str) -> Dict[str, Any]:
        """Get a summary of the user for quick reference"""
        memory = self.get_or_create_user_memory(user_id)

        return {
            "user_id": user_id,
            "personal_summary": {
                "age": memory["personal_info"].get("age"),
                "occupation": memory["personal_info"].get("occupation"),
                "life_stage": memory["personal_info"].get("life_stage")
            },
            "financial_summary": {
                "risk_tolerance": memory["financial_profile"]["risk_tolerance"],
                "investment_experience": memory["financial_profile"]["investment_experience"],
                "primary_goals": memory["financial_profile"]["financial_goals"][:3]
            },
            "engagement_summary": {
                "total_conversations": memory["analytics"]["total_conversations"],
                "engagement_score": memory["analytics"]["engagement_score"],
                "last_active": memory["analytics"]["last_active"],
                "preferred_style": memory["behavioral_patterns"]["communication_style"]
            },
            "recent_insights": memory["conversation_insights"][-3:] if memory["conversation_insights"] else [],
            "current_focus": memory["key_concerns"][-3:] if memory["key_concerns"] else []
        }

# Global long-term memory instance
long_term_memory = LongTermMemory()
