"""
Base Agent Class - Foundation for all intelligent agents
Each agent has its own LLM, memory, and reasoning capabilities
"""

import asyncio
import logging
import os
from typing import Dict, Any, List, Optional
from datetime import datetime
from abc import ABC, abstractmethod

logger = logging.getLogger(__name__)

class BaseAgent(ABC):
    """Base class for all intelligent agents"""
    
    def __init__(self, agent_name: str, specialization: str):
        self.agent_name = agent_name
        self.specialization = specialization
        self.llm = None
        self.memory = {}  # Agent's own memory
        self.is_initialized = False
        self.conversation_history = []
        
    async def initialize(self):
        """Initialize the agent with its own LLM and memory"""
        try:
            logger.info(f"Initializing {self.agent_name} Agent")
            
            # Initialize LLM for this agent
            await self._initialize_llm()
            
            # Initialize agent's memory
            await self._initialize_memory()
            
            # Agent-specific initialization
            await self._agent_specific_init()
            
            self.is_initialized = True
            logger.info(f"✅ {self.agent_name} Agent initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize {self.agent_name} Agent: {e}")
            raise
    
    async def _initialize_llm(self):
        """Initialize agent's own LLM"""
        try:
            from langchain_groq import ChatGroq
            
            api_key = os.getenv("GROQ_API_KEY")
            if api_key:
                self.llm = ChatGroq(
                    groq_api_key=api_key,
                    model_name="llama3-70b-8192",
                    temperature=0.1,
                    max_tokens=1000
                )
                logger.info(f"{self.agent_name} LLM initialized")
            else:
                logger.warning(f"{self.agent_name} No API key, using fallback")
                self.llm = None
                
        except Exception as e:
            logger.error(f"Failed to initialize LLM for {self.agent_name}: {e}")
            self.llm = None
    
    async def _initialize_memory(self):
        """Initialize agent's memory"""
        self.memory = {
            "expertise_knowledge": {},
            "user_interactions": {},
            "learned_patterns": {},
            "context_history": []
        }
    
    @abstractmethod
    async def _agent_specific_init(self):
        """Agent-specific initialization - must be implemented by subclasses"""
        pass
    
    async def process_query(self, user_id: str, query: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Process a query using agent's intelligence"""
        try:
            if not self.is_initialized:
                return {"success": False, "error": "Agent not initialized"}
            
            # Store context in agent's memory
            self._store_context(user_id, query, context)
            
            # Generate agent's specialized response
            response = await self._generate_response(user_id, query, context)
            
            # Learn from this interaction
            await self._learn_from_interaction(user_id, query, response)
            
            return {
                "success": True,
                "agent": self.agent_name,
                "specialization": self.specialization,
                "response": response,
                "confidence": response.get("confidence", 0.7),
                "reasoning": response.get("reasoning", ""),
                "data": response.get("data", {})
            }
            
        except Exception as e:
            logger.error(f"{self.agent_name} failed to process query: {e}")
            return {"success": False, "error": str(e)}
    
    async def _generate_response(self, user_id: str, query: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Generate intelligent response using LLM"""
        try:
            if not self.llm:
                return await self._fallback_response(user_id, query, context)
            
            # Build specialized prompt for this agent
            system_prompt = self._build_system_prompt(context)
            user_prompt = self._build_user_prompt(user_id, query, context)
            
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ]
            
            # Get LLM response
            llm_response = await self.llm.ainvoke(messages)
            
            # Process and structure the response
            structured_response = await self._process_llm_response(llm_response.content, query, context)
            
            return structured_response
            
        except Exception as e:
            logger.error(f"{self.agent_name} LLM response failed: {e}")
            return await self._fallback_response(user_id, query, context)
    
    def _build_system_prompt(self, context: Dict[str, Any]) -> str:
        """Build system prompt for this agent"""
        base_prompt = f"""You are {self.agent_name}, a specialized AI agent focused on {self.specialization}.

Your role is to provide expert analysis and insights in your area of specialization.
You work as part of a multi-agent system, so focus on your expertise area.

Key responsibilities:
- Provide accurate, specialized information in your domain
- Analyze data and context relevant to your specialization  
- Offer actionable insights and recommendations
- Maintain professional, helpful tone
- Be concise but thorough in your analysis

Current context: {context.get('user_profile', {})}
"""
        return base_prompt
    
    def _build_user_prompt(self, user_id: str, query: str, context: Dict[str, Any]) -> str:
        """Build user prompt with context"""
        user_context = self.memory["user_interactions"].get(user_id, {})
        
        prompt = f"""User Query: {query}

User Context:
- Previous interactions: {len(user_context.get('history', []))}
- User profile: {context.get('user_profile', {})}

Please provide your specialized analysis and recommendations for this query.
Focus on your area of expertise: {self.specialization}

Respond in JSON format with:
{{
    "analysis": "Your detailed analysis",
    "recommendations": ["list", "of", "recommendations"],
    "confidence": 0.8,
    "reasoning": "Why you reached this conclusion",
    "data": {{"any": "relevant data"}},
    "next_steps": ["suggested", "actions"]
}}
"""
        return prompt
    
    async def _process_llm_response(self, response: str, query: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Process and validate LLM response"""
        try:
            import json
            # Try to parse as JSON
            parsed = json.loads(response)
            return parsed
        except:
            # If not JSON, structure it
            return {
                "analysis": response,
                "recommendations": [],
                "confidence": 0.6,
                "reasoning": "Generated from unstructured response",
                "data": {},
                "next_steps": []
            }
    
    @abstractmethod
    async def _fallback_response(self, user_id: str, query: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Fallback response when LLM is not available - must be implemented by subclasses"""
        pass
    
    def _store_context(self, user_id: str, query: str, context: Dict[str, Any]):
        """Store interaction context in agent's memory"""
        if user_id not in self.memory["user_interactions"]:
            self.memory["user_interactions"][user_id] = {"history": []}
        
        self.memory["user_interactions"][user_id]["history"].append({
            "timestamp": datetime.now(),
            "query": query,
            "context": context
        })
        
        # Keep only last 10 interactions per user
        if len(self.memory["user_interactions"][user_id]["history"]) > 10:
            self.memory["user_interactions"][user_id]["history"] = \
                self.memory["user_interactions"][user_id]["history"][-10:]
    
    async def _learn_from_interaction(self, user_id: str, query: str, response: Dict[str, Any]):
        """Learn from this interaction to improve future responses"""
        # Update learned patterns
        query_type = self._classify_query(query)
        if query_type not in self.memory["learned_patterns"]:
            self.memory["learned_patterns"][query_type] = {"count": 0, "success_rate": 0.0}
        
        self.memory["learned_patterns"][query_type]["count"] += 1
        
        # Store successful patterns
        if response.get("confidence", 0) > 0.7:
            self.memory["learned_patterns"][query_type]["success_rate"] += 0.1
    
    def _classify_query(self, query: str) -> str:
        """Classify query type for learning"""
        query_lower = query.lower()
        
        # Basic classification - can be enhanced
        if any(word in query_lower for word in ['how', 'what', 'explain']):
            return "informational"
        elif any(word in query_lower for word in ['should', 'recommend', 'suggest']):
            return "advisory"
        elif any(word in query_lower for word in ['calculate', 'compute', 'analyze']):
            return "analytical"
        else:
            return "general"
    
    def get_agent_status(self) -> Dict[str, Any]:
        """Get current agent status"""
        return {
            "name": self.agent_name,
            "specialization": self.specialization,
            "initialized": self.is_initialized,
            "llm_available": self.llm is not None,
            "total_interactions": sum(len(user_data["history"]) for user_data in self.memory["user_interactions"].values()),
            "learned_patterns": len(self.memory["learned_patterns"])
        }
