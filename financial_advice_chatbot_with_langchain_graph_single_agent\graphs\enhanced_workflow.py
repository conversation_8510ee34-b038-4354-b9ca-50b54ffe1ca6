from typing import TypedDict, List, Dict, Any
from langgraph.graph import StateGraph, END
from langchain_groq import ChatGroq
from langchain.schema import HumanMessage, SystemMessage
import os
from dotenv import load_dotenv

# Import dynamic LLM manager
from utils.llm_manager import llm_manager

# Import tools
from tools.financial_tools import get_real_stock_data, get_economic_indicators
from tools.geography_tools import get_real_country_data
from tools.market_tools import get_market_sentiment, get_sector_performance

# Import enhanced memory systems
from memory.enhanced_memory import memory

# Import planning system
from graphs.planning_system import planning_system

# Import reasoning system
from graphs.reasoning_system import reasoning_system

# Import guardrails system
from guardrails.unified_guardrails import financial_guardrails

load_dotenv()

# Enhanced state for more complex workflows with memory, planning, and reasoning
class EnhancedFinancialState(TypedDict):
    user_id: str
    user_input: str
    user_profile: Dict[str, Any]
    conversation_history: str  # LangChain memory buffer
    vector_context: str  # Vector database context
    financial_plan: Dict[str, Any]  # LangChain planning result
    reasoning_result: Dict[str, Any]  # Advanced reasoning result
    analysis_type: str
    stock_analysis: str
    economic_analysis: str
    geographic_analysis: str
    market_sentiment: str
    sector_analysis: str
    final_response: str
    confidence_score: float

class EnhancedFinancialWorkflow:
    def __init__(self):
        # Use dynamic LLM manager instead of static initialization
        self.llm_manager = llm_manager
        
        # Create the enhanced workflow
        self.workflow = self.create_workflow()
        self.app = self.workflow.compile()
    
    def create_workflow(self):
        """Create the enhanced LangGraph workflow"""
        workflow = StateGraph(EnhancedFinancialState)
        
        # Enhanced workflow: analyze -> reason -> context -> plan -> respond
        workflow.add_node("analyze_query", self.analyze_query)
        workflow.add_node("apply_reasoning", self.apply_reasoning)
        workflow.add_node("retrieve_context", self.retrieve_context)
        workflow.add_node("create_plan", self.create_plan)
        workflow.add_node("synthesize_response", self.synthesize_response)

        # Set entry point
        workflow.set_entry_point("analyze_query")

        # Enhanced workflow with memory, planning, and reasoning
        workflow.add_edge("analyze_query", "apply_reasoning")
        workflow.add_edge("apply_reasoning", "retrieve_context")
        workflow.add_edge("retrieve_context", "create_plan")

        # Simple routing: LLM decides direct response or needs tools
        workflow.add_conditional_edges(
            "create_plan",
            self.route_analysis,
            {
                "direct_response": "synthesize_response",
                "needs_tools": "synthesize_response"  # Let LLM handle tool decisions in synthesis
            }
        )

        # End workflow
        workflow.add_edge("synthesize_response", END)
        
        return workflow
    
    def analyze_query(self, state: EnhancedFinancialState) -> EnhancedFinancialState:
        """Enhanced query analysis with memory and vector context"""
        query = state["user_input"].lower()

        # Update user profile with enhanced memory
        current_profile = memory.get_user_profile(state["user_id"])
        updated_profile = memory.extract_user_info(state["user_input"], current_profile)
        memory.save_user_profile(state["user_id"], updated_profile)

        # Update behavioral memory with user profile
        # This is now handled automatically in memory.save_user_profile()

        state["user_profile"] = updated_profile

        # Get LangChain conversation history
        state["conversation_history"] = memory.get_conversation_history(state["user_id"])

        # Let the LLM decide if it needs tools or can respond directly
        decision_prompt = f"""
        User Query: "{state['user_input']}"

        As PennyWise, a financial advisor, do you need external tools/data to answer this properly?

        Respond with ONLY one word:
        - "DIRECT" if you can answer directly (greetings, general advice, simple questions)
        - "TOOLS" if you need market data, stock info, or complex analysis

        Decision:"""

        try:
            # Get current LLM instance (may use custom API key)
            current_llm = self.llm_manager.get_llm()
            decision_response = current_llm.invoke(decision_prompt)
            decision = decision_response.content.strip().upper()

            if "DIRECT" in decision:
                state["analysis_type"] = "direct_response"
            else:
                state["analysis_type"] = "needs_tools"

        except Exception as e:
            # Fallback: assume tools needed for safety
            state["analysis_type"] = "needs_tools"

        state["analysis_type"] = state.get("analysis_type", "needs_tools")
        return state

    def apply_reasoning(self, state: EnhancedFinancialState) -> EnhancedFinancialState:
        """Apply advanced reasoning (Plan-and-Execute + ReAct)"""

        try:
            # Apply reasoning based on query complexity and type
            reasoning_context = {
                "user_profile": state["user_profile"],
                "analysis_type": state["analysis_type"],
                "conversation_history": state.get("conversation_history", "")
            }

            # Use the reasoning system
            reasoning_result = reasoning_system.reason_about_query(
                state["user_input"],
                reasoning_context
            )

            state["reasoning_result"] = reasoning_result

            # Update confidence based on reasoning quality
            reasoning_confidence = reasoning_result.get("confidence", 0.7)
            current_confidence = state.get("confidence_score", 0.7)
            state["confidence_score"] = (reasoning_confidence + current_confidence) / 2

            print(f"✅ Applied {reasoning_result.get('reasoning_type', 'unknown')} reasoning")

        except Exception as e:
            print(f"❌ Reasoning error: {e}")
            # Fallback reasoning
            state["reasoning_result"] = {
                "reasoning_type": "fallback",
                "final_reasoning": "I'll analyze this step by step using my financial knowledge.",
                "confidence": 0.6,
                "approach_used": "Fallback reasoning due to system error"
            }

        return state

    def retrieve_context(self, state: EnhancedFinancialState) -> EnhancedFinancialState:
        """Retrieve relevant context from vector database and prioritize feedback"""

        # Get comprehensive context from all memory systems
        comprehensive_context = memory.get_comprehensive_context(
            state["user_id"],
            state["user_input"]
        )

        # Extract semantic context for backward compatibility
        semantic_context = comprehensive_context.get('semantic', {}).get('relevant_knowledge', '')

        # Get conversation history and extract feedback - ENHANCED WITH DEBUG
        try:
            # Get conversation history as string
            conversation_history_str = memory.get_conversation_history(state["user_id"])

            # DEBUG: Print conversation history to see what's actually there
            print(f"DEBUG: Conversation history for user {state['user_id']}:")
            print(f"DEBUG: History length: {len(conversation_history_str) if conversation_history_str else 0}")
            if conversation_history_str:
                print(f"DEBUG: First 500 chars: {conversation_history_str[:500]}")
                print(f"DEBUG: Contains FEEDBACK_ENTRY: {'[FEEDBACK_ENTRY]' in conversation_history_str}")
                print(f"DEBUG: Contains FEEDBACK: {'[FEEDBACK]' in conversation_history_str}")

            feedback_context = ""

            # Process conversation history for structured feedback entries
            if conversation_history_str and isinstance(conversation_history_str, str):
                lines = conversation_history_str.split('\n')

                # Look for structured feedback entries
                feedback_entries = []
                current_entry = []
                in_feedback_entry = False

                for line in lines:
                    if '[FEEDBACK_ENTRY]' in line:
                        in_feedback_entry = True
                        current_entry = []
                    elif '---' in line and in_feedback_entry:
                        if current_entry:
                            feedback_entries.append('\n'.join(current_entry))
                        in_feedback_entry = False
                        current_entry = []
                    elif in_feedback_entry:
                        current_entry.append(line.strip())

                # Also look for simple feedback markers
                simple_feedback = [line for line in lines if any(keyword in line for keyword in ['[FEEDBACK]', 'USER_FEEDBACK:', 'SYSTEM_FEEDBACK:']) and 'FEEDBACK_ENTRY' not in line]

                # DEBUG: Print what feedback was found
                print(f"DEBUG: Found {len(feedback_entries)} structured feedback entries")
                print(f"DEBUG: Found {len(simple_feedback)} simple feedback items")
                if feedback_entries:
                    print(f"DEBUG: First feedback entry: {feedback_entries[0][:100]}...")
                if simple_feedback:
                    print(f"DEBUG: First simple feedback: {simple_feedback[0][:100]}...")

                # Combine feedback information
                if feedback_entries or simple_feedback:
                    feedback_context += "\n\nPREVIOUS USER FEEDBACK:"

                    # Add structured feedback entries (most recent first)
                    for entry in feedback_entries[-2:]:  # Last 2 structured entries
                        feedback_context += f"\n{entry}\n"

                    # Add simple feedback
                    for feedback in simple_feedback[-3:]:  # Last 3 simple feedback items
                        feedback_context += f"\n- {feedback.strip()}"

                    feedback_context += "\n\nCRITICAL: Use this feedback to adjust your response style and approach!"
                else:
                    print("DEBUG: No feedback found in conversation history")

        except Exception as e:
            print(f"Error retrieving feedback context: {e}")
            feedback_context = ""

        # Combine semantic context with feedback
        state["vector_context"] = semantic_context + feedback_context
        state["comprehensive_context"] = comprehensive_context
        return state

    def create_plan(self, state: EnhancedFinancialState) -> EnhancedFinancialState:
        """Create financial plan using LangChain planning system"""

        planning_steps = []

        try:
            # Record planning step
            planning_steps.append({
                "step": "plan_analysis",
                "description": "Analyzing query complexity and planning requirements",
                "status": "completed"
            })

            # Use LangChain plan-and-execute for complex planning
            if "comprehensive" in state["user_input"].lower() or len(state["user_input"].split()) > 15:
                planning_steps.append({
                    "step": "comprehensive_planning",
                    "description": "Creating comprehensive financial plan with LangChain",
                    "status": "in_progress"
                })

                plan_result = planning_system.execute_plan_with_langchain(state["user_input"])
                state["financial_plan"] = {"type": "comprehensive", "result": plan_result}

                planning_steps[-1]["status"] = "completed"
                planning_steps[-1]["result"] = "Comprehensive plan created successfully"

            else:
                planning_steps.append({
                    "step": "custom_planning",
                    "description": "Creating custom financial plan based on user profile",
                    "status": "in_progress"
                })

                # Use custom financial planning
                plan_result = planning_system.create_comprehensive_plan(
                    state["user_profile"],
                    state["user_input"]
                )
                state["financial_plan"] = {"type": "custom", "result": plan_result}

                planning_steps[-1]["status"] = "completed"
                planning_steps[-1]["result"] = "Custom plan created successfully"

        except Exception as e:
            planning_steps.append({
                "step": "error_handling",
                "description": f"Planning error occurred: {str(e)}",
                "status": "error"
            })
            state["financial_plan"] = {"type": "error", "result": f"Planning error: {str(e)}"}

        # Store planning steps in state
        state["planning_steps"] = planning_steps

        return state


    def route_analysis(self, state: EnhancedFinancialState) -> str:
        """Route to appropriate analysis based on query type"""
        return state["analysis_type"]
    

    def stock_analysis(self, state: EnhancedFinancialState) -> EnhancedFinancialState:
        """Perform real stock analysis"""
        query = state["user_input"]
        
        # Extract stock symbols from query
        common_symbols = ["AAPL", "MSFT", "GOOGL", "AMZN", "TSLA", "NVDA", "META", "SPY", "QQQ"]
        symbols_to_analyze = []
        
        for symbol in common_symbols:
            if symbol.lower() in query.lower():
                symbols_to_analyze.append(symbol)
        
        # If no specific symbols, use SPY as market proxy
        if not symbols_to_analyze:
            symbols_to_analyze = ["SPY"]
        
        stock_results = []
        for symbol in symbols_to_analyze[:2]:  # Limit to 2 stocks to avoid API limits
            result = get_real_stock_data.invoke({"symbol": symbol})
            stock_results.append(result)
        
        state["stock_analysis"] = "\n\n".join(stock_results)
        return state
    
    def economic_analysis(self, state: EnhancedFinancialState) -> EnhancedFinancialState:
        """Perform real economic analysis"""
        result = get_economic_indicators.invoke({"country": "US"})
        state["economic_analysis"] = result
        return state
    
    def geographic_analysis(self, state: EnhancedFinancialState) -> EnhancedFinancialState:
        """Perform real geographic analysis"""
        query = state["user_input"].lower()
        
        # Determine country to analyze
        country = "Germany"  # Default
        if "france" in query:
            country = "France"
        elif "uk" in query or "britain" in query:
            country = "United Kingdom"
        elif "japan" in query:
            country = "Japan"
        
        result = get_real_country_data.invoke({"country": country})
        state["geographic_analysis"] = result
        return state
    
    def market_analysis(self, state: EnhancedFinancialState) -> EnhancedFinancialState:
        """Perform real market sentiment analysis"""
        result = get_market_sentiment.invoke({})
        state["market_sentiment"] = result
        return state
    
    def sector_analysis(self, state: EnhancedFinancialState) -> EnhancedFinancialState:
        """Perform real sector performance analysis"""
        result = get_sector_performance.invoke({})
        state["sector_analysis"] = result
        return state
    
    def synthesize_response(self, state: EnhancedFinancialState) -> EnhancedFinancialState:
        """Smart response synthesis - LLM decides if it needs tools"""

        # Check if LLM decided it needs tools
        if state.get("analysis_type") == "needs_tools":
            # LLM decided it needs external data, so let's get some tools
            try:
                # Get basic market data if needed
                query = state["user_input"].lower()
                if any(word in query for word in ["stock", "market", "price", "ticker"]):
                    # Get stock data
                    stock_data = self.get_stock_data_for_query(query)
                    if stock_data:
                        state["stock_analysis"] = stock_data

                # Get economic data if needed
                if any(word in query for word in ["economy", "inflation", "gdp", "economic"]):
                    economic_data = self.get_economic_data()
                    if economic_data:
                        state["economic_analysis"] = economic_data

            except Exception as e:
                # If tools fail, continue without them
                pass

        # Prepare context for LLM
        context_parts = []

        # Add vector database context
        if state.get("vector_context"):
            context_parts.append(f"Financial Knowledge:\n{state['vector_context']}")

        # Add conversation history
        if state.get("conversation_history"):
            context_parts.append(f"Previous Conversation:\n{state['conversation_history']}")

        # Add any tool results
        if state.get("stock_analysis"):
            context_parts.append(f"Market Data:\n{state['stock_analysis']}")

        if state.get("economic_analysis"):
            context_parts.append(f"Economic Data:\n{state['economic_analysis']}")

        # Add reasoning results
        if state.get("reasoning_result"):
            reasoning = state["reasoning_result"]
            reasoning_text = f"""
Reasoning Analysis:
- Approach: {reasoning.get('approach_used', 'Standard analysis')}
- Type: {reasoning.get('reasoning_type', 'basic')}
- Confidence: {reasoning.get('confidence', 0.7):.1%}
- Analysis: {reasoning.get('final_reasoning', 'Standard financial analysis applied')}
"""
            context_parts.append(reasoning_text)

        context = "\n\n".join(context_parts)
        
        # Get user profile for personalization
        profile = state["user_profile"]
        profile_text = ""
        if profile:
            profile_items = []
            if profile.get("age"):
                profile_items.append(f"Age: {profile['age']}")
            if profile.get("income"):
                profile_items.append(f"Income: ${profile['income']:,}")
            if profile.get("risk_tolerance"):
                profile_items.append(f"Risk Tolerance: {profile['risk_tolerance']}")
            if profile.get("goals"):
                profile_items.append(f"Goals: {', '.join(profile['goals'])}")
            
            if profile_items:
                profile_text = f"User Profile: {', '.join(profile_items)}"
        
        # Enhanced synthesis prompt with memory and planning context
        base_system_prompt = """You are PennyWise, a friendly financial advisor.

            Guidelines:
            - Keep responses SHORT and conversational (2-3 paragraphs max)
            - ALWAYS use the user's profile information when available (age, income, risk tolerance, goals)
            - If user asks about their profile, refer to the specific details provided
            - CRITICAL: Pay close attention to any FEEDBACK in the context - if user gave positive feedback, continue that approach; if negative feedback, adjust your style and be more detailed/careful
            - If you see [POSITIVE_FEEDBACK] - the user liked that response style, so use similar approach
            - If you see [NEGATIVE_FEEDBACK] - the user didn't like that response, so change your approach and be more thorough
            - If you created a financial plan, just mention "I've created a plan for you" - don't show details
            - Give practical, actionable advice
            - Use simple language, avoid jargon
            - Be encouraging and supportive

            Remember: Keep it brief, friendly, personalized, and ALWAYS consider user feedback!"""

        # Add security protection to system prompt
        protected_prompt = financial_guardrails.get_protected_system_prompt(base_system_prompt)

        messages = [
            SystemMessage(content=protected_prompt),
            HumanMessage(content=f"""
            User Query: {state['user_input']}

            {profile_text}

            Available Context and Analysis:
            {context}

            Please provide integrated financial advice that leverages all available information including:
            - Real market data and analysis
            - Relevant financial knowledge from database
            - User's conversation history and profile
            - Any financial plans created

            Make sure to reference specific data points and provide actionable next steps.
            """)
        ]

        # Get current LLM instance (may use custom API key)
        current_llm = self.llm_manager.get_llm()
        response = current_llm.invoke(messages)
        raw_response = response.content

        # Check for response manipulation (AI was successfully jailbroken)
        if financial_guardrails.detect_response_manipulation(raw_response):
            print("🚨 CRITICAL: Response manipulation detected! Using safe fallback.")
            state["final_response"] = financial_guardrails.get_safe_fallback_response(state["user_input"])
        else:
            # Apply guardrails to ensure safe and compliant response
            guardrail_result = financial_guardrails.check_response(
                user_input=state["user_input"],
                ai_response=raw_response,
                user_profile=state.get("user_profile", {})
            )

            # Use guardrail-modified response
            state["final_response"] = guardrail_result.final_response

            # Log any guardrail violations (for monitoring)
            if not guardrail_result.overall_safe:
                print(f"⚠️ Input violations: {guardrail_result.input_violations}")
                print(f"⚠️ Response violations: {guardrail_result.response_violations}")

            if guardrail_result.warnings:
                print(f"💡 Guardrail warnings: {guardrail_result.warnings}")

        # Enhanced confidence scoring including new features
        data_sources = len([x for x in [
            state.get("stock_analysis"),
            state.get("economic_analysis"),
            state.get("geographic_analysis"),
            state.get("market_sentiment"),
            state.get("sector_analysis"),
            state.get("vector_context"),
            state.get("financial_plan")
        ] if x])

        state["confidence_score"] = min(0.95, 0.4 + (data_sources * 0.08))

        # Save conversation to LangChain memory
        memory.add_conversation(
            state["user_id"],
            state["user_input"],
            state["final_response"]
        )

        # Add insights to semantic memory for future reference
        if state.get("final_response"):
            insight = f"User query: {state['user_input']}\nResponse: {state['final_response'][:500]}..."
            memory.semantic.add_financial_concept(insight, "conversation_insight", "medium")

        return state

    def get_stock_data_for_query(self, query: str) -> str:
        """Get stock data based on query content"""
        try:
            # Extract stock symbol if mentioned
            symbols = ["AAPL", "MSFT", "GOOGL", "TSLA", "AMZN", "NVDA"]
            for symbol in symbols:
                if symbol.lower() in query:
                    from tools.financial_tools import get_real_stock_data
                    return get_real_stock_data(symbol)

            # Default to market overview
            from tools.market_tools import get_market_sentiment
            return get_market_sentiment()
        except Exception as e:
            return f"Market data temporarily unavailable: {str(e)}"

    def get_economic_data(self) -> str:
        """Get basic economic data"""
        try:
            from tools.financial_tools import get_economic_indicators
            return get_economic_indicators()
        except Exception as e:
            return f"Economic data temporarily unavailable: {str(e)}"

    def process_query(self, user_id: str, query: str) -> Dict[str, Any]:
        """Process a user query through the enhanced workflow with memory, vector, and planning"""
        initial_state = {
            "user_id": user_id,
            "user_input": query,
            "user_profile": {},
            "conversation_history": "",  # LangChain memory buffer
            "vector_context": "",  # Vector database context
            "financial_plan": {},  # Planning results
            "analysis_type": "",
            "stock_analysis": "",
            "economic_analysis": "",
            "geographic_analysis": "",
            "market_sentiment": "",
            "sector_analysis": "",
            "final_response": "",
            "confidence_score": 0.0
        }

        final_state = self.app.invoke(initial_state)

        return {
            "response": final_state["final_response"],
            "confidence": final_state["confidence_score"],
            "analysis_type": final_state["analysis_type"],
            "user_profile": final_state["user_profile"],
            "vector_context": final_state.get("vector_context", ""),
            "financial_plan": final_state.get("financial_plan", {}),
            "reasoning_result": final_state.get("reasoning_result", {}),
            "conversation_history": final_state.get("conversation_history", ""),
            "planning_steps": final_state.get("planning_steps", []),
            "workflow_state": final_state  # Include full state for debugging
        }

# Create global workflow instance
enhanced_workflow = EnhancedFinancialWorkflow()