"""
Geography Agent - Geographic and Regional Financial Data
Handles location-based financial information and regional economic data
"""

import asyncio
import logging
from typing import Dict, Any, List, Optional
import requests

logger = logging.getLogger(__name__)

class GeographyAgent:
    """Agent responsible for geographic and regional financial data"""
    
    def __init__(self):
        self.is_initialized = False
        self.country_data_cache = {}
        
    async def initialize(self):
        """Initialize the geography agent"""
        try:
            logger.info("Initializing Geography Agent")
            
            # Initialize with basic country data
            self.country_data_cache = {
                "US": {
                    "name": "United States",
                    "currency": "USD",
                    "tax_advantages": ["401k", "IRA", "Roth IRA", "HSA"],
                    "retirement_age": 67,
                    "social_security": True,
                    "investment_accounts": ["Brokerage", "401k", "IRA", "Roth IRA"]
                },
                "CA": {
                    "name": "Canada", 
                    "currency": "CAD",
                    "tax_advantages": ["RRSP", "TFSA", "RESP"],
                    "retirement_age": 65,
                    "social_security": True,
                    "investment_accounts": ["RRSP", "TFSA", "Non-registered"]
                },
                "UK": {
                    "name": "United Kingdom",
                    "currency": "GBP", 
                    "tax_advantages": ["ISA", "SIPP", "Pension"],
                    "retirement_age": 66,
                    "social_security": True,
                    "investment_accounts": ["ISA", "SIPP", "General Investment Account"]
                }
            }
            
            self.is_initialized = True
            logger.info("✅ Geography Agent initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize Geography Agent: {e}")
            raise
    
    async def get_data(self, user_id: str, query: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Get geographic data based on query"""
        try:
            query_lower = query.lower()
            data = {}
            
            # Determine if geographic data is needed
            if any(word in query_lower for word in ['country', 'region', 'international', 'global', 'local']):
                country_data = await self._get_country_data(query)
                if country_data:
                    data['geographic_analysis'] = country_data
            
            # Check for specific countries mentioned
            countries_mentioned = self._extract_countries(query)
            if countries_mentioned:
                country_comparison = await self._compare_countries(countries_mentioned)
                if country_comparison:
                    data['country_comparison'] = country_comparison
            
            return data
            
        except Exception as e:
            logger.error(f"Failed to get geographic data: {e}")
            return {}
    
    async def _get_country_data(self, query: str) -> Dict[str, Any]:
        """Get country-specific financial data"""
        try:
            # Extract country from query or use default
            country_code = self._extract_primary_country(query)
            
            if country_code in self.country_data_cache:
                country_info = self.country_data_cache[country_code]
                
                return {
                    "country": country_info["name"],
                    "currency": country_info["currency"],
                    "tax_advantaged_accounts": country_info["tax_advantages"],
                    "retirement_age": country_info["retirement_age"],
                    "has_social_security": country_info["social_security"],
                    "investment_options": country_info["investment_accounts"],
                    "financial_advice": self._get_country_specific_advice(country_code)
                }
            else:
                return {
                    "country": "Unknown",
                    "note": "Country-specific data not available",
                    "general_advice": "Consult local financial regulations and tax laws"
                }
                
        except Exception as e:
            logger.error(f"Failed to get country data: {e}")
            return {}
    
    async def _compare_countries(self, countries: List[str]) -> Dict[str, Any]:
        """Compare financial systems between countries"""
        try:
            comparison = {
                "countries_compared": countries,
                "comparison_data": {},
                "key_differences": [],
                "recommendations": []
            }
            
            for country in countries:
                if country in self.country_data_cache:
                    comparison["comparison_data"][country] = self.country_data_cache[country]
            
            # Generate key differences
            if len(comparison["comparison_data"]) >= 2:
                comparison["key_differences"] = self._analyze_differences(comparison["comparison_data"])
                comparison["recommendations"] = self._generate_country_recommendations(comparison["comparison_data"])
            
            return comparison
            
        except Exception as e:
            logger.error(f"Failed to compare countries: {e}")
            return {}
    
    def _extract_countries(self, query: str) -> List[str]:
        """Extract country mentions from query"""
        countries = []
        query_upper = query.upper()
        
        # Country mappings
        country_keywords = {
            "US": ["US", "USA", "UNITED STATES", "AMERICA", "AMERICAN"],
            "CA": ["CANADA", "CANADIAN"],
            "UK": ["UK", "UNITED KINGDOM", "BRITAIN", "BRITISH", "ENGLAND"],
            "AU": ["AUSTRALIA", "AUSTRALIAN"],
            "DE": ["GERMANY", "GERMAN"],
            "FR": ["FRANCE", "FRENCH"],
            "JP": ["JAPAN", "JAPANESE"],
            "IN": ["INDIA", "INDIAN"]
        }
        
        for country_code, keywords in country_keywords.items():
            if any(keyword in query_upper for keyword in keywords):
                countries.append(country_code)
        
        return countries
    
    def _extract_primary_country(self, query: str) -> str:
        """Extract primary country from query, default to US"""
        countries = self._extract_countries(query)
        return countries[0] if countries else "US"
    
    def _get_country_specific_advice(self, country_code: str) -> List[str]:
        """Get country-specific financial advice"""
        advice = []
        
        if country_code == "US":
            advice = [
                "Maximize employer 401(k) match before other investments",
                "Consider Roth IRA for tax-free retirement growth",
                "Use HSA as triple tax-advantaged retirement account",
                "Take advantage of tax-loss harvesting in taxable accounts"
            ]
        elif country_code == "CA":
            advice = [
                "Maximize RRSP contributions for tax deduction",
                "Use TFSA for tax-free growth on after-tax dollars",
                "Consider RESP for children's education savings",
                "Understand Canadian tax implications of US investments"
            ]
        elif country_code == "UK":
            advice = [
                "Maximize ISA allowance for tax-free investing",
                "Consider SIPP for additional pension contributions",
                "Understand dividend tax implications",
                "Take advantage of capital gains tax allowance"
            ]
        else:
            advice = [
                "Research local tax-advantaged investment accounts",
                "Understand local tax implications of investments",
                "Consider currency risk for international investments",
                "Consult local financial regulations"
            ]
        
        return advice
    
    def _analyze_differences(self, country_data: Dict[str, Dict[str, Any]]) -> List[str]:
        """Analyze key differences between countries"""
        differences = []
        
        countries = list(country_data.keys())
        if len(countries) >= 2:
            country1, country2 = countries[0], countries[1]
            data1, data2 = country_data[country1], country_data[country2]
            
            # Compare retirement ages
            if data1["retirement_age"] != data2["retirement_age"]:
                differences.append(f"Retirement age: {data1['name']} ({data1['retirement_age']}) vs {data2['name']} ({data2['retirement_age']})")
            
            # Compare tax-advantaged accounts
            accounts1 = set(data1["tax_advantages"])
            accounts2 = set(data2["tax_advantages"])
            
            if accounts1 != accounts2:
                unique1 = accounts1 - accounts2
                unique2 = accounts2 - accounts1
                
                if unique1:
                    differences.append(f"{data1['name']} has unique accounts: {', '.join(unique1)}")
                if unique2:
                    differences.append(f"{data2['name']} has unique accounts: {', '.join(unique2)}")
            
            # Compare currencies
            if data1["currency"] != data2["currency"]:
                differences.append(f"Different currencies: {data1['currency']} vs {data2['currency']}")
        
        return differences
    
    def _generate_country_recommendations(self, country_data: Dict[str, Dict[str, Any]]) -> List[str]:
        """Generate recommendations based on country comparison"""
        recommendations = []
        
        if len(country_data) >= 2:
            recommendations.append("Consider tax implications when investing across borders")
            recommendations.append("Understand currency exchange risks for international investments")
            recommendations.append("Research tax treaties between countries to avoid double taxation")
            recommendations.append("Consider professional advice for cross-border financial planning")
        
        return recommendations
    
    async def get_regional_economic_data(self, region: str) -> Dict[str, Any]:
        """Get regional economic data"""
        try:
            # This would integrate with economic data APIs
            # For now, return basic regional information
            
            regional_data = {
                "region": region,
                "economic_indicators": {
                    "note": "Regional economic data requires additional API integration",
                    "general_trends": [
                        "Monitor regional inflation rates",
                        "Consider local market conditions",
                        "Understand regional economic cycles"
                    ]
                },
                "investment_considerations": [
                    "Diversify across regions to reduce risk",
                    "Consider regional ETFs for exposure",
                    "Understand political and economic stability"
                ]
            }
            
            return regional_data
            
        except Exception as e:
            logger.error(f"Failed to get regional economic data: {e}")
            return {}
    
    async def execute_task(self, task_type: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """Execute a geography task"""
        try:
            if task_type == "get_data":
                return await self.get_data(data['user_id'], data['query'], data.get('context', {}))
            elif task_type == "country_data":
                country = data.get('country', 'US')
                return await self._get_country_data(f"information about {country}")
            elif task_type == "regional_data":
                region = data.get('region', 'North America')
                return await self.get_regional_economic_data(region)
            else:
                return {'success': False, 'error': f'Unknown task type: {task_type}'}
                
        except Exception as e:
            logger.error(f"Failed to execute geography task {task_type}: {e}")
            return {'success': False, 'error': str(e)}
    
    def update_config(self, config):
        """Update configuration"""
        try:
            logger.info("Updated geography agent configuration")
        except Exception as e:
            logger.error(f"Failed to update config: {e}")
