#!/usr/bin/env python3
"""
Final comprehensive test for all fixes
"""

def test_all_file_syntax():
    """Test that all Python files have valid syntax"""
    print("🔍 TESTING FILE SYNTAX")
    print("=" * 50)
    
    files_to_test = [
        "main.py",
        "memory/enhanced_memory.py", 
        "graphs/enhanced_workflow.py",
        "memory/short_term_memory.py",
        "memory/semantic_memory.py",
        "memory/behavioral_memory.py",
        "memory/episodic_memory.py"
    ]
    
    all_valid = True
    
    for file_path in files_to_test:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Try to compile the code
            compile(content, file_path, 'exec')
            print(f"✅ {file_path} - Syntax OK")
            
        except SyntaxError as e:
            print(f"❌ {file_path} - Syntax Error: {e}")
            all_valid = False
        except FileNotFoundError:
            print(f"⚠️ {file_path} - File not found")
        except Exception as e:
            print(f"❌ {file_path} - Error: {e}")
            all_valid = False
    
    return all_valid

def test_feedback_system_structure():
    """Test the structure of the feedback system"""
    print("\n🔍 TESTING FEEDBACK SYSTEM STRUCTURE")
    print("=" * 50)
    
    try:
        # Test main.py feedback implementation
        with open("main.py", "r") as f:
            main_content = f.read()
        
        # Check for enhanced feedback storage calls
        feedback_checks = [
            ("Enhanced positive feedback", "memory.store_feedback(st.session_state.user_id, prompt, response, feedback_text, 4)"),
            ("Enhanced negative feedback", "memory.store_feedback(st.session_state.user_id, prompt, response, feedback_text, 2)"),
            ("Enhanced comment feedback", "memory.store_feedback(st.session_state.user_id, prompt, response, feedback_text, 3)"),
            ("Interaction storage", "memory.store_interaction_with_feedback(st.session_state.user_id, prompt, response)"),
            ("Close button removed", "Close Planning Steps" not in main_content)
        ]
        
        all_checks_passed = True
        for check_name, check_condition in feedback_checks:
            if isinstance(check_condition, str):
                if check_condition in main_content:
                    print(f"✅ {check_name} - Found")
                else:
                    print(f"❌ {check_name} - Not found")
                    all_checks_passed = False
            else:  # Boolean condition
                if check_condition:
                    print(f"✅ {check_name} - Verified")
                else:
                    print(f"❌ {check_name} - Failed")
                    all_checks_passed = False
        
        # Test memory system enhancements
        with open("memory/enhanced_memory.py", "r") as f:
            memory_content = f.read()
        
        memory_checks = [
            ("Enhanced store_feedback method", "def store_feedback(self, user_id: str, query: str, response: str, feedback: str, rating: int):"),
            ("Interaction storage method", "def store_interaction_with_feedback"),
            ("Structured feedback entry", "[FEEDBACK_ENTRY]"),
            ("Feedback separation", "USER_FEEDBACK")
        ]
        
        for check_name, check_condition in memory_checks:
            if check_condition in memory_content:
                print(f"✅ {check_name} - Found")
            else:
                print(f"❌ {check_name} - Not found")
                all_checks_passed = False
        
        # Test workflow enhancements
        with open("graphs/enhanced_workflow.py", "r") as f:
            workflow_content = f.read()
        
        workflow_checks = [
            ("Enhanced feedback retrieval", "PREVIOUS USER FEEDBACK"),
            ("Structured feedback processing", "[FEEDBACK_ENTRY]"),
            ("Feedback instruction", "CRITICAL: Use this feedback")
        ]
        
        for check_name, check_condition in workflow_checks:
            if check_condition in workflow_content:
                print(f"✅ {check_name} - Found")
            else:
                print(f"❌ {check_name} - Not found")
                all_checks_passed = False
        
        return all_checks_passed
        
    except Exception as e:
        print(f"❌ Error testing feedback system structure: {e}")
        return False

def test_thinking_process_fixes():
    """Test that thinking process variable fixes are in place"""
    print("\n🔍 TESTING THINKING PROCESS FIXES")
    print("=" * 50)
    
    try:
        with open("main.py", "r") as f:
            content = f.read()
        
        variable_fixes = [
            "analysis_type = thinking_data.get('analysis_type'",
            "vector_context = thinking_data.get('vector_context'",
            "financial_plan = thinking_data.get('financial_plan'",
            "processing_time = thinking_data.get('processing_time'"
        ]
        
        all_fixed = True
        for fix in variable_fixes:
            if fix in content:
                print(f"✅ Variable fix found: {fix[:30]}...")
            else:
                print(f"❌ Variable fix missing: {fix[:30]}...")
                all_fixed = False
        
        return all_fixed
        
    except Exception as e:
        print(f"❌ Error testing thinking process fixes: {e}")
        return False

def main():
    """Run comprehensive tests"""
    print("🧪 FINAL COMPREHENSIVE TEST SUITE")
    print("=" * 70)
    
    # Run all tests
    syntax_test = test_all_file_syntax()
    feedback_test = test_feedback_system_structure()
    thinking_test = test_thinking_process_fixes()
    
    print("\n" + "=" * 70)
    print("📊 COMPREHENSIVE TEST RESULTS")
    print("=" * 70)
    
    print(f"1. File Syntax Validation: {'✅ PASSED' if syntax_test else '❌ FAILED'}")
    print(f"2. Feedback System Structure: {'✅ PASSED' if feedback_test else '❌ FAILED'}")
    print(f"3. Thinking Process Fixes: {'✅ PASSED' if thinking_test else '❌ FAILED'}")
    
    overall_success = all([syntax_test, feedback_test, thinking_test])
    
    if overall_success:
        print("\n🎉 ALL COMPREHENSIVE TESTS PASSED!")
        print("\n🔧 FIXES IMPLEMENTED:")
        print("✅ Enhanced feedback storage with query, response, and feedback")
        print("✅ Structured feedback entries in short-term memory")
        print("✅ Enhanced feedback retrieval in workflow")
        print("✅ Fixed all thinking process variable errors")
        print("✅ Removed non-working close planning button")
        print("✅ Bot will remember previous feedback in next responses")
        
        print("\n🚀 READY FOR PRODUCTION TESTING!")
        print("📋 TEST SEQUENCE:")
        print("1. streamlit run main.py")
        print("2. Ask: 'What should I invest in?'")
        print("3. Give feedback: 👎 'Too vague, consider my age and income'")
        print("4. Ask: 'What feedback did I give you about investing?'")
        print("5. Bot should remember and reference your specific feedback!")
        print("6. Test 'Show My Thinking' - should work without NameError")
        
        print("\n💡 EXPECTED BEHAVIOR:")
        print("- Bot remembers feedback from previous interactions")
        print("- Feedback includes original query, response, and user feedback")
        print("- Workflow uses feedback to adjust response style")
        print("- No more variable errors in thinking process")
        print("- Clean UI without broken buttons")
        
    else:
        print("\n⚠️ Some tests failed. Please review the implementation.")
    
    return overall_success

if __name__ == "__main__":
    main()
