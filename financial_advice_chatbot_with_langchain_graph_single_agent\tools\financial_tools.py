from langchain.tools import tool
import yfinance as yf
import requests
import os
from typing import Optional
import pandas as pd

@tool
def get_real_stock_data(symbol: str) -> str:
    """Get real-time stock data using Yahoo Finance API"""

    try: 
        ticker = yf.Ticker(symbol.upper())
        info = ticker.info
        hist = ticker.history(period="5d")

        if hist.empty:
            return f"Could not find stock data for symbol: {symbol}"

        current_price = hist['Close'].iloc[-1]
        prev_price = hist['Close'].iloc[-2] if len(hist) > 1 else current_price
        change = current_price - prev_price
        change_percent = (change / prev_price) * 100 if prev_price != 0 else 0

        market_cap = info.get('marketCap', 'N/A')
        pe_ration = info.get('trailingPE', 'N/A')
        dividend_yield = info.get('dividendYield','N/A')

        return f"""
        Real Stock Analysis for {symbol.upper()}:

        Current Price: ${current_price:.2f}
        Daily Change: ${change:.2f} ({change_percent:.2f}%)
        Market Cap: {market_cap}
        P/E Ratio: {pe_ration}
        Dividend Yield: {dividend_yield}

        Company: {info.get('longName', symbol.upper())}
        Sector: {info.get('sector', 'N/A')}
        Industry: {info.get('industry', 'N/A')}

        Investment Recommendation: Based on current metrics, this stock shows
        {'positive' if change_percent > 0 else 'negative'} momentum.
        """

    except Exception as e:
        return f"Error fetching stock data for {symbol}: {str(e)}"

@tool
def get_economic_indicators(country: str = "US") -> str:
    """Get real economic indicators using Alpha Vantage API"""
    api_key = os.getenv("ALPHA_VANTAGE_API_KEY")

    if not api_key:
        return "Alpha Vantage API key not configured. Using sample data for demonstration."

    try:
        gdp_url = f"https://www.alphavantage.co/query?function=REAL_GDP&interval=annual&apikey={api_key}"
        gdp_response = requests.get(gdp_url)
        gdp_data = gdp_response.json()

        inflation_url = f"https://www.alphavantage.co/query?function=INFLATION&apikey={api_key}"
        inflation_response = requests.get(inflation_url)
        inflation_data = inflation_response.json()

        unemployment_url = f"https://www.alphavantage.co/query?function=UNEMPLOYMENT&apikey={api_key}"
        unemployment_response = requests.get(unemployment_url)
        unemployment_data = unemployment_response.json()

        latest_gdp = 'N/A'
        latest_inflation = 'N/A'
        latest_unemployment = 'N/A'

        if 'data' in gdp_data and gdp_data['data']:
            latest_gdp = gdp_data['data'][0]['value']
        
        if 'data' in inflation_data and inflation_data['data']:
            latest_inflation = inflation_data['data'][0]['value']
        
        if 'data' in unemployment_data and unemployment_data['data']:
            latest_unemployment = unemployment_data['data'][0]['value']

        return f"""
        Real Economic Indicators for {country}:
        
        GDP Growth: {latest_gdp}%
        Inflation Rate: {latest_inflation}%
        Unemployment Rate: {latest_unemployment}%

        Economic Assessment:
        - GDP growth indicates economic {'expansion' if latest_gdp != 'N/A' and float(latest_gdp) > 2 else 'stability'}
        - Inflation is {'within target range' if latest_inflation != 'N/A' and float(latest_inflation) < 3 else 'being monitored'}
        - Employment market is {'strong' if latest_unemployment != 'N/A' and float(latest_unemployment) < 5 else 'stable'}

        Investment Implication: Current economic conditions suggest a {'favorable' if latest_gdp != 'N/A' and float(latest_gdp) > 2 else 'cautious'} investment environment.
        """

    except Exception as e:
        # Fallback with real-world approximate data
        return f"""
        Economic Indicators for {country} (Latest Available):

        GDP Growth: ~2.1% (Q3 2024)
        Inflation Rate: ~3.2% (October 2024)
        Unemployment Rate: ~3.7% (October 2024)

        Economic Assessment:
        - GDP growth shows moderate expansion
        - Inflation slightly above Fed target but declining
        - Employment market remains robust
        
        Investment Implication: Current conditions favor diversified investment approach with moderate risk tolerance.
        
        Note: Using latest available data. API error: {str(e)}
        """