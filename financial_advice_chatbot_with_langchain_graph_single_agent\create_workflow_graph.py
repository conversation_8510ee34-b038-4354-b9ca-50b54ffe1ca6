#!/usr/bin/env python3
"""
Create a detailed visualization of the PennyWise LangGraph workflow
Generates a PNG file showing the complete workflow without requiring database connections
"""

import os

# Install graphviz if needed
try:
    import graphviz
except ImportError:
    print("Installing graphviz package...")
    import subprocess
    subprocess.check_call(["pip", "install", "graphviz"])
    import graphviz

def create_pennywise_workflow_diagram():
    """Create a comprehensive diagram of the PennyWise workflow"""
    
    # Create a new directed graph
    dot = graphviz.Digraph(
        'PennyWise_Complete_Workflow', 
        comment='PennyWise Financial Advisor - Complete LangGraph Workflow',
        format='png',
        engine='dot'
    )
    
    # Set graph attributes for better layout
    dot.attr(
        rankdir='TB',  # Top to bottom layout
        size='16,12',
        ratio='fill',
        fontname='Arial Bold',
        fontsize='18',
        label='PennyWise Financial Advisor - Complete LangGraph Workflow',
        labelloc='t',
        bgcolor='white',
        pad='0.5',
        nodesep='0.8',
        ranksep='1.2'
    )
    
    # Default node styling
    dot.attr('node', 
        shape='box', 
        style='filled,rounded',
        fontname='Arial',
        fontsize='11',
        margin='0.3,0.2',
        penwidth='2'
    )
    
    # Default edge styling
    dot.attr('edge', 
        fontname='Arial',
        fontsize='9',
        penwidth='1.5',
        arrowsize='0.8'
    )
    
    # 1. USER INTERACTION LAYER
    with dot.subgraph(name='cluster_user') as c:
        c.attr(label='👤 User Interaction Layer', style='filled', color='darkblue', fillcolor='lightblue', fontsize='14')
        c.node('user_input', '📝 User Input\n(Financial Question)', fillcolor='lightgreen', shape='ellipse')
        c.node('final_output', '💬 Final Response\n(Personalized Advice)', fillcolor='lightgreen', shape='ellipse')
    
    # 2. GUARDRAILS LAYER
    with dot.subgraph(name='cluster_guardrails') as c:
        c.attr(label='🛡️ Safety & Validation Layer', style='filled', color='darkred', fillcolor='mistyrose', fontsize='14')
        c.node('input_guardrails', '🔍 Input Validation\n• PII Detection\n• Relevance Check\n• Safety Filter', fillcolor='lightyellow')
        c.node('output_guardrails', '✅ Response Validation\n• Content Safety\n• Financial Compliance\n• Quality Check', fillcolor='lightyellow')
    
    # 3. ENHANCED WORKFLOW CORE
    with dot.subgraph(name='cluster_workflow') as c:
        c.attr(label='🧠 Enhanced Workflow Engine', style='filled', color='darkgreen', fillcolor='lightcyan', fontsize='14')
        c.node('query_analysis', '🔍 Query Analysis\n• Intent Detection\n• Complexity Assessment\n• Tool Requirements', fillcolor='lightcyan')
        c.node('context_retrieval', '📚 Context Retrieval\n• Memory Systems\n• User Profile\n• Conversation History', fillcolor='lightcyan')
        c.node('decision_router', '🎯 Decision Router\n• Direct Response\n• Planning Required\n• Reasoning Required', fillcolor='orange', shape='diamond')
    
    # 4. MEMORY SYSTEMS
    with dot.subgraph(name='cluster_memory') as c:
        c.attr(label='🧠 Memory Systems', style='filled', color='purple', fillcolor='lavender', fontsize='14')
        c.node('short_term', '⚡ Short-Term\n(Redis)\n• Session Data\n• Chat History', fillcolor='lightcyan')
        c.node('semantic', '🔍 Semantic\n(Qdrant)\n• Financial Knowledge\n• Vector Search', fillcolor='lightcyan')
        c.node('behavioral', '📊 Behavioral\n(MongoDB)\n• User Patterns\n• Preferences', fillcolor='lightcyan')
        c.node('episodic', '📝 Episodic\n(MongoDB)\n• Past Interactions\n• Context Memory', fillcolor='lightcyan')
        c.node('long_term', '🏛️ Long-Term\n(Unified)\n• Complete Profile\n• Journey Tracking', fillcolor='lightcyan')
    
    # 5. PLANNING SYSTEM
    with dot.subgraph(name='cluster_planning') as c:
        c.attr(label='📋 Planning System', style='filled', color='darkmagenta', fillcolor='thistle', fontsize='14')
        c.node('plan_creation', '📝 Plan Creation\n• Goal Analysis\n• Step Generation\n• Resource Planning', fillcolor='lightpink')
        c.node('plan_execution', '⚡ Plan Execution\n• Step-by-Step\n• Progress Tracking\n• Adaptation', fillcolor='lightpink')
        c.node('plan_evaluation', '📊 Plan Evaluation\n• Quality Assessment\n• Feasibility Check\n• Risk Analysis', fillcolor='lightpink')
    
    # 6. REASONING SYSTEM
    with dot.subgraph(name='cluster_reasoning') as c:
        c.attr(label='🤔 Reasoning System', style='filled', color='darkorange', fillcolor='peachpuff', fontsize='14')
        c.node('reasoning_router', '🎯 Reasoning Router\n• Type Selection\n• Complexity Analysis', fillcolor='orange', shape='diamond')
        c.node('plan_execute', '📋 Plan-and-Execute\n• Systematic Approach\n• Structured Thinking', fillcolor='lightpink')
        c.node('react_reasoning', '🔄 ReAct Reasoning\n• Dynamic Thinking\n• Iterative Analysis', fillcolor='lightpink')
        c.node('hybrid_reasoning', '🔀 Hybrid Reasoning\n• Combined Approach\n• Complex Problems', fillcolor='lightpink')
    
    # 7. FINANCIAL TOOLS
    with dot.subgraph(name='cluster_tools') as c:
        c.attr(label='🛠️ Financial Tools', style='filled', color='darkgreen', fillcolor='honeydew', fontsize='14')
        c.node('stock_data', '📈 Stock Data\n(Yahoo Finance)\n• Real-time Prices\n• Market Data', fillcolor='palegreen')
        c.node('economic_data', '📊 Economic Data\n(Alpha Vantage)\n• Indicators\n• Trends', fillcolor='palegreen')
        c.node('market_sentiment', '💭 Market Sentiment\n• VIX Analysis\n• Trend Detection', fillcolor='palegreen')
        c.node('country_data', '🌍 Country Data\n• Economic Stats\n• Demographics', fillcolor='palegreen')
    
    # 8. RESPONSE GENERATION
    with dot.subgraph(name='cluster_response') as c:
        c.attr(label='💬 Response Generation', style='filled', color='darkblue', fillcolor='aliceblue', fontsize='14')
        c.node('llm_synthesis', '🧠 LLM Synthesis\n• Context Integration\n• Personalization\n• Response Crafting', fillcolor='lightblue')
        c.node('feedback_integration', '📊 Feedback Integration\n• User Ratings\n• Improvement Learning\n• Quality Enhancement', fillcolor='lightblue')
    
    # MAIN WORKFLOW CONNECTIONS
    dot.edge('user_input', 'input_guardrails', label='1. Input', color='blue', fontcolor='blue')
    dot.edge('input_guardrails', 'query_analysis', label='2. Validated', color='blue', fontcolor='blue')
    dot.edge('query_analysis', 'context_retrieval', label='3. Analyze', color='blue', fontcolor='blue')
    dot.edge('context_retrieval', 'decision_router', label='4. Context', color='blue', fontcolor='blue')
    
    # DECISION ROUTING
    dot.edge('decision_router', 'llm_synthesis', label='Direct Response', color='green', fontcolor='green')
    dot.edge('decision_router', 'plan_creation', label='Planning Needed', color='purple', fontcolor='purple')
    dot.edge('decision_router', 'reasoning_router', label='Reasoning Needed', color='orange', fontcolor='orange')
    
    # PLANNING FLOW
    dot.edge('plan_creation', 'plan_execution', color='purple')
    dot.edge('plan_execution', 'plan_evaluation', color='purple')
    dot.edge('plan_evaluation', 'llm_synthesis', label='Plan Complete', color='purple', fontcolor='purple')
    
    # REASONING FLOW
    dot.edge('reasoning_router', 'plan_execute', label='Systematic', color='orange', fontcolor='orange')
    dot.edge('reasoning_router', 'react_reasoning', label='Dynamic', color='orange', fontcolor='orange')
    dot.edge('reasoning_router', 'hybrid_reasoning', label='Complex', color='orange', fontcolor='orange')
    dot.edge('plan_execute', 'llm_synthesis', color='orange')
    dot.edge('react_reasoning', 'llm_synthesis', color='orange')
    dot.edge('hybrid_reasoning', 'llm_synthesis', color='orange')
    
    # MEMORY CONNECTIONS
    dot.edge('context_retrieval', 'short_term', style='dashed', dir='both', color='purple')
    dot.edge('context_retrieval', 'semantic', style='dashed', dir='both', color='purple')
    dot.edge('context_retrieval', 'behavioral', style='dashed', dir='both', color='purple')
    dot.edge('context_retrieval', 'episodic', style='dashed', dir='both', color='purple')
    dot.edge('short_term', 'long_term', style='dotted', color='purple')
    dot.edge('semantic', 'long_term', style='dotted', color='purple')
    dot.edge('behavioral', 'long_term', style='dotted', color='purple')
    dot.edge('episodic', 'long_term', style='dotted', color='purple')
    
    # TOOL CONNECTIONS
    dot.edge('query_analysis', 'stock_data', style='dashed', dir='both', color='green')
    dot.edge('query_analysis', 'economic_data', style='dashed', dir='both', color='green')
    dot.edge('query_analysis', 'market_sentiment', style='dashed', dir='both', color='green')
    dot.edge('query_analysis', 'country_data', style='dashed', dir='both', color='green')
    
    # FINAL RESPONSE FLOW
    dot.edge('llm_synthesis', 'output_guardrails', label='5. Generate', color='blue', fontcolor='blue')
    dot.edge('output_guardrails', 'feedback_integration', label='6. Validate', color='blue', fontcolor='blue')
    dot.edge('feedback_integration', 'final_output', label='7. Deliver', color='blue', fontcolor='blue')
    
    # FEEDBACK LOOP
    dot.edge('final_output', 'long_term', label='Store Interaction', style='dashed', color='gray')
    dot.edge('final_output', 'feedback_integration', label='User Feedback', style='dashed', color='gray')
    
    # Save the diagram
    filename = 'pennywise_complete_workflow'
    try:
        dot.render(filename, cleanup=True)
        print(f"✅ Complete workflow diagram created: {filename}.png")
        return f"{filename}.png"
    except Exception as e:
        print(f"❌ Error creating diagram: {e}")
        # Try saving without cleanup
        try:
            dot.render(filename, cleanup=False)
            print(f"✅ Workflow diagram created (with source): {filename}.png")
            return f"{filename}.png"
        except Exception as e2:
            print(f"❌ Failed to create diagram: {e2}")
            return None

if __name__ == "__main__":
    print("🎨 Creating PennyWise Workflow Diagram...")
    diagram_path = create_pennywise_workflow_diagram()
    if diagram_path:
        print(f"📊 Diagram saved to: {diagram_path}")
        print("🎯 This diagram shows the complete LangGraph workflow including:")
        print("   • User interaction flow")
        print("   • Memory systems integration")
        print("   • Planning and reasoning systems")
        print("   • Financial tools and data sources")
        print("   • Guardrails and validation")
        print("   • Response generation and feedback")
    else:
        print("❌ Failed to create workflow diagram")
