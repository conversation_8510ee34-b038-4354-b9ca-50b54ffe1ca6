#!/usr/bin/env python3
"""
Quick start script for PennyWise Multi-Agent Financial Advisor
"""

import os
import sys
import subprocess
import time

def check_requirements():
    """Check if required dependencies are installed"""
    try:
        import streamlit
        import langchain
        import langgraph
        import redis
        import pymongo
        import qdrant_client
        print("✅ All required dependencies are installed")
        return True
    except ImportError as e:
        print(f"❌ Missing dependency: {e}")
        print("Please run: pip install -r requirements.txt")
        return False

def check_environment():
    """Check environment variables"""
    required_vars = ['GROQ_API_KEY']
    missing_vars = []
    
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        print(f"⚠️ Missing environment variables: {', '.join(missing_vars)}")
        print("Please set these in your .env file or environment")
        return False
    
    print("✅ Environment variables are set")
    return True

def start_services():
    """Start the multi-agent system"""
    print("🚀 Starting PennyWise Multi-Agent Financial Advisor...")
    
    try:
        # Start the main application
        cmd = [
            sys.executable, "-m", "streamlit", "run", 
            "frontend/multi_agent_ui.py",
            "--server.port=8501",
            "--server.address=0.0.0.0"
        ]
        
        print("🌐 Starting Streamlit application on http://localhost:8501")
        subprocess.run(cmd)
        
    except KeyboardInterrupt:
        print("\n👋 Shutting down PennyWise Multi-Agent System")
    except Exception as e:
        print(f"❌ Error starting application: {e}")

def main():
    """Main startup function"""
    print("🤖 PennyWise Multi-Agent Financial Advisor")
    print("=" * 50)
    
    # Check requirements
    if not check_requirements():
        sys.exit(1)
    
    # Check environment
    if not check_environment():
        print("\n💡 To get started:")
        print("1. Copy .env.example to .env")
        print("2. Add your Groq API key to .env")
        print("3. Run this script again")
        sys.exit(1)
    
    # Start services
    start_services()

if __name__ == "__main__":
    main()
