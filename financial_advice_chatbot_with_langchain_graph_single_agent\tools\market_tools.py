from langchain.tools import tool
import yfinance as yf
import requests
from datetime import datetime, timedelta

@tool
def get_market_sentiment() -> str:
    """Get real market sentiment using major indices and VIX data"""
    try:
        # Get major indices
        indices = {
            '^GSPC': 'S&P 500',
            '^DJI': 'Dow Jones',
            '^IXIC': 'NASDAQ',
            '^VIX': 'VIX (Fear Index)'
        }
        
        market_data = {}
        
        for symbol, name in indices.items():
            ticker = yf.Ticker(symbol)
            hist = ticker.history(period="5d")
            
            if not hist.empty:
                current = hist['Close'].iloc[-1]
                prev = hist['Close'].iloc[-2] if len(hist) > 1 else current
                change_pct = ((current - prev) / prev) * 100 if prev != 0 else 0
                
                market_data[name] = {
                    'current': current,
                    'change_pct': change_pct
                }
        
        # Analyze sentiment
        sp500_change = market_data.get('S&P 500', {}).get('change_pct', 0)
        vix_level = market_data.get('VIX (Fear Index)', {}).get('current', 20)
        
        if vix_level < 15:
            sentiment = "Low Fear (Complacent)"
        elif vix_level < 25:
            sentiment = "Normal (Stable)"
        elif vix_level < 35:
            sentiment = "Elevated Fear (Cautious)"
        else:
            sentiment = "High Fear (Panic)"
        
        market_direction = "Bullish" if sp500_change > 0 else "Bearish"
        
        return f"""
        Real Market Sentiment Analysis:
        
        📊 Major Indices Performance:
        - S&P 500: {market_data.get('S&P 500', {}).get('current', 'N/A'):.2f} ({market_data.get('S&P 500', {}).get('change_pct', 0):+.2f}%)
        - Dow Jones: {market_data.get('Dow Jones', {}).get('current', 'N/A'):.2f} ({market_data.get('Dow Jones', {}).get('change_pct', 0):+.2f}%)
        - NASDAQ: {market_data.get('NASDAQ', {}).get('current', 'N/A'):.2f} ({market_data.get('NASDAQ', {}).get('change_pct', 0):+.2f}%)
        
        😰 Fear & Greed Indicator:
        - VIX Level: {vix_level:.2f}
        - Sentiment: {sentiment}
        - Market Direction: {market_direction}
        
        💡 Investment Implication:
        - Current sentiment suggests {'buying opportunities' if vix_level > 25 else 'normal market conditions' if vix_level < 20 else 'cautious approach recommended'}
        - {'Consider dollar-cost averaging' if vix_level > 30 else 'Maintain diversified portfolio' if vix_level < 20 else 'Monitor for volatility'}
        
        📈 Recommendation: {'Defensive positioning' if vix_level > 25 else 'Balanced approach' if vix_level < 20 else 'Opportunistic investing'}
        """
        
    except Exception as e:
        return f"Error fetching market sentiment data: {str(e)}"

@tool
def get_sector_performance() -> str:
    """Get real sector performance using sector ETFs"""
    try:
        # Major sector ETFs
        sectors = {
            'XLK': 'Technology',
            'XLF': 'Financial',
            'XLV': 'Healthcare', 
            'XLE': 'Energy',
            'XLI': 'Industrial',
            'XLY': 'Consumer Discretionary',
            'XLP': 'Consumer Staples',
            'XLRE': 'Real Estate',
            'XLU': 'Utilities'
        }
        
        sector_performance = {}
        
        for etf, sector_name in sectors.items():
            ticker = yf.Ticker(etf)
            hist = ticker.history(period="1mo")  # 1 month performance
            
            if not hist.empty:
                current = hist['Close'].iloc[-1]
                month_ago = hist['Close'].iloc[0]
                monthly_return = ((current - month_ago) / month_ago) * 100
                
                sector_performance[sector_name] = monthly_return
        
        # Sort by performance
        sorted_sectors = sorted(sector_performance.items(), key=lambda x: x[1], reverse=True)
        
        # Create performance summary
        top_performers = sorted_sectors[:3]
        bottom_performers = sorted_sectors[-3:]
        
        performance_text = "📊 Sector Performance (1 Month):\n\n"
        performance_text += "🚀 Top Performers:\n"
        for sector, perf in top_performers:
            performance_text += f"- {sector}: {perf:+.2f}%\n"
        
        performance_text += "\n📉 Underperformers:\n"
        for sector, perf in bottom_performers:
            performance_text += f"- {sector}: {perf:+.2f}%\n"
        
        # Investment recommendation
        best_sector = top_performers[0][0] if top_performers else "Technology"
        performance_text += f"\n💡 Investment Insight:\n"
        performance_text += f"- {best_sector} sector showing strongest momentum\n"
        performance_text += f"- Consider sector rotation strategies\n"
        performance_text += f"- Diversification across sectors recommended\n"
        
        return performance_text

    except Exception as e:
        return f"Error fetching market sentiment data: {str(e)}"

@tool
def get_sector_performance() -> str:
    """Get real sector performance using sector ETFs"""
    try:
        # Major sector ETFs
        sectors = {
            'XLK': 'Technology',
            'XLF': 'Financial',
            'XLV': 'Healthcare',
            'XLE': 'Energy',
            'XLI': 'Industrial',
            'XLY': 'Consumer Discretionary',
            'XLP': 'Consumer Staples',
            'XLRE': 'Real Estate',
            'XLU': 'Utilities'
        }

        sector_performance = {}

        for etf, sector_name in sectors.items():
            ticker = yf.Ticker(etf)
            hist = ticker.history(period="1mo")  # 1 month performance

            if not hist.empty:
                current = hist['Close'].iloc[-1]
                month_ago = hist['Close'].iloc[0]
                monthly_return = ((current - month_ago) / month_ago) * 100

                sector_performance[sector_name] = monthly_return

        # Sort by performance
        sorted_sectors = sorted(sector_performance.items(), key=lambda x: x[1], reverse=True)

        # Create performance summary
        top_performers = sorted_sectors[:3]
        bottom_performers = sorted_sectors[-3:]

        performance_text = "📊 Sector Performance (1 Month):\n\n"
        performance_text += "🚀 Top Performers:\n"
        for sector, perf in top_performers:
            performance_text += f"- {sector}: {perf:+.2f}%\n"

        performance_text += "\n📉 Underperformers:\n"
        for sector, perf in bottom_performers:
            performance_text += f"- {sector}: {perf:+.2f}%\n"

        # Investment recommendation
        best_sector = top_performers[0][0] if top_performers else "Technology"
        performance_text += f"\n💡 Investment Insight:\n"
        performance_text += f"- {best_sector} sector showing strongest momentum\n"
        performance_text += f"- Consider sector rotation strategies\n"
        performance_text += f"- Diversification across sectors recommended\n"

        return performance_text

    except Exception as e:
        return f"Error fetching sector performance: {str(e)}"