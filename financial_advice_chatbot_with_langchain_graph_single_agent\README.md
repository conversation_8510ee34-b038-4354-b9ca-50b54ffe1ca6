# 💰 PennyWise - Enhanced AI Financial Advisor

**Advanced LangGraph financial advisor with built-in LangChain Memory, Vector Database, and AI Planning**

## 🚀 Enhanced Features

### 🧠 **LangChain Built-in Memory**
- **Conversation Memory**: Remembers your chat history using `ConversationSummaryBufferMemory`
- **User Profiles**: Extracts and stores user information automatically
- **Redis Support**: Optional persistent memory storage
- **Memory Summarization**: Intelligent conversation summarization

### 📊 **Vector Database (ChromaDB)**
- **Financial Knowledge Base**: Pre-loaded with financial planning knowledge
- **Semantic Search**: Find relevant financial advice using embeddings
- **User Context Storage**: Stores user-specific insights
- **HuggingFace Embeddings**: Free, high-quality embeddings

### 📋 **LangChain AI Planning**
- **Plan-and-Execute**: Uses LangChain's built-in planning system
- **Financial Planning**: Creates comprehensive financial strategies
- **Step-by-step Plans**: Breaks down complex financial goals
- **Investment Strategies**: Automated portfolio recommendations

### 📈 **Real Market Data**
- **Yahoo Finance**: Real-time stock prices and company data
- **Alpha Vantage**: Economic indicators (GDP, inflation, unemployment)
- **REST Countries**: Geographic and economic country data
- **Market Sentiment**: VIX, indices, and sector performance

## 🏗️ Architecture

```
Enhanced PennyWise System
├── 🧠 LangChain Memory
│   ├── ConversationSummaryBufferMemory
│   ├── RedisChatMessageHistory (optional)
│   └── User Profile Extraction
├── 📊 Vector Database (ChromaDB)
│   ├── Financial Knowledge Base
│   ├── HuggingFace Embeddings
│   └── Semantic Search & Retrieval
├── 📋 LangChain Planning
│   ├── Plan-and-Execute Agent
│   ├── Financial Strategy Creation
│   └── Investment Planning
├── 🛠️ Real Data Tools
│   ├── Stock Analysis (Yahoo Finance)
│   ├── Economic Indicators (Alpha Vantage)
│   ├── Geographic Analysis (REST Countries)
│   └── Market Sentiment Analysis
└── 🔄 LangGraph Workflow
    ├── Query Analysis
    ├── Context Retrieval
    ├── AI Planning
    ├── Data Analysis
    └── Response Synthesis
```

## 🚀 Quick Start

### 1. **Setup Environment**
```bash
# Clone and navigate
cd financial_advice_chatbot_with_langchain_graph(single_agent)

# Copy environment template
cp .env.example .env

# Edit .env with your API keys
# Required: GROQ_API_KEY
# Optional: ALPHA_VANTAGE_API_KEY, REDIS_URL
```

### 2. **Install Dependencies**
```bash
pip install -r requirements.txt
```

### 3. **Test the System**
```bash
python test_enhanced_system.py
```

### 4. **Test Memory System**
```bash
python test_memory_system.py
```

### 5. **Run Locally**
```bash
streamlit run main.py
```

### 6. **Run with Docker (Recommended)**
```bash
docker-compose up --build
# Access at http://localhost:8501
```

## 💡 Example Queries

### **Memory-Powered Conversations**
```
"I'm 30, earn $100k, and want to retire at 65"
"Based on our previous conversation, what should I do next?"
"Remember that I'm risk-averse, should I invest in tech stocks?"
```

### **AI Planning Requests**
```
"Create a comprehensive financial plan for me"
"Give me a step-by-step retirement strategy"
"Plan my investment approach for the next 20 years"
```

### **Real Market Analysis**
```
"What's the current market sentiment and how should I adjust?"
"Analyze AAPL stock and create an investment plan"
"How are European markets performing for long-term investment?"
```

## 🧠 Built-in LangChain Features

### **Memory System**
- Uses `ConversationSummaryBufferMemory` for intelligent conversation tracking
- Optional Redis integration for persistent storage
- Automatic user profile extraction and updates

### **Vector Database**
- ChromaDB with HuggingFace embeddings
- Pre-loaded financial knowledge base
- Contextual compression retrieval

### **Planning System**
- LangChain's `PlanAndExecute` for complex reasoning
- Custom financial planning chains
- Investment strategy generation

## 📊 System Components

| Component | Technology | Purpose |
|-----------|------------|---------|
| **Memory** | LangChain ConversationSummaryBufferMemory | Conversation tracking |
| **Vector DB** | ChromaDB + HuggingFace Embeddings | Knowledge retrieval |
| **Planning** | LangChain Plan-and-Execute | AI planning |
| **Workflow** | LangGraph StateGraph | Orchestration |
| **LLM** | Groq Llama3-8B | Language model |
| **Data** | Yahoo Finance, Alpha Vantage | Real market data |
| **UI** | Streamlit | User interface |
| **Storage** | Redis (optional) | Persistent memory |

## 🔧 Configuration

### **Environment Variables**
```bash
# Required
GROQ_API_KEY=your_groq_key

# Memory System Configuration
REDIS_URL=redis://localhost:6379
MONGODB_URL=mongodb://localhost:27017
MONGODB_DATABASE=pennywise_financial_advisor
QDRANT_URL=http://localhost:6333

# Optional
ALPHA_VANTAGE_API_KEY=your_av_key
```

### **Three-Tier Memory Architecture**

#### **🧬 Semantic Memory (Qdrant)**
- **Purpose**: Financial knowledge, concepts, and semantic relationships
- **Storage**: Qdrant vector database
- **Embeddings**: `sentence-transformers/all-MiniLM-L6-v2`
- **Features**: Similarity search, contextual compression, financial knowledge base

#### **🎯 Behavioral Memory (MongoDB)**
- **Purpose**: User behavioral patterns, preferences, decision history
- **Storage**: MongoDB collections
- **Features**: Risk tolerance, decision patterns, market behavior analysis
- **Learning**: Adapts to user's financial decision-making style

#### **📚 Episodic Memory (MongoDB)**
- **Purpose**: Conversation episodes, experiences, contextual memories
- **Storage**: MongoDB collections
- **Features**: Conversation history, financial experiences, temporal context
- **Tracking**: User satisfaction, goal achievement, emotional journey

#### **🔴 Short-term Memory (Redis)**
- **Purpose**: Session data, temporary calculations, real-time cache
- **Storage**: Redis in-memory database
- **Features**: Fast session management, conversation buffer

## 🎯 Key Benefits

### **vs Basic Chatbot**
✅ **Remembers conversations** (LangChain memory)  
✅ **Accesses knowledge base** (Vector database)  
✅ **Creates detailed plans** (AI planning)  
✅ **Uses real market data** (Live APIs)  
✅ **Learns from interactions** (Context storage)  

### **vs Simple LangChain**
✅ **Advanced workflows** (LangGraph orchestration)  
✅ **Multi-step reasoning** (Plan-and-execute)  
✅ **Persistent memory** (Redis integration)  
✅ **Financial expertise** (Domain knowledge base)  
✅ **Real-time data** (Market APIs)  

## 🧪 Testing

```bash
# Test all components
python test_enhanced_system.py

# Test individual components
python -c "from memory.enhanced_memory import memory; print('Memory OK')"
python -c "from memory.vector_memory import vector_memory; print('Vector DB OK')"
python -c "from graphs.planning_system import planning_system; print('Planning OK')"
```

## 🐳 Docker Deployment

The system includes all three memory databases:

```yaml
services:
  pennywise-enhanced:  # Main application
  qdrant:             # Semantic memory (financial knowledge)
  mongodb:            # Behavioral & episodic memory
  redis:              # Short-term session memory
```

**Volumes:**
- `qdrant_data`: Semantic memory storage
- `mongodb_data`: Behavioral & episodic memory storage
- `redis_data`: Session memory storage
- `logs`: Application logs

## 📈 Performance

- **Response Time**: 2-5 seconds (with planning)
- **Memory Usage**: ~500MB (with vector DB)
- **API Calls**: 1-3 per query (depending on complexity)
- **Confidence**: 85-95% (with multiple data sources)

## 🎉 What You Get

**A production-ready financial advisor with:**
- 🧠 **Smart Memory** that remembers your conversations
- 📊 **Knowledge Base** with financial expertise
- 📋 **AI Planning** that creates step-by-step strategies
- 📈 **Real Data** from live market sources
- 🔄 **Advanced Workflows** using LangGraph
- 🐳 **Easy Deployment** with Docker

**Built with the latest LangChain/LangGraph features for maximum capability!** 🚀
