from langchain.tools import tool
import requests
from typing import Optional

@tool
def get_real_country_data(country: str) -> str:
    """Get real country economic and demographic data using REST Countries API and World Bank data"""
    try:
        # Get basic country info from REST Countries API (free, no key needed)
        country_url = f"https://restcountries.com/v3.1/name/{country}"
        country_response = requests.get(country_url)
        
        if country_response.status_code != 200:
            return f"Could not find data for country: {country}"
        
        country_data = country_response.json()[0]
        
        # Extract key information
        country_name = country_data.get('name', {}).get('common', country)
        capital = country_data.get('capital', ['N/A'])[0] if country_data.get('capital') else 'N/A'
        population = country_data.get('population', 'N/A')
        region = country_data.get('region', 'N/A')
        subregion = country_data.get('subregion', 'N/A')
        currencies = country_data.get('currencies', {})
        
        # Get currency info
        currency_info = "N/A"
        if currencies:
            currency_code = list(currencies.keys())[0]
            currency_name = currencies[currency_code].get('name', currency_code)
            currency_info = f"{currency_name} ({currency_code})"
        
        # Get additional economic context based on country
        economic_context = get_economic_context(country_name)
        
        return f"""
        Real Country Analysis - {country_name}:
        
        🏛️ Basic Information:
        - Capital: {capital}
        - Population: {population:,} people
        - Region: {region} ({subregion})
        - Currency: {currency_info}
        
        💼 Economic Context:
        {economic_context}
        
        🌍 Investment Considerations:
        - Currency risk: Consider hedging if investing from different currency zone
        - Market access: {region} markets generally accessible to international investors
        - Diversification: Adds geographic diversification to portfolio
        
        📊 Recommendation: Research local market ETFs or ADRs for exposure to {country_name} economy.
        """
        
    except Exception as e:
        return f"Error fetching country data for {country}: {str(e)}"

def get_economic_context(country_name: str) -> str:
    """Get economic context based on country"""
    country_lower = country_name.lower()
    
    economic_profiles = {
        'germany': """
        - Largest economy in Europe, strong manufacturing base
        - Key sectors: Automotive, machinery, chemicals, technology
        - Stable political environment, EU member
        - Known for: Engineering excellence, export-oriented economy
        - Investment appeal: Blue-chip companies, dividend stocks
        """,
        'france': """
        - Second largest EU economy, diverse industrial base
        - Key sectors: Luxury goods, aerospace, energy, finance
        - Stable democracy, EU founding member
        - Known for: Consumer brands, infrastructure, nuclear energy
        - Investment appeal: Consumer discretionary, utilities
        """,
        'united kingdom': """
        - Major financial center (London), service-based economy
        - Key sectors: Financial services, technology, pharmaceuticals
        - Post-Brexit adjustments ongoing
        - Known for: Banking, fintech innovation, life sciences
        - Investment appeal: Financial sector, healthcare, tech
        """,
        'japan': """
        - Third largest global economy, technological leader
        - Key sectors: Technology, automotive, robotics, gaming
        - Aging population, low interest rates
        - Known for: Innovation, quality manufacturing, efficiency
        - Investment appeal: Technology stocks, automation, healthcare
        """,
        'united states': """
        - World's largest economy, innovation hub
        - Key sectors: Technology, healthcare, finance, energy
        - Reserve currency advantage, deep capital markets
        - Known for: Tech giants, pharmaceutical innovation, financial markets
        - Investment appeal: Growth stocks, tech sector, healthcare
        """
    }
    
    # Find matching country profile
    for country_key, profile in economic_profiles.items():
        if country_key in country_lower:
            return profile
    
    # Default profile for other countries
    return f"""
    - Emerging or developed market with unique characteristics
    - Research specific sectors and economic drivers
    - Consider political stability and regulatory environment
    - Evaluate currency stability and inflation trends
    - Investment appeal: Depends on specific market conditions
    """