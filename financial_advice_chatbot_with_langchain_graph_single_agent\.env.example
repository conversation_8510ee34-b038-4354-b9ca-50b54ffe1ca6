# Enhanced PennyWise Configuration
# Copy this file to .env and fill in your API keys

# Required: Groq API for LLM
GROQ_API_KEY=your_groq_api_key_here

# Optional: Alpha Vantage for economic data (free tier: 25 requests/day)
ALPHA_VANTAGE_API_KEY=your_alpha_vantage_key_here

# Memory Configuration
# Redis for short-term memory (sessions, cache, temporary data)
REDIS_URL=redis://localhost:6379

# MongoDB for long-term storage (user profiles, conversation history, financial plans)
MONGODB_URL=mongodb://localhost:27017
MONGODB_DATABASE=pennywise_financial_advisor

# Qdrant for semantic memory (financial knowledge, embeddings)
QDRANT_URL=http://localhost:6333

# <PERSON><PERSON>mith disabled (paid service not needed)
LANGCHAIN_TRACING_V2=false

# API Key Sources:
# - Groq: https://console.groq.com/ (Free tier available)
# - Alpha Vantage: https://www.alphavantage.co/support/#api-key (Free tier: 25 requests/day)
# - Lang<PERSON>mith: https://smith.langchain.com/ (Optional, for debugging)
