# Core LangChain/LangGraph for Multi-Agent
langchain
langgraph
langchain-groq
langchain-experimental

# MCP (Model Context Protocol) Support
mcp-use
mcp[cli]
langchain-mcp-adapters
mcp
pydantic
httpx
websockets

# Memory Systems
redis
pymongo
motor
mongoengine
qdrant-client
langchain-qdrant

# ML and Embeddings
sentence-transformers
langchain-huggingface
torch
transformers

# Financial Data APIs
yfinance
requests
pandas
numpy

# UI and Environment
streamlit
python-dotenv

# Safety and Guardrails
better-profanity
textstat

# Utilities
tiktoken
asyncio-mqtt
aiofiles

# Multi-Agent Communication
celery
kombu
redis[hiredis]

# Monitoring and Logging
structlog
prometheus-client

# Development
pytest
pytest-asyncio
black
flake8
