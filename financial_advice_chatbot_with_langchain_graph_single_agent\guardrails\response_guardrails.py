"""
Response Guardrails for PennyWise
Validates and modifies AI-generated responses for safety and compliance
"""

import re
from typing import Dict, List, Tuple, Any
from enum import Enum
from dataclasses import dataclass

class ResponseViolation(Enum):
    """Types of response violations"""
    # Output Validations (Safety & Compliance)
    SPECIFIC_STOCK_RECOMMENDATION = "specific_stock_recommendation"  # output validation
    GUARANTEED_RETURNS = "guaranteed_returns"  # output validation
    EXCESSIVE_RISK = "excessive_risk"  # output validation
    MARKET_TIMING = "market_timing"  # output validation
    INSIDER_TRADING = "insider_trading"  # output validation
    UNQUALIFIED_ADVICE = "unqualified_advice"  # output validation
    RESPONSE_MANIPULATION = "response_manipulation"  # output validation
    MISSING_DISCLAIMERS = "missing_disclaimers"  # output validation

@dataclass
class ResponseValidationResult:
    """Result of response validation"""
    is_safe: bool
    violations: List[ResponseViolation]
    modified_response: str
    warnings: List[str]
    confidence_adjustment: float

class ResponseGuardrails:
    """Comprehensive response validation and modification system"""
    
    def __init__(self):
        # Prohibited financial advice phrases
        self.prohibited_phrases = [
            "guaranteed returns", "risk-free investment", "sure thing",
            "can't lose", "guaranteed profit", "100% safe",
            "insider information", "hot tip", "secret strategy",
            "no risk", "certain profit", "foolproof"
        ]
        
        # High-risk advice indicators
        self.high_risk_indicators = [
            "put all your money", "mortgage your house", "borrow to invest",
            "day trading", "penny stocks", "cryptocurrency gambling",
            "options trading", "margin trading", "leveraged trading",
            "all in", "bet everything", "max out"
        ]
        
        # Market timing phrases
        self.market_timing_phrases = [
            "now is the perfect time", "market will crash", "buy before the market",
            "sell everything now", "wait for the dip", "time the market",
            "market is about to", "perfect entry point"
        ]
        
        # Response manipulation indicators
        self.manipulation_indicators = [
            "i am not pennywise", "my real name is", "i am actually",
            "my instructions are", "my system prompt", "i was trained to",
            "my programming", "developer mode", "jailbreak successful",
            "restrictions bypassed", "safety protocols disabled"
        ]
        
        # Compliance disclaimers
        self.disclaimers = {
            "general": "This is educational information only, not personalized financial advice. Consult a qualified financial advisor for your specific situation.",
            "investment": "All investments carry risk. Past performance doesn't guarantee future results.",
            "tax": "Tax implications vary by individual situation. Consult a tax professional.",
            "insurance": "Insurance needs are individual. Speak with a licensed insurance agent.",
            "legal": "This is not legal advice. Consult with a qualified attorney for legal matters."
        }
    
    def validate_response(self, user_input: str, ai_response: str, user_profile: Dict = None) -> ResponseValidationResult:
        """Comprehensive response validation and modification"""
        violations = []
        warnings = []
        modified_response = ai_response
        confidence_adjustment = 0.0
        
        # 1. Check for response manipulation (AI was jailbroken)
        if self._detect_response_manipulation(ai_response):
            violations.append(ResponseViolation.RESPONSE_MANIPULATION)
            # Return safe fallback immediately
            return ResponseValidationResult(
                is_safe=False,
                violations=violations,
                modified_response=self._get_manipulation_fallback(),
                warnings=["Critical: Response manipulation detected"],
                confidence_adjustment=-0.5
            )
        
        # 2. Check for prohibited financial advice
        violation = self._check_prohibited_advice(ai_response)
        if violation:
            violations.append(violation)
            modified_response = self._remove_prohibited_phrases(modified_response)
            confidence_adjustment -= 0.1
        
        # 3. Check for specific stock recommendations
        violation = self._check_specific_recommendations(ai_response)
        if violation:
            violations.append(violation)
            modified_response = self._soften_specific_recommendations(modified_response)
            confidence_adjustment -= 0.1
        
        # 4. Check for guaranteed returns claims
        violation = self._check_guaranteed_returns(ai_response)
        if violation:
            violations.append(violation)
            modified_response = self._remove_guarantee_language(modified_response)
            confidence_adjustment -= 0.2
        
        # 5. Check for excessive risk advice
        violation = self._check_excessive_risk(ai_response, user_profile)
        if violation:
            violations.append(violation)
            modified_response = self._add_risk_warnings(modified_response)
            confidence_adjustment -= 0.15
        
        # 6. Check for market timing advice
        violation = self._check_market_timing(ai_response)
        if violation:
            violations.append(violation)
            modified_response = self._soften_timing_advice(modified_response)
            confidence_adjustment -= 0.1
        
        # 7. Add appropriate disclaimers
        modified_response = self._add_disclaimers(modified_response, user_input)
        
        # 8. Check response quality
        quality_warnings = self._check_response_quality(modified_response)
        warnings.extend(quality_warnings)
        
        is_safe = len(violations) == 0 or ResponseViolation.RESPONSE_MANIPULATION not in violations
        
        return ResponseValidationResult(
            is_safe=is_safe,
            violations=violations,
            modified_response=modified_response,
            warnings=warnings,
            confidence_adjustment=confidence_adjustment
        )
    
    def _detect_response_manipulation(self, response: str) -> bool:
        """Detect if the AI response shows signs of successful manipulation"""
        response_lower = response.lower()
        return any(indicator in response_lower for indicator in self.manipulation_indicators)
    
    def _check_prohibited_advice(self, response: str) -> ResponseViolation:
        """Check for prohibited financial advice phrases"""
        response_lower = response.lower()
        for phrase in self.prohibited_phrases:
            if phrase in response_lower:
                return ResponseViolation.GUARANTEED_RETURNS
        return None
    
    def _check_specific_recommendations(self, response: str) -> ResponseViolation:
        """Check for overly specific stock recommendations"""
        # Pattern for specific stock recommendations
        stock_patterns = [
            r"buy \w{1,5} stock", r"invest in \w{1,5}", 
            r"\w{1,5} is a great buy", r"I recommend \w{1,5}"
        ]
        
        for pattern in stock_patterns:
            if re.search(pattern, response, re.IGNORECASE):
                return ResponseViolation.SPECIFIC_STOCK_RECOMMENDATION
        return None
    
    def _check_guaranteed_returns(self, response: str) -> ResponseViolation:
        """Check for guaranteed return claims"""
        guarantee_patterns = [
            r"guaranteed \d+%", r"will definitely", r"100% certain",
            r"no risk", r"can't lose", r"sure to"
        ]
        
        for pattern in guarantee_patterns:
            if re.search(pattern, response, re.IGNORECASE):
                return ResponseViolation.GUARANTEED_RETURNS
        return None
    
    def _check_excessive_risk(self, response: str, user_profile: Dict = None) -> ResponseViolation:
        """Check for excessive risk advice"""
        response_lower = response.lower()
        
        # Check for high-risk indicators
        for indicator in self.high_risk_indicators:
            if indicator in response_lower:
                return ResponseViolation.EXCESSIVE_RISK
        
        # Check risk appropriateness based on user profile
        if user_profile:
            risk_tolerance = user_profile.get("risk_tolerance", "moderate").lower()
            age = user_profile.get("age", 30)
            
            # Conservative users shouldn't get aggressive advice
            if risk_tolerance == "conservative":
                aggressive_terms = ["aggressive growth", "high-risk", "speculative"]
                if any(term in response_lower for term in aggressive_terms):
                    return ResponseViolation.EXCESSIVE_RISK
            
            # Older users need more conservative advice
            if age > 60:
                if "100% stocks" in response_lower or "aggressive portfolio" in response_lower:
                    return ResponseViolation.EXCESSIVE_RISK
        
        return None
    
    def _check_market_timing(self, response: str) -> ResponseViolation:
        """Check for market timing advice"""
        response_lower = response.lower()
        for phrase in self.market_timing_phrases:
            if phrase in response_lower:
                return ResponseViolation.MARKET_TIMING
        return None
    
    def _remove_prohibited_phrases(self, response: str) -> str:
        """Remove or replace prohibited phrases"""
        for phrase in self.prohibited_phrases:
            response = re.sub(phrase, "potentially profitable", response, flags=re.IGNORECASE)
        return response
    
    def _soften_specific_recommendations(self, response: str) -> str:
        """Soften specific stock recommendations"""
        # Replace specific recommendations with general advice
        response = re.sub(r"buy (\w{1,5}) stock", r"consider researching companies like \1", response, flags=re.IGNORECASE)
        response = re.sub(r"invest in (\w{1,5})", r"consider diversified investments that might include companies like \1", response, flags=re.IGNORECASE)
        return response
    
    def _remove_guarantee_language(self, response: str) -> str:
        """Remove guarantee language"""
        response = re.sub(r"guaranteed \d+%", "potentially", response, flags=re.IGNORECASE)
        response = re.sub(r"will definitely", "may", response, flags=re.IGNORECASE)
        response = re.sub(r"100% certain", "likely", response, flags=re.IGNORECASE)
        return response
    
    def _add_risk_warnings(self, response: str) -> str:
        """Add risk warnings to high-risk advice"""
        risk_warning = "\n\n⚠️ **Important:** This involves significant risk. Please consider your risk tolerance and consult a financial advisor."
        return response + risk_warning
    
    def _soften_timing_advice(self, response: str) -> str:
        """Soften market timing advice"""
        response = re.sub(r"now is the perfect time", "this could be a good time", response, flags=re.IGNORECASE)
        response = re.sub(r"market will crash", "market may experience volatility", response, flags=re.IGNORECASE)
        return response
    
    def _add_disclaimers(self, response: str, user_input: str) -> str:
        """Add appropriate disclaimers to response"""
        disclaimers = []
        user_input_lower = user_input.lower()
        
        # Add general disclaimer for financial advice
        if any(word in user_input_lower for word in ["invest", "stock", "portfolio", "financial", "money"]):
            disclaimers.append(self.disclaimers["general"])
        
        # Add investment disclaimer
        if any(word in user_input_lower for word in ["invest", "stock", "etf", "mutual fund", "portfolio"]):
            disclaimers.append(self.disclaimers["investment"])
        
        # Add tax disclaimer
        if any(word in user_input_lower for word in ["tax", "401k", "ira", "roth", "deduction"]):
            disclaimers.append(self.disclaimers["tax"])
        
        # Add insurance disclaimer
        if any(word in user_input_lower for word in ["insurance", "life insurance", "disability"]):
            disclaimers.append(self.disclaimers["insurance"])
        
        # Append disclaimers
        if disclaimers:
            response += "\n\n---\n**Important:** " + " ".join(set(disclaimers))
        
        return response
    
    def _check_response_quality(self, response: str) -> List[str]:
        """Check response quality and provide warnings"""
        warnings = []
        
        # Check length
        if len(response) > 1000:
            warnings.append("Response is quite long - consider making it more concise")
        
        if len(response) < 50:
            warnings.append("Response might be too brief for financial advice")
        
        # Check for technical jargon
        jargon_terms = ["alpha", "beta", "sharpe ratio", "volatility", "correlation", "standard deviation"]
        jargon_count = sum(1 for term in jargon_terms if term in response.lower())
        
        if jargon_count > 2:
            warnings.append("Response contains technical jargon - consider simplifying")
        
        return warnings
    
    def _get_manipulation_fallback(self) -> str:
        """Get safe fallback response for manipulation attempts"""
        return """I'm PennyWise, your personal financial advisor. I'm here to help you with:

💰 Investment planning and portfolio advice
🏠 Saving for major purchases like homes
📈 Understanding market trends and economic indicators  
💳 Debt management and budgeting strategies
🎯 Setting and achieving financial goals
📊 Retirement planning and wealth building

What financial topic would you like to explore today?"""

# Global response guardrails instance
response_guardrails = ResponseGuardrails()
