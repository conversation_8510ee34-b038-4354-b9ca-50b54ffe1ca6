"""
Response Agent - Response Synthesis and Generation
Handles final response synthesis, formatting, and delivery
"""

import asyncio
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime

logger = logging.getLogger(__name__)

class ResponseAgent:
    """Agent responsible for response synthesis and generation"""
    
    def __init__(self):
        self.is_initialized = False
        self.llm = None
        
    async def initialize(self):
        """Initialize the response agent"""
        try:
            logger.info("Initializing Response Agent")
            
            # Initialize LLM
            await self._initialize_llm()
            
            self.is_initialized = True
            logger.info("✅ Response Agent initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize Response Agent: {e}")
            raise
    
    async def _initialize_llm(self):
        """Initialize the language model"""
        try:
            from langchain_groq import ChatGroq
            from config.dynamic_config import get_database_config
            
            config = get_database_config()
            
            if config.groq_api_key:
                self.llm = ChatGroq(
                    groq_api_key=config.groq_api_key,
                    model_name="llama3-70b-8192",
                    temperature=0.1,
                    max_tokens=1000
                )
                logger.info("LLM initialized with Groq")
            else:
                logger.warning("No Groq API key provided - using fallback responses")
                self.llm = None
                
        except Exception as e:
            logger.error(f"Failed to initialize LLM: {e}")
            self.llm = None

    async def generate_response(self, user_id: str, query: str, context: Dict[str, Any],
                              agent_responses: Dict[str, Any]) -> Dict[str, Any]:
        """Generate final response from agent inputs"""
        try:
            if not self.is_initialized:
                return {"success": False, "error": "Response agent not initialized"}

            # Build synthesis context
            synthesis_context = {
                "user_id": user_id,
                "user_query": query,
                "user_profile": context.get("user_profile", {}),
                "agent_responses": agent_responses,
                "conversation_history": context.get("conversation_history", [])
            }

            # Generate response using LLM or fallback
            if self.llm:
                response_text = await self._generate_llm_response(synthesis_context)
            else:
                response_text = await self._generate_fallback_response(synthesis_context)

            # Calculate confidence
            confidence = self._calculate_confidence(agent_responses, context)

            return {
                "success": True,
                "response": response_text,
                "confidence": confidence,
                "synthesis_method": "llm" if self.llm else "fallback"
            }

        except Exception as e:
            logger.error(f"Failed to generate response: {e}")
            return {
                "success": False,
                "error": str(e),
                "response": "I apologize, but I encountered an error processing your request. Please try again."
            }

    async def synthesize_response(self, user_id: str, query: str, context: Dict[str, Any],
                                reasoning: Dict[str, Any], data_results: List[Dict[str, Any]], 
                                planning: Optional[Dict[str, Any]]) -> Dict[str, Any]:
        """Synthesize final response from all agent inputs"""
        try:
            # Prepare synthesis context
            synthesis_context = self._prepare_synthesis_context(
                user_id, query, context, reasoning, data_results, planning
            )
            
            # Generate response
            if self.llm:
                response = await self._generate_llm_response(synthesis_context)
            else:
                response = await self._generate_fallback_response(synthesis_context)
            
            # Format and enhance response
            formatted_response = self._format_response(response, synthesis_context)
            
            # Calculate confidence
            confidence = self._calculate_response_confidence(synthesis_context, response)
            
            return {
                "response": formatted_response,
                "confidence": confidence,
                "synthesis_context": synthesis_context,
                "generation_method": "llm" if self.llm else "fallback",
                "generated_at": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Failed to synthesize response: {e}")
            return {
                "response": "I apologize, but I encountered an error processing your request. Please try again.",
                "confidence": 0.1,
                "error": str(e)
            }
    
    def _prepare_synthesis_context(self, user_id: str, query: str, context: Dict[str, Any], 
                                 reasoning: Dict[str, Any], data_results: List[Dict[str, Any]], 
                                 planning: Optional[Dict[str, Any]]) -> Dict[str, Any]:
        """Prepare context for response synthesis"""
        synthesis_context = {
            "user_query": query,
            "user_profile": context.get('user_profile', {}),
            "conversation_history": context.get('conversation_history', []),
            "feedback_history": context.get('feedback_history', []),
            "reasoning_analysis": reasoning,
            "data_sources": [],
            "planning_info": planning,
            "key_insights": []
        }
        
        # Process data results
        for result in data_results:
            if result.success and result.data:
                agent_name = result.agent_name
                agent_data = result.data
                
                synthesis_context["data_sources"].append({
                    "agent": agent_name,
                    "data": agent_data,
                    "confidence": agent_data.get("confidence", 0.7)
                })
        
        # Extract key insights
        synthesis_context["key_insights"] = self._extract_key_insights(synthesis_context)
        
        return synthesis_context
    
    def _extract_key_insights(self, synthesis_context: Dict[str, Any]) -> List[str]:
        """Extract key insights from all available data"""
        insights = []
        
        # User profile insights
        user_profile = synthesis_context.get("user_profile", {})
        if user_profile:
            age = user_profile.get("age")
            income = user_profile.get("income")
            risk_tolerance = user_profile.get("risk_tolerance")
            
            if age:
                if age < 30:
                    insights.append("Young investor with long time horizon for growth")
                elif age > 50:
                    insights.append("Approaching retirement - consider risk management")
            
            if income and income > 100000:
                insights.append("High income allows for aggressive savings and investment")
            
            if risk_tolerance:
                insights.append(f"User has {risk_tolerance} risk tolerance")
        
        # Feedback insights
        feedback_history = synthesis_context.get("feedback_history", [])
        if feedback_history:
            recent_feedback = feedback_history[-3:]  # Last 3 feedback items
            negative_feedback = [f for f in recent_feedback if "NEGATIVE" in str(f)]
            if negative_feedback:
                insights.append("User has given negative feedback recently - adjust approach")
            
            positive_feedback = [f for f in recent_feedback if "POSITIVE" in str(f)]
            if positive_feedback:
                insights.append("User has been satisfied with recent responses")
        
        # Data source insights
        data_sources = synthesis_context.get("data_sources", [])
        for source in data_sources:
            agent = source.get("agent", "")
            data = source.get("data", {})
            
            if agent == "financial" and data:
                if "stock_analysis" in data:
                    insights.append("Real-time stock data available for analysis")
                if "market_analysis" in data:
                    insights.append("Current market conditions analyzed")
            
            if agent == "market" and data:
                insights.append("Market sentiment and trends considered")
        
        return insights
    
    async def _generate_llm_response(self, synthesis_context: Dict[str, Any]) -> str:
        """Generate response using LLM"""
        try:
            # Build system prompt
            system_prompt = self._build_system_prompt(synthesis_context)
            
            # Build user prompt
            user_prompt = self._build_user_prompt(synthesis_context)
            
            # Generate response
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ]
            
            response = await self.llm.ainvoke(messages)
            return response.content
            
        except Exception as e:
            logger.error(f"Failed to generate LLM response: {e}")
            return await self._generate_fallback_response(synthesis_context)
    
    def _build_system_prompt(self, synthesis_context: Dict[str, Any]) -> str:
        """Build system prompt for LLM"""
        user_profile = synthesis_context.get("user_profile", {})
        key_insights = synthesis_context.get("key_insights", [])
        
        system_prompt = """You are PennyWise, a friendly and knowledgeable financial advisor. 

Guidelines:
- Keep responses SHORT and conversational (2-3 paragraphs max)
- Be encouraging and supportive
- Use simple language, avoid jargon
- Give practical, actionable advice
- Always consider user's specific situation"""
        
        # Add user profile context
        if user_profile:
            system_prompt += f"\n\nUser Profile:"
            if user_profile.get("age"):
                system_prompt += f"\n- Age: {user_profile['age']}"
            if user_profile.get("income"):
                system_prompt += f"\n- Income: ${user_profile['income']:,}"
            if user_profile.get("risk_tolerance"):
                system_prompt += f"\n- Risk Tolerance: {user_profile['risk_tolerance']}"
        
        # Add key insights
        if key_insights:
            system_prompt += f"\n\nKey Insights:\n" + "\n".join(f"- {insight}" for insight in key_insights)
        
        # Add feedback consideration
        feedback_history = synthesis_context.get("feedback_history", [])
        if feedback_history:
            system_prompt += "\n\nIMPORTANT: Consider user's previous feedback when crafting your response."
        
        return system_prompt
    
    def _build_user_prompt(self, synthesis_context: Dict[str, Any]) -> str:
        """Build user prompt for LLM"""
        query = synthesis_context.get("user_query", "")
        
        user_prompt = f"User Question: {query}\n\n"
        
        # Add reasoning context
        reasoning = synthesis_context.get("reasoning_analysis", {})
        if reasoning:
            reasoning_type = reasoning.get("reasoning_type", "analytical")
            user_prompt += f"Reasoning Approach: Use {reasoning_type} reasoning\n\n"
        
        # Add data context
        data_sources = synthesis_context.get("data_sources", [])
        if data_sources:
            user_prompt += "Available Data:\n"
            for source in data_sources:
                agent = source.get("agent", "")
                data = source.get("data", {})
                user_prompt += f"- {agent.title()} Agent: {self._summarize_data(data)}\n"
            user_prompt += "\n"
        
        # Add planning context
        planning_info = synthesis_context.get("planning_info")
        if planning_info and planning_info.get("planning_steps"):
            user_prompt += "Planning Steps Available: Yes (mention that you've created a plan)\n\n"
        
        user_prompt += "Please provide a helpful, personalized response."
        
        return user_prompt
    
    def _summarize_data(self, data: Dict[str, Any]) -> str:
        """Summarize data for prompt"""
        if not data:
            return "No data"
        
        # Summarize based on data type
        if "stock_analysis" in data:
            return "Real-time stock prices and analysis"
        elif "market_analysis" in data:
            return "Current market indices and trends"
        elif "economic_analysis" in data:
            return "Economic indicators and data"
        else:
            return f"Analysis data ({len(data)} items)"
    
    async def _generate_fallback_response(self, synthesis_context: Dict[str, Any]) -> str:
        """Generate fallback response when LLM is not available"""
        query = synthesis_context.get("user_query", "").lower()
        user_profile = synthesis_context.get("user_profile", {})
        
        # Basic response templates
        if any(word in query for word in ['invest', 'investment']):
            response = "For investment advice, I'd recommend starting with a diversified portfolio that matches your risk tolerance. "
            if user_profile.get("age") and user_profile["age"] < 35:
                response += "Given your young age, you have time for growth-oriented investments. "
            response += "Consider low-cost index funds as a foundation for your portfolio."
            
        elif any(word in query for word in ['retire', 'retirement']):
            response = "Retirement planning is crucial for your financial future. "
            if user_profile.get("income"):
                target = user_profile["income"] * 10
                response += f"Aim to save 10-15 times your annual income (around ${target:,}) by retirement. "
            response += "Start with employer 401(k) matching, then consider IRAs for additional savings."
            
        elif any(word in query for word in ['budget', 'budgeting']):
            response = "Creating a budget is a great first step! Try the 50/30/20 rule: 50% for needs, 30% for wants, and 20% for savings and debt repayment. "
            response += "Track your expenses for a month to see where your money goes, then adjust accordingly."
            
        else:
            response = "Thank you for your financial question. While I'd love to provide specific advice, "
            response += "I recommend consulting with a qualified financial advisor who can review your complete situation. "
            response += "In the meantime, focus on building an emergency fund and paying down high-interest debt."
        
        return response
    
    def _format_response(self, response: str, synthesis_context: Dict[str, Any]) -> str:
        """Format and enhance the response"""
        # Ensure response ends properly
        if not response.endswith('.'):
            response += '.'
        
        # Add disclaimer if needed
        if any(word in response.lower() for word in ['invest', 'stock', 'bond', 'portfolio']):
            if 'risk' not in response.lower():
                response += "\n\nRemember that all investments carry risk, and past performance doesn't guarantee future results."
        
        return response
    
    def _calculate_response_confidence(self, synthesis_context: Dict[str, Any], response: str) -> float:
        """Calculate confidence in the response"""
        base_confidence = 0.7
        
        # Increase confidence if we have user profile
        if synthesis_context.get("user_profile"):
            base_confidence += 0.1
        
        # Increase confidence if we have data sources
        data_sources = synthesis_context.get("data_sources", [])
        if data_sources:
            base_confidence += 0.1
        
        # Increase confidence if we have reasoning
        if synthesis_context.get("reasoning_analysis"):
            base_confidence += 0.05
        
        # Decrease confidence if using fallback
        if not self.llm:
            base_confidence -= 0.2
        
        # Ensure confidence is between 0 and 1
        return max(0.1, min(1.0, base_confidence))
    
    async def execute_task(self, task_type: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """Execute a response task"""
        try:
            if task_type == "synthesize_response":
                return await self.synthesize_response(
                    data['user_id'],
                    data['query'],
                    data['context'],
                    data['reasoning'],
                    data['data_results'],
                    data.get('planning')
                )
            else:
                return {'success': False, 'error': f'Unknown task type: {task_type}'}
                
        except Exception as e:
            logger.error(f"Failed to execute response task {task_type}: {e}")
            return {'success': False, 'error': str(e)}
    
    def update_config(self, config):
        """Update configuration"""
        try:
            # Reinitialize LLM with new config
            asyncio.create_task(self._initialize_llm())
            logger.info("Updated response agent configuration")
        except Exception as e:
            logger.error(f"Failed to update config: {e}")
