"""
Memory Agent - Multi-Agent Memory Management
Handles all memory operations across the multi-agent system
"""

import asyncio
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime

logger = logging.getLogger(__name__)

class MemoryAgent:
    """Agent responsible for all memory operations"""
    
    def __init__(self):
        self.memory_systems = {}
        self.is_initialized = False
        
    async def initialize(self):
        """Initialize the memory agent and all memory systems"""
        try:
            logger.info("Initializing Memory Agent")
            
            # Initialize memory systems
            await self._initialize_memory_systems()
            
            self.is_initialized = True
            logger.info("✅ Memory Agent initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize Memory Agent: {e}")
            raise
    
    async def _initialize_memory_systems(self):
        """Initialize all memory systems"""
        try:
            from memory.short_term_memory import ShortTermMemory
            from memory.semantic_memory import SemanticMemory
            from memory.behavioral_memory import BehavioralMemory
            from memory.episodic_memory import EpisodicMemory
            from memory.long_term_memory import LongTermMemory

            # Initialize memory systems
            self.memory_systems = {
                'short_term': ShortTermMemory(),
                'semantic': SemanticMemory(),
                'behavioral': BehavioralMemory(),
                'episodic': EpisodicMemory(),
                'long_term': LongTermMemory()
            }
            
            # Initialize each system
            for name, system in self.memory_systems.items():
                await system.initialize()
                logger.info(f"Initialized {name} memory system")
                
        except Exception as e:
            logger.error(f"Failed to initialize memory systems: {e}")
            raise
    
    async def get_context(self, user_id: str, query: str) -> Dict[str, Any]:
        """Get comprehensive context for a user query"""
        try:
            context = {
                'user_profile': {},
                'conversation_history': [],
                'semantic_knowledge': {},
                'behavioral_patterns': {},
                'episodic_memories': [],
                'feedback_history': []
            }
            
            # Get short-term memory (conversation history, user profile)
            if 'short_term' in self.memory_systems:
                short_term_data = await self.memory_systems['short_term'].get_context(user_id, query)
                context['user_profile'] = short_term_data.get('user_profile', {})
                context['conversation_history'] = short_term_data.get('conversation_history', [])
                context['feedback_history'] = short_term_data.get('feedback_history', [])
            
            # Get semantic memory (financial knowledge)
            if 'semantic' in self.memory_systems:
                semantic_data = await self.memory_systems['semantic'].search_knowledge(query)
                context['semantic_knowledge'] = semantic_data
            
            # Get behavioral memory (user patterns)
            if 'behavioral' in self.memory_systems:
                behavioral_data = await self.memory_systems['behavioral'].get_user_patterns(user_id)
                context['behavioral_patterns'] = behavioral_data
            
            # Get episodic memory (past interactions)
            if 'episodic' in self.memory_systems:
                episodic_data = await self.memory_systems['episodic'].get_relevant_episodes(user_id, query)
                context['episodic_memories'] = episodic_data
            
            logger.info(f"Retrieved comprehensive context for user {user_id}")
            return context
            
        except Exception as e:
            logger.error(f"Failed to get context: {e}")
            return {}
    
    async def store_conversation(self, user_id: str, query: str, response: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Store a conversation in all relevant memory systems"""
        try:
            results = {}
            
            # Store in short-term memory
            if 'short_term' in self.memory_systems:
                short_term_result = await self.memory_systems['short_term'].add_conversation(
                    user_id, query, response
                )
                results['short_term'] = short_term_result
            
            # Store in episodic memory
            if 'episodic' in self.memory_systems:
                episodic_result = await self.memory_systems['episodic'].store_episode(
                    user_id, query, response, context
                )
                results['episodic'] = episodic_result
            
            # Update behavioral patterns
            if 'behavioral' in self.memory_systems:
                behavioral_result = await self.memory_systems['behavioral'].update_patterns(
                    user_id, query, response, context
                )
                results['behavioral'] = behavioral_result
            
            logger.info(f"Stored conversation for user {user_id}")
            return {'success': True, 'results': results}
            
        except Exception as e:
            logger.error(f"Failed to store conversation: {e}")
            return {'success': False, 'error': str(e)}
    
    async def store_feedback(self, user_id: str, query: str, response: str, feedback_data: Dict[str, Any]) -> Dict[str, Any]:
        """Store user feedback in memory systems"""
        try:
            results = {}
            
            # Store feedback in short-term memory for immediate LLM access
            if 'short_term' in self.memory_systems:
                feedback_message = self._format_feedback_for_llm(feedback_data)
                short_term_result = await self.memory_systems['short_term'].add_conversation(
                    user_id, "SYSTEM_FEEDBACK", feedback_message
                )
                results['short_term'] = short_term_result
            
            # Store feedback in behavioral memory for pattern analysis
            if 'behavioral' in self.memory_systems:
                behavioral_result = await self.memory_systems['behavioral'].store_feedback(
                    user_id, query, response, feedback_data
                )
                results['behavioral'] = behavioral_result
            
            # Store feedback in episodic memory for context
            if 'episodic' in self.memory_systems:
                episodic_result = await self.memory_systems['episodic'].store_feedback_episode(
                    user_id, query, response, feedback_data
                )
                results['episodic'] = episodic_result
            
            logger.info(f"Stored feedback for user {user_id}")
            return {'success': True, 'results': results}
            
        except Exception as e:
            logger.error(f"Failed to store feedback: {e}")
            return {'success': False, 'error': str(e)}
    
    def _format_feedback_for_llm(self, feedback_data: Dict[str, Any]) -> str:
        """Format feedback data for LLM consumption"""
        rating = feedback_data.get('rating', 3)
        comment = feedback_data.get('comment', '')
        feedback_type = feedback_data.get('type', 'general')
        
        if rating >= 4:
            feedback_type_label = "POSITIVE_FEEDBACK"
            instruction = "Continue this approach - user found it helpful"
        elif rating <= 2:
            feedback_type_label = "NEGATIVE_FEEDBACK"
            instruction = "Improve approach - be more detailed and careful"
        else:
            feedback_type_label = "NEUTRAL_FEEDBACK"
            instruction = "Maintain current approach with minor adjustments"
        
        message = f"[{feedback_type_label}] User rated response {rating}/5 stars"
        if comment:
            message += f" Comment: '{comment}'"
        message += f" INSTRUCTION: {instruction}"
        
        return message
    
    async def get_user_profile(self, user_id: str) -> Dict[str, Any]:
        """Get user profile from memory"""
        try:
            if 'short_term' in self.memory_systems:
                profile = await self.memory_systems['short_term'].get_user_profile(user_id)
                return profile
            return {}
        except Exception as e:
            logger.error(f"Failed to get user profile: {e}")
            return {}
    
    async def update_user_profile(self, user_id: str, profile_data: Dict[str, Any]) -> Dict[str, Any]:
        """Update user profile in memory"""
        try:
            results = {}
            
            # Update in short-term memory
            if 'short_term' in self.memory_systems:
                short_term_result = await self.memory_systems['short_term'].save_user_profile(
                    user_id, profile_data
                )
                results['short_term'] = short_term_result
            
            # Update behavioral patterns based on profile
            if 'behavioral' in self.memory_systems:
                behavioral_result = await self.memory_systems['behavioral'].update_user_profile(
                    user_id, profile_data
                )
                results['behavioral'] = behavioral_result
            
            logger.info(f"Updated user profile for {user_id}")
            return {'success': True, 'results': results}
            
        except Exception as e:
            logger.error(f"Failed to update user profile: {e}")
            return {'success': False, 'error': str(e)}
    
    async def search_knowledge(self, query: str, limit: int = 5) -> Dict[str, Any]:
        """Search semantic knowledge base"""
        try:
            if 'semantic' in self.memory_systems:
                results = await self.memory_systems['semantic'].search_knowledge(query, limit)
                return results
            return {}
        except Exception as e:
            logger.error(f"Failed to search knowledge: {e}")
            return {}
    
    async def get_conversation_insights(self, user_id: str) -> Dict[str, Any]:
        """Get insights about user's conversation history"""
        try:
            insights = {
                'total_conversations': 0,
                'topics_discussed': [],
                'feedback_summary': {},
                'engagement_level': 'medium',
                'recent_interests': []
            }
            
            # Get insights from behavioral memory
            if 'behavioral' in self.memory_systems:
                behavioral_insights = await self.memory_systems['behavioral'].get_user_insights(user_id)
                insights.update(behavioral_insights)
            
            # Get conversation count from episodic memory
            if 'episodic' in self.memory_systems:
                episodic_insights = await self.memory_systems['episodic'].get_conversation_stats(user_id)
                insights.update(episodic_insights)
            
            return insights
            
        except Exception as e:
            logger.error(f"Failed to get conversation insights: {e}")
            return {}
    
    async def execute_task(self, task_type: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """Execute a memory-related task"""
        try:
            if task_type == "get_context":
                return await self.get_context(data['user_id'], data['query'])
            elif task_type == "store_conversation":
                return await self.store_conversation(
                    data['user_id'], data['query'], data['response'], data.get('context', {})
                )
            elif task_type == "store_feedback":
                return await self.store_feedback(
                    data['user_id'], data['query'], data['response'], data['feedback_data']
                )
            elif task_type == "get_user_profile":
                return await self.get_user_profile(data['user_id'])
            elif task_type == "update_user_profile":
                return await self.update_user_profile(data['user_id'], data['profile_data'])
            elif task_type == "search_knowledge":
                return await self.search_knowledge(data['query'], data.get('limit', 5))
            elif task_type == "get_insights":
                return await self.get_conversation_insights(data['user_id'])
            else:
                return {'success': False, 'error': f'Unknown task type: {task_type}'}
                
        except Exception as e:
            logger.error(f"Failed to execute memory task {task_type}: {e}")
            return {'success': False, 'error': str(e)}
    
    def update_config(self, config):
        """Update configuration for memory systems"""
        try:
            for system in self.memory_systems.values():
                if hasattr(system, 'update_config'):
                    system.update_config(config)
            logger.info("Updated memory systems configuration")
        except Exception as e:
            logger.error(f"Failed to update memory config: {e}")
