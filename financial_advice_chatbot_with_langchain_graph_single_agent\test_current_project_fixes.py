#!/usr/bin/env python3
"""
Test script to verify current project fixes are working
"""

def test_feedback_memory_integration():
    """Test that feedback is stored in memory and can be retrieved"""
    print("🧪 Testing Enhanced Feedback Memory Integration")
    print("=" * 50)

    try:
        from memory.enhanced_memory import memory

        # Test storing complete interaction with feedback
        test_user_id = "test_feedback_user_enhanced"
        test_query = "What should I invest in?"
        test_response = "I recommend starting with a diversified portfolio of low-cost index funds. Consider your risk tolerance and time horizon when making investment decisions."
        test_feedback = "This response was too vague and didn't consider my specific situation"

        # Clear any existing conversation
        memory.clear_conversation_history(test_user_id)

        # Store complete interaction with feedback
        memory.store_interaction_with_feedback(test_user_id, test_query, test_response, test_feedback, 2)
        print("✅ Complete interaction with feedback stored successfully")

        # Retrieve conversation history
        conversation = memory.get_conversation_history(test_user_id)
        print(f"📝 Conversation length: {len(str(conversation)) if conversation else 0} characters")

        # Check for structured feedback entry
        if "[FEEDBACK_ENTRY]" in str(conversation):
            print("✅ Structured feedback entry found in conversation")

            # Check for all components
            if test_query in str(conversation):
                print("✅ Original query found in feedback entry")
            else:
                print("❌ Original query not found in feedback entry")
                return False

            if test_response[:50] in str(conversation):
                print("✅ Original response found in feedback entry")
            else:
                print("❌ Original response not found in feedback entry")
                return False

            if test_feedback in str(conversation):
                print("✅ Feedback text found in feedback entry")
            else:
                print("❌ Feedback text not found in feedback entry")
                return False

            print("✅ All feedback components properly stored!")
            return True
        else:
            print("❌ Structured feedback entry not found")
            print(f"📝 Conversation content: {str(conversation)[:300]}...")
            return False

    except Exception as e:
        print(f"❌ Error testing feedback integration: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_simplified_feedback_ui():
    """Test that feedback UI changes are in place"""
    print("\n🧪 Testing Simplified Feedback UI")
    print("=" * 50)
    
    try:
        # Check if main.py has the simplified feedback structure
        with open("main.py", "r") as f:
            content = f.read()
        
        # Check for thumbs up/down buttons
        if "👍 Good" in content and "👎 Poor" in content:
            print("✅ Thumbs up/down buttons found")
        else:
            print("❌ Thumbs up/down buttons not found")
            return False
        
        # Check for comment section
        if "💬 Add Comment" in content:
            print("✅ Comment section found")
        else:
            print("❌ Comment section not found")
            return False
        
        # Check that star ratings are removed
        if "select_slider" not in content or "⭐" not in content:
            print("✅ Star ratings removed")
        else:
            print("❌ Star ratings still present")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing feedback UI: {e}")
        return False

def test_combined_thinking_process():
    """Test that planning and reasoning are combined"""
    print("\n🧪 Testing Combined Thinking Process")
    print("=" * 50)

    try:
        # Check if main.py has the combined thinking process
        with open("main.py", "r") as f:
            content = f.read()

        # Check for combined thinking button
        if "🧠 Show My Thinking" in content:
            print("✅ Combined thinking button found")
        else:
            print("❌ Combined thinking button not found")
            return False

        # Check for display_thinking_process function
        if "display_thinking_process" in content:
            print("✅ Combined thinking display function found")
        else:
            print("❌ Combined thinking display function not found")
            return False

        # Check that separate planning/reasoning buttons are removed
        if "Show Planning" not in content and "Show Reasoning" not in content:
            print("✅ Separate planning/reasoning buttons removed")
        else:
            print("❌ Separate planning/reasoning buttons still present")
            return False

        # Check for variable definition fixes
        if "analysis_type = thinking_data.get('analysis_type'" in content:
            print("✅ analysis_type variable properly defined")
        else:
            print("❌ analysis_type variable not properly defined")
            return False

        # Check for other variable fixes
        if "vector_context = thinking_data.get('vector_context'" in content:
            print("✅ vector_context variable properly defined")
        else:
            print("❌ vector_context variable not properly defined")
            return False

        return True

    except Exception as e:
        print(f"❌ Error testing thinking process: {e}")
        return False

def test_workflow_feedback_retrieval():
    """Test that workflow can retrieve and use feedback"""
    print("\n🧪 Testing Workflow Feedback Retrieval")
    print("=" * 50)

    try:
        from graphs.enhanced_workflow import EnhancedFinancialWorkflow
        from memory.enhanced_memory import memory

        # Create workflow instance
        workflow = EnhancedFinancialWorkflow()

        # Test user with feedback
        test_user_id = "test_workflow_feedback"
        test_query = "Should I invest in stocks?"
        test_response = "Yes, stocks can be good for long-term growth."
        test_feedback = "Please provide more specific advice for my age and income"

        # Clear and store interaction with feedback
        memory.clear_conversation_history(test_user_id)
        memory.store_interaction_with_feedback(test_user_id, test_query, test_response, test_feedback, 2)

        # Create state for workflow
        state = {
            "user_id": test_user_id,
            "user_input": "What about bonds?",
            "vector_context": "",
            "comprehensive_context": {}
        }

        # Test context retrieval
        updated_state = workflow.retrieve_context(state)
        vector_context = updated_state.get("vector_context", "")

        # Check if feedback is included in context
        if "PREVIOUS USER FEEDBACK" in vector_context:
            print("✅ Feedback section found in context")

            if test_feedback in vector_context:
                print("✅ Specific feedback text found in context")
            else:
                print("❌ Specific feedback text not found in context")
                return False

            if "CRITICAL: Use this feedback" in vector_context:
                print("✅ Feedback instruction found in context")
            else:
                print("❌ Feedback instruction not found in context")
                return False

            print("✅ Workflow can successfully retrieve and process feedback!")
            return True
        else:
            print("❌ Feedback section not found in context")
            print(f"📝 Context content: {vector_context[:300]}...")
            return False

    except Exception as e:
        print(f"❌ Error testing workflow feedback retrieval: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all tests"""
    print("🔧 TESTING ENHANCED CURRENT PROJECT FIXES")
    print("=" * 70)

    test1_passed = test_feedback_memory_integration()
    test2_passed = test_simplified_feedback_ui()
    test3_passed = test_combined_thinking_process()
    test4_passed = test_workflow_feedback_retrieval()

    print("\n" + "=" * 70)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 70)

    print(f"1. Enhanced Feedback Memory: {'✅ PASSED' if test1_passed else '❌ FAILED'}")
    print(f"2. Simplified Feedback UI: {'✅ PASSED' if test2_passed else '❌ FAILED'}")
    print(f"3. Combined Thinking Process: {'✅ PASSED' if test3_passed else '❌ FAILED'}")
    print(f"4. Workflow Feedback Retrieval: {'✅ PASSED' if test4_passed else '❌ FAILED'}")

    if all([test1_passed, test2_passed, test3_passed, test4_passed]):
        print("\n🎉 ALL ENHANCED FIXES WORKING PERFECTLY!")
        print("\n📋 ENHANCED FEEDBACK SYSTEM FEATURES:")
        print("✅ Structured feedback storage with query, response, and feedback")
        print("✅ Enhanced feedback retrieval in workflow")
        print("✅ Bot remembers previous feedback in next responses")
        print("✅ Removed non-working close planning button")
        print("✅ Combined thinking process without errors")

        print("\n🚀 READY FOR COMPREHENSIVE TESTING:")
        print("1. Start: streamlit run main.py")
        print("2. Ask: 'What should I invest in?'")
        print("3. Give feedback: Use 👎 'This was too vague'")
        print("4. Ask: 'What feedback did I give you?'")
        print("5. Bot should remember and mention your previous feedback!")
        print("6. Test 'Show My Thinking' - should work without errors")
    else:
        print("\n⚠️ Some enhanced fixes need attention. Check the implementation.")

    print("\n🚀 NEW MULTI-AGENT PROJECT READY!")
    print("📁 Location: ../financial_advisor_multi_agent/")
    print("🐳 Start with: cd ../financial_advisor_multi_agent && docker-compose up -d")
    print("🌐 Access at: http://localhost:8501")

    return all([test1_passed, test2_passed, test3_passed, test4_passed])

if __name__ == "__main__":
    main()
