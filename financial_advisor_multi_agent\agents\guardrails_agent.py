"""
Guardrails Agent - Safety and Compliance
Handles input validation, output safety, and compliance checks
"""

import asyncio
import logging
from typing import Dict, Any, List, Optional
import re

logger = logging.getLogger(__name__)

class GuardrailsAgent:
    """Agent responsible for safety and compliance"""
    
    def __init__(self):
        self.is_initialized = False
        self.blocked_patterns = []
        self.financial_keywords = []
        
    async def initialize(self):
        """Initialize the guardrails agent"""
        try:
            logger.info("Initializing Guardrails Agent")
            
            # Initialize blocked patterns
            self.blocked_patterns = [
                r'\b(?:hack|crack|illegal|fraud|scam|ponzi|pyramid)\b',
                r'\b(?:insider trading|market manipulation)\b',
                r'\b(?:guaranteed returns?|risk-free|100% profit)\b',
                r'\b(?:get rich quick|easy money|no risk)\b'
            ]
            
            # Initialize financial keywords for relevance checking
            self.financial_keywords = [
                'invest', 'investment', 'stock', 'bond', 'portfolio', 'retirement',
                'budget', 'save', 'saving', 'debt', 'loan', 'credit', 'mortgage',
                'insurance', 'tax', 'taxes', 'finance', 'financial', 'money',
                'income', 'expense', 'asset', 'liability', 'equity', 'fund',
                'market', 'trading', 'broker', 'advisor', 'planning', 'goal'
            ]
            
            self.is_initialized = True
            logger.info("✅ Guardrails Agent initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize Guardrails Agent: {e}")
            raise
    
    async def validate_input(self, user_id: str, query: str) -> Dict[str, Any]:
        """Validate user input for safety and relevance"""
        try:
            validation_result = {
                "is_safe": True,
                "is_relevant": True,
                "confidence": 1.0,
                "flags": [],
                "recommendations": []
            }
            
            # Check for blocked patterns
            safety_check = self._check_safety(query)
            if not safety_check["is_safe"]:
                validation_result["is_safe"] = False
                validation_result["flags"].extend(safety_check["flags"])
                validation_result["recommendations"].extend(safety_check["recommendations"])
            
            # Check relevance to financial topics
            relevance_check = self._check_relevance(query)
            if not relevance_check["is_relevant"]:
                validation_result["is_relevant"] = False
                validation_result["flags"].extend(relevance_check["flags"])
                validation_result["recommendations"].extend(relevance_check["recommendations"])
            
            # Check for PII (Personal Identifiable Information)
            pii_check = self._check_pii(query)
            if pii_check["has_pii"]:
                validation_result["flags"].extend(pii_check["flags"])
                validation_result["recommendations"].extend(pii_check["recommendations"])
            
            # Calculate overall confidence
            validation_result["confidence"] = self._calculate_confidence(validation_result)
            
            return validation_result
            
        except Exception as e:
            logger.error(f"Failed to validate input: {e}")
            return {
                "is_safe": False,
                "is_relevant": False,
                "confidence": 0.0,
                "flags": ["validation_error"],
                "error": str(e)
            }
    
    async def validate_output(self, response: str) -> Dict[str, Any]:
        """Validate AI response for safety and compliance"""
        try:
            validation_result = {
                "is_safe": True,
                "is_compliant": True,
                "confidence": 1.0,
                "flags": [],
                "recommendations": []
            }
            
            # Check for inappropriate financial advice
            advice_check = self._check_financial_advice(response)
            if not advice_check["is_appropriate"]:
                validation_result["is_safe"] = False
                validation_result["flags"].extend(advice_check["flags"])
                validation_result["recommendations"].extend(advice_check["recommendations"])
            
            # Check for compliance issues
            compliance_check = self._check_compliance(response)
            if not compliance_check["is_compliant"]:
                validation_result["is_compliant"] = False
                validation_result["flags"].extend(compliance_check["flags"])
                validation_result["recommendations"].extend(compliance_check["recommendations"])
            
            # Check response quality
            quality_check = self._check_response_quality(response)
            validation_result["flags"].extend(quality_check["flags"])
            validation_result["recommendations"].extend(quality_check["recommendations"])
            
            # Calculate overall confidence
            validation_result["confidence"] = self._calculate_confidence(validation_result)
            
            return validation_result
            
        except Exception as e:
            logger.error(f"Failed to validate output: {e}")
            return {
                "is_safe": False,
                "is_compliant": False,
                "confidence": 0.0,
                "flags": ["validation_error"],
                "error": str(e)
            }
    
    def _check_safety(self, query: str) -> Dict[str, Any]:
        """Check query for safety issues"""
        flags = []
        recommendations = []
        
        query_lower = query.lower()
        
        # Check blocked patterns
        for pattern in self.blocked_patterns:
            if re.search(pattern, query_lower):
                flags.append("inappropriate_content")
                recommendations.append("Please ask about legitimate financial topics only")
                break
        
        # Check for inappropriate requests
        if any(word in query_lower for word in ['hack', 'illegal', 'fraud', 'scam']):
            flags.append("illegal_activity")
            recommendations.append("I can only provide advice on legal financial activities")
        
        # Check for unrealistic expectations
        if any(phrase in query_lower for phrase in ['guaranteed profit', 'risk-free', '100% return']):
            flags.append("unrealistic_expectations")
            recommendations.append("Remember that all investments carry some level of risk")
        
        return {
            "is_safe": len(flags) == 0,
            "flags": flags,
            "recommendations": recommendations
        }
    
    def _check_relevance(self, query: str) -> Dict[str, Any]:
        """Check if query is relevant to financial topics"""
        flags = []
        recommendations = []
        
        query_lower = query.lower()
        
        # Check for financial keywords
        has_financial_keywords = any(keyword in query_lower for keyword in self.financial_keywords)
        
        # Check for financial question patterns
        financial_patterns = [
            r'\b(?:how to|should i|what is|explain|help with)\b.*\b(?:invest|save|budget|retire)\b',
            r'\b(?:money|financial|finance|investment|retirement|budget)\b',
            r'\b(?:stock|bond|fund|portfolio|401k|ira|roth)\b'
        ]
        
        has_financial_patterns = any(re.search(pattern, query_lower) for pattern in financial_patterns)
        
        # Allow greetings and common conversational starters
        greeting_patterns = [
            r'\b(?:hello|hi|hey|good morning|good afternoon|good evening)\b',
            r'\b(?:thanks|thank you|please|help)\b',
            r'\b(?:what can you do|what do you do|who are you)\b'
        ]
        
        is_greeting = any(re.search(pattern, query_lower) for pattern in greeting_patterns)
        
        # Be more lenient - allow if it has financial keywords, patterns, or is a greeting
        if not has_financial_keywords and not has_financial_patterns and not is_greeting:
            # Only flag if it's clearly non-financial and not a greeting
            if len(query.split()) > 3:  # Only check longer queries
                non_financial_indicators = ['weather', 'sports', 'cooking', 'travel', 'music', 'movies']
                if any(indicator in query_lower for indicator in non_financial_indicators):
                    flags.append("not_financial_topic")
                    recommendations.append("Please ask questions related to personal finance, investing, budgeting, or financial planning")
        
        return {
            "is_relevant": len(flags) == 0,
            "flags": flags,
            "recommendations": recommendations
        }
    
    def _check_pii(self, query: str) -> Dict[str, Any]:
        """Check for Personal Identifiable Information"""
        flags = []
        recommendations = []
        
        # Check for SSN patterns
        ssn_pattern = r'\b\d{3}-?\d{2}-?\d{4}\b'
        if re.search(ssn_pattern, query):
            flags.append("potential_ssn")
            recommendations.append("Please don't share your Social Security Number")
        
        # Check for credit card patterns
        cc_pattern = r'\b\d{4}[\s-]?\d{4}[\s-]?\d{4}[\s-]?\d{4}\b'
        if re.search(cc_pattern, query):
            flags.append("potential_credit_card")
            recommendations.append("Please don't share credit card numbers")
        
        # Check for account number patterns
        account_pattern = r'\b(?:account|acct)[\s#:]*\d{6,}\b'
        if re.search(account_pattern, query, re.IGNORECASE):
            flags.append("potential_account_number")
            recommendations.append("Please don't share account numbers")
        
        return {
            "has_pii": len(flags) > 0,
            "flags": flags,
            "recommendations": recommendations
        }
    
    def _check_financial_advice(self, response: str) -> Dict[str, Any]:
        """Check response for inappropriate financial advice"""
        flags = []
        recommendations = []
        
        response_lower = response.lower()
        
        # Check for inappropriate guarantees
        if any(phrase in response_lower for phrase in ['guaranteed', 'promise', 'certain profit', 'no risk']):
            flags.append("inappropriate_guarantees")
            recommendations.append("Avoid making guarantees about financial outcomes")
        
        # Check for specific investment recommendations without disclaimers
        if any(phrase in response_lower for phrase in ['you should buy', 'i recommend buying', 'definitely invest in']):
            if 'not financial advice' not in response_lower and 'consult' not in response_lower:
                flags.append("specific_investment_advice")
                recommendations.append("Include appropriate disclaimers for investment suggestions")
        
        # Check for tax advice
        if any(phrase in response_lower for phrase in ['tax deduction', 'tax strategy', 'tax planning']):
            if 'tax professional' not in response_lower and 'accountant' not in response_lower:
                flags.append("tax_advice_without_disclaimer")
                recommendations.append("Recommend consulting a tax professional for tax advice")
        
        return {
            "is_appropriate": len(flags) == 0,
            "flags": flags,
            "recommendations": recommendations
        }
    
    def _check_compliance(self, response: str) -> Dict[str, Any]:
        """Check response for compliance issues"""
        flags = []
        recommendations = []
        
        response_lower = response.lower()
        
        # Check for required disclaimers
        if any(phrase in response_lower for phrase in ['invest', 'investment', 'stock', 'bond']):
            if 'risk' not in response_lower:
                flags.append("missing_risk_disclosure")
                recommendations.append("Include risk disclosure for investment advice")
        
        # Check for fiduciary language
        if any(phrase in response_lower for phrase in ['i guarantee', 'i promise', 'definitely will']):
            flags.append("inappropriate_certainty")
            recommendations.append("Avoid language that implies certainty in financial outcomes")
        
        return {
            "is_compliant": len(flags) == 0,
            "flags": flags,
            "recommendations": recommendations
        }
    
    def _check_response_quality(self, response: str) -> Dict[str, Any]:
        """Check response quality"""
        flags = []
        recommendations = []
        
        # Check response length
        if len(response) < 50:
            flags.append("response_too_short")
            recommendations.append("Provide more detailed and helpful responses")
        elif len(response) > 2000:
            flags.append("response_too_long")
            recommendations.append("Keep responses concise and focused")
        
        # Check for helpful structure
        if len(response.split('.')) < 2:
            flags.append("lacks_structure")
            recommendations.append("Structure responses with clear points")
        
        return {
            "flags": flags,
            "recommendations": recommendations
        }
    
    def _calculate_confidence(self, validation_result: Dict[str, Any]) -> float:
        """Calculate confidence score based on validation results"""
        base_confidence = 1.0
        
        # Reduce confidence for each flag
        flag_count = len(validation_result.get("flags", []))
        confidence_reduction = flag_count * 0.1
        
        # Ensure minimum confidence
        final_confidence = max(0.1, base_confidence - confidence_reduction)
        
        return round(final_confidence, 2)
    
    async def execute_task(self, task_type: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """Execute a guardrails task"""
        try:
            if task_type == "validate_input":
                return await self.validate_input(data['user_id'], data['query'])
            elif task_type == "validate_output":
                return await self.validate_output(data['response'])
            else:
                return {'success': False, 'error': f'Unknown task type: {task_type}'}
                
        except Exception as e:
            logger.error(f"Failed to execute guardrails task {task_type}: {e}")
            return {'success': False, 'error': str(e)}
    
    def update_config(self, config):
        """Update configuration"""
        try:
            logger.info("Updated guardrails agent configuration")
        except Exception as e:
            logger.error(f"Failed to update config: {e}")

