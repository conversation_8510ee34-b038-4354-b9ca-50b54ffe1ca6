#!/usr/bin/env python3
"""
Test syntax of feedback system
"""

def test_feedback_syntax():
    """Test that feedback_system.py has valid syntax"""
    try:
        # Try to import the feedback system
        import sys
        import os
        
        # Add current directory to path
        sys.path.insert(0, os.getcwd())
        
        # Try to compile the file
        with open("feedback/feedback_system.py", "r") as f:
            content = f.read()
        
        compile(content, "feedback/feedback_system.py", "exec")
        print("✅ feedback/feedback_system.py syntax is valid")
        
        # Try to import it
        from feedback.feedback_system import feedback_collector, feedback_analyzer
        print("✅ feedback_system imports successfully")
        
        return True
        
    except SyntaxError as e:
        print(f"❌ Syntax Error in feedback_system.py: {e}")
        print(f"Line {e.lineno}: {e.text}")
        return False
    except Exception as e:
        print(f"❌ Error testing feedback_system.py: {e}")
        return False

def test_main_imports():
    """Test that main.py can import everything"""
    try:
        # Test individual imports
        imports_to_test = [
            "from graphs.enhanced_workflow import enhanced_workflow",
            "from memory.enhanced_memory import memory", 
            "from graphs.planning_system import planning_system",
            "from guardrails.unified_guardrails import financial_guardrails",
            "from utils.llm_manager import llm_manager",
            "from feedback.feedback_system import feedback_collector, feedback_analyzer"
        ]
        
        for import_statement in imports_to_test:
            try:
                exec(import_statement)
                print(f"✅ {import_statement}")
            except Exception as e:
                print(f"❌ {import_statement} - Error: {e}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing imports: {e}")
        return False

if __name__ == "__main__":
    print("🔍 TESTING SYNTAX AND IMPORTS")
    print("=" * 50)
    
    syntax_ok = test_feedback_syntax()
    imports_ok = test_main_imports()
    
    if syntax_ok and imports_ok:
        print("\n🎉 ALL SYNTAX AND IMPORTS OK!")
        print("✅ feedback_system.py syntax fixed")
        print("✅ All imports working")
        print("\n🚀 Ready to run: streamlit run main.py")
    else:
        print("\n⚠️ Some issues remain. Check the errors above.")
