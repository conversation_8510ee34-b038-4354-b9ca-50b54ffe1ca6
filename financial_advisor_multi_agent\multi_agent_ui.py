"""
Multi-Agent Financial Advisor Frontend
Streamlit interface with dynamic configuration
"""

import streamlit as st
import time
import uuid
import asyncio
from typing import Dict, Any

# Configure page
st.set_page_config(
    page_title="PennyWise Multi-Agent Financial Advisor",
    page_icon="🤖",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Import configuration and agents
try:
    from config.dynamic_config import config_manager, get_database_config, update_database_config, test_database_connections, get_system_status
    from agents.orchestrator_agent import OrchestratorAgent
except ImportError as e:
    st.error(f"Failed to import required modules: {e}")
    st.stop()

def initialize_session_state():
    """Initialize session state variables"""
    if 'user_id' not in st.session_state:
        st.session_state.user_id = str(uuid.uuid4())
    
    if 'session_id' not in st.session_state:
        st.session_state.session_id = str(uuid.uuid4())
    
    if 'messages' not in st.session_state:
        st.session_state.messages = []
    
    if 'agents_initialized' not in st.session_state:
        st.session_state.agents_initialized = False

def render_sidebar():
    """Render sidebar with dynamic configuration"""
    st.sidebar.title("🤖 Multi-Agent Config")
    
    # Dynamic MCP Server Management
    st.sidebar.markdown("### 🔧 Dynamic MCP Servers")
    
    with st.sidebar.expander("➕ Create New MCP Server"):
        server_name = st.text_input("Server Name", placeholder="my_custom_server")
        tool_name = st.text_input("Tool Name", placeholder="custom_tool")
        tool_description = st.text_area(
            "Tool Description", 
            placeholder="Describe what this tool does...",
            height=100
        )
        llm_api_key = st.text_input(
            "LLM API Key", 
            placeholder="gsk_...",
            help="Enter your Groq API key (starts with gsk_)",
            autocomplete="off"
        )
        
        if st.button("🚀 Create MCP Server"):
            if server_name and tool_name and tool_description and llm_api_key:
                with st.spinner("Creating MCP server..."):
                    from mcp.dynamic_server_manager import dynamic_mcp_manager
                    
                    result = asyncio.run(dynamic_mcp_manager.create_server({
                        "name": server_name,
                        "tool_name": tool_name,
                        "tool_description": tool_description,
                        "llm_api_key": llm_api_key
                    }))
                    
                    if result["success"]:
                        st.success(f"✅ Server '{server_name}' created!")
                        st.rerun()
                    else:
                        st.error(f"❌ Failed: {result['error']}")
            else:
                st.error("Please fill all fields")
    
    # Show existing MCP servers
    st.sidebar.markdown("### 📊 Active MCP Servers")
    try:
        from mcp.dynamic_server_manager import dynamic_mcp_manager
        servers = dynamic_mcp_manager.get_servers()
        
        if servers:
            for server_name, server_info in servers.items():
                status = server_info["status"]
                with st.sidebar.container():
                    col1, col2 = st.columns([3, 1])
                    with col1:
                        st.text(f"🔧 {server_name}")
                        st.caption(f"Port: {status['port']} | Tools: {status['tools_count']}")
                    with col2:
                        if st.button("🗑️", key=f"delete_{server_name}"):
                            asyncio.run(dynamic_mcp_manager.stop_server(server_name))
                            st.rerun()
        else:
            st.sidebar.info("No MCP servers running")
    except Exception as e:
        st.sidebar.error(f"Error loading servers: {e}")
    
    # System status
    st.sidebar.markdown("### 📊 System Status")
    if st.sidebar.button("📈 Show Status"):
        status = get_system_status()
        
        # Agent status
        st.sidebar.markdown("**Agents:**")
        for agent_name, agent_status in status.get('agents', {}).items():
            st.sidebar.text(f"🤖 {agent_name}: {agent_status}")
        
        # MCP servers
        st.sidebar.markdown("**MCP Servers:**")
        for server_name, server_status in status.get('mcp_servers', {}).items():
            st.sidebar.text(f"🔧 {server_name}: {server_status}")

def render_chat_interface():
    """Render the main chat interface"""
    st.title("🤖 PennyWise Multi-Agent Financial Advisor")
    st.markdown("*Powered by Multi-Agent Architecture with Dynamic Configuration*")
    
    # Display chat messages
    for message in st.session_state.messages:
        with st.chat_message(message["role"]):
            st.markdown(message["content"])
            
            # Show agent responses if available
            if message["role"] == "assistant" and "agent_responses" in message:
                with st.expander("🤖 Agent Details"):
                    agent_responses = message["agent_responses"]
                    for agent_name, response_data in agent_responses.items():
                        if response_data:
                            st.markdown(f"**{agent_name.title()} Agent:**")
                            if isinstance(response_data, dict):
                                for key, value in response_data.items():
                                    if key not in ['raw_data', 'debug_info']:
                                        st.text(f"  {key}: {value}")
                            else:
                                st.text(f"  {response_data}")
    
    # Chat input
    if prompt := st.chat_input("Ask me anything about your finances! 💰"):
        # Add user message
        st.session_state.messages.append({"role": "user", "content": prompt})
        
        # Display user message
        with st.chat_message("user"):
            st.markdown(prompt)
        
        # Process with multi-agent system
        with st.chat_message("assistant"):
            with st.spinner("🤖 Multi-agent system processing..."):
                try:
                    # Get orchestrator agent
                    agents = config_manager.get_agents()
                    if 'orchestrator' not in agents:
                        st.error("❌ Orchestrator agent not available")
                        return
                    
                    orchestrator = agents['orchestrator']
                    
                    # Process query through multi-agent system
                    start_time = time.time()
                    result = asyncio.run(orchestrator.process_user_query(
                        st.session_state.user_id, 
                        prompt
                    ))
                    processing_time = time.time() - start_time
                    
                    # Display response
                    response = result.get("response", "I apologize, but I couldn't process your request.")
                    st.markdown(response)
                    
                    # Store message with agent responses
                    message_data = {
                        "role": "assistant", 
                        "content": response,
                        "agent_responses": result.get("agent_responses", {}),
                        "processing_time": processing_time,
                        "confidence": result.get("confidence", 0.5)
                    }
                    st.session_state.messages.append(message_data)
                    
                    # Show processing time and confidence
                    col1, col2 = st.columns(2)
                    with col1:
                        st.metric("Processing Time", f"{processing_time:.1f}s")
                    with col2:
                        confidence_pct = int(result.get("confidence", 0.5) * 100)
                        st.metric("Confidence", f"{confidence_pct}%")
                    
                    # Show thinking process if available
                    if result.get("reasoning_result") or result.get("planning_steps"):
                        if st.button("🧠 Show My Thinking", key=f"thinking_{len(st.session_state.messages)}"):
                            st.markdown("### 🧠 How I Processed Your Question")
                            
                            # Show reasoning
                            reasoning = result.get("reasoning_result", {})
                            if reasoning:
                                st.markdown("**🔍 My Approach:**")
                                approach = reasoning.get("approach_used", "Standard analysis")
                                st.info(f"I used {approach.lower()} to understand your question")
                            
                            # Show planning steps
                            planning_steps = result.get("planning_steps", [])
                            if planning_steps:
                                st.markdown("**📋 My Planning Process:**")
                                for i, step in enumerate(planning_steps, 1):
                                    step_desc = step.get("description", "Processing step")
                                    status = step.get("status", "completed")
                                    st.markdown(f"{i}. {step_desc} ({'✅' if status == 'completed' else '⏳'})")
                            
                            # Show agent contributions
                            agent_responses = result.get("agent_responses", {})
                            if agent_responses:
                                st.markdown("**🤖 Agent Contributions:**")
                                for agent_name, response_data in agent_responses.items():
                                    if response_data and agent_name not in ['guardrails', 'response']:
                                        st.markdown(f"• **{agent_name.title()} Agent**: Provided {agent_name} analysis")
                    
                except Exception as e:
                    st.error(f"❌ Error: {str(e)}")
                    error_response = "I apologize, but I encountered an error. Please try again."
                    st.session_state.messages.append({"role": "assistant", "content": error_response})

def render_agent_monitor():
    """Render agent monitoring section"""
    st.markdown("---")
    st.markdown("### 🤖 Agent Monitor")
    
    agents = config_manager.get_agents()
    if agents:
        cols = st.columns(min(len(agents), 4))
        for i, (agent_name, agent) in enumerate(agents.items()):
            with cols[i % 4]:
                st.metric(
                    f"{agent_name.title()} Agent",
                    "Active" if agent else "Inactive",
                    delta="Online" if agent else "Offline"
                )
    else:
        st.info("No agents currently active")

async def initialize_agents():
    """Initialize the multi-agent system"""
    if st.session_state.agents_initialized:
        return True
    
    try:
        # Create a simple orchestrator with basic agents
        from agents.guardrails_agent import GuardrailsAgent
        
        # Initialize guardrails agent
        guardrails = GuardrailsAgent()
        await guardrails.initialize()
        
        # Create orchestrator
        orchestrator = OrchestratorAgent()
        orchestrator.agents = {"guardrails": guardrails}
        await orchestrator.initialize()
        
        # Store in config manager
        config_manager.set_agents({'orchestrator': orchestrator})
        
        st.session_state.agents_initialized = True
        return True
        
    except Exception as e:
        st.error(f"Failed to initialize agents: {e}")
        logger.error(f"Agent initialization error: {e}")
        return False

def main():
    """Main application function"""
    initialize_session_state()
    
    # Render sidebar
    render_sidebar()
    
    # Initialize agents if needed
    if not st.session_state.agents_initialized:
        with st.spinner("🤖 Initializing multi-agent system..."):
            success = asyncio.run(initialize_agents())
            if not success:
                st.error("❌ Failed to initialize agents")
                st.stop()
            else:
                st.success("✅ Multi-agent system initialized!")
                time.sleep(1)
                st.rerun()
    
    # Render main interface
    render_chat_interface()
    
    # Render agent monitor
    render_agent_monitor()

if __name__ == "__main__":
    main()
