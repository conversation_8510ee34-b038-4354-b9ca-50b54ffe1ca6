"""
PennyWise Multi-Agent Financial Advisor
Streamlit interface matching single agent UI with multi-agent backend
"""

import streamlit as st
import time
import uuid
import asyncio
import logging
from typing import Dict, Any
from dotenv import load_dotenv
import os

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Configure page
st.set_page_config(
    page_title="PennyWise - AI Financial Advisor",
    page_icon="💰",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Import configuration and agents
try:
    from config.dynamic_config import config_manager, get_database_config, update_database_config, test_database_connections, get_system_status
    from agents.orchestrator_agent import OrchestratorAgent
    from agents.memory_agent import MemoryAgent
except ImportError as e:
    st.error(f"Failed to import required modules: {e}")
    st.stop()

# Custom CSS
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }

    .user-profile {
        background-color: #f0f8ff;
        padding: 1rem;
        border-radius: 8px;
        margin: 1rem 0;
    }

    .chat-message {
        padding: 1rem;
        margin: 0.5rem 0;
        border-radius: 8px;
        font-weight: 500;
    }

    .streamlit-expanderHeader {
        background-color: #f8f9fa;
        border-radius: 5px;
        font-weight: bold;
    }
</style>
""", unsafe_allow_html=True)

def initialize_session_state():
    """Initialize session state variables"""
    if 'user_id' not in st.session_state:
        st.session_state.user_id = str(uuid.uuid4())
        print(f"DEBUG: Created new user session with user_id: {st.session_state.user_id}")
    else:
        print(f"DEBUG: Existing user session with user_id: {st.session_state.user_id}")

    if 'messages' not in st.session_state:
        st.session_state.messages = []

    if 'agents_initialized' not in st.session_state:
        st.session_state.agents_initialized = False

def get_user_profile(user_id: str) -> Dict[str, Any]:
    """Get user profile from memory agent"""
    try:
        agents = config_manager.get_agents()
        if 'memory' in agents:
            memory_agent = agents['memory']
            # Get user profile directly
            result = asyncio.run(memory_agent.execute_task("get_user_profile", {
                "user_id": user_id
            }))
            return result if isinstance(result, dict) and 'success' not in result else result.get('data', {})
        return {}
    except Exception as e:
        logger.error(f"Error getting user profile: {e}")
        return {}

def save_user_profile(user_id: str, profile_data: Dict[str, Any]) -> bool:
    """Save user profile through memory agent"""
    try:
        agents = config_manager.get_agents()
        if 'memory' in agents:
            memory_agent = agents['memory']
            # Store profile update
            result = asyncio.run(memory_agent.execute_task("update_user_profile", {
                "user_id": user_id,
                "profile_data": profile_data
            }))
            return result.get('success', False)
        return False
    except Exception as e:
        logger.error(f"Error saving user profile: {e}")
        return False

def render_sidebar():
    """Render sidebar with user profile and features"""
    st.sidebar.header("👤 Your Financial Profile")

    user_profile = get_user_profile(st.session_state.user_id)

    # User Profile Form
    with st.sidebar.form("user_profile_form"):
        st.write("**Quick Setup (Optional):**")

        col1, col2 = st.sidebar.columns(2)
        with col1:
            age = st.number_input("Age", min_value=18, max_value=100, value=user_profile.get("age", 30))
            income = st.number_input("Annual Income ($)", min_value=0, value=user_profile.get("income", 50000), step=5000)

        with col2:
            risk_tolerance = st.selectbox(
                "Risk Tolerance",
                ["Conservative", "Moderate", "Aggressive"],
                index=["conservative", "moderate", "aggressive"].index(user_profile.get("risk_tolerance", "moderate"))
            )

            goals = st.multiselect(
                "Financial Goals",
                ["Retirement", "House Purchase", "Education", "Wealth Building", "Emergency Fund"],
                default=user_profile.get("goals", [])
            )

        if st.form_submit_button("💾 Save Profile"):
            profile_data = {
                "age": age,
                "income": income,
                "risk_tolerance": risk_tolerance.lower(),
                "goals": goals
            }

            # Save to memory system
            if save_user_profile(st.session_state.user_id, profile_data):
                st.success("✅ Profile saved! I now know your preferences and will personalize my advice.")
                st.info("💡 Try asking me: 'What's my age?' or 'What are my financial goals?' to test it!")
            else:
                st.error("❌ Failed to save profile")

    # Show current profile if exists
    if user_profile and any(user_profile.values()):
        st.sidebar.markdown('<div class="user-profile">', unsafe_allow_html=True)
        st.sidebar.write("**Current Profile:**")
        if user_profile.get("age"):
            st.sidebar.write(f"🎂 Age: {user_profile['age']}")
        if user_profile.get("income"):
            st.sidebar.write(f"💰 Income: ${user_profile['income']:,}")
        if user_profile.get("risk_tolerance"):
            st.sidebar.write(f"⚖️ Risk: {user_profile['risk_tolerance'].title()}")
        if user_profile.get("goals"):
            st.sidebar.write(f"🎯 Goals: {', '.join(user_profile['goals'])}")
        st.sidebar.markdown('</div>', unsafe_allow_html=True)

    st.sidebar.markdown("---")

    st.sidebar.header("💡 What I Can Help With")
    st.sidebar.write("💰 Retirement Planning")
    st.sidebar.write("🏠 Home Buying")
    st.sidebar.write("📈 Investing")
    st.sidebar.write("💳 Debt Management")
    st.sidebar.write("🎯 Financial Goals")

    st.sidebar.markdown("---")

    # Try These section in sidebar
    st.sidebar.header("💡 Try These")

    example_queries = [
        "Help me plan for retirement",
        "Should I invest in stocks?",
        "How to buy my first house?",
        "Emergency fund advice",
        "Best way to invest $10,000",
        "Investment options for beginners"
    ]

    for i, query in enumerate(example_queries):
        if st.sidebar.button(query, key=f"sidebar_example_{i}"):
            st.session_state.example_query = query
            st.rerun()

    st.sidebar.markdown("---")

    # LLM Configuration section
    st.sidebar.header("🔑 LLM Configuration")

    # Get current API key info from config
    config = get_database_config()

    # Display current status
    if config.groq_api_key:
        st.sidebar.success("🔑 **API Key Configured**")
        st.sidebar.write(f"Key: {config.groq_api_key[:8]}...")
    else:
        st.sidebar.info("🔑 **Using Default Key**")
        st.sidebar.write("Key: Default system key")

    with st.sidebar.form("api_key_form"):
        st.write("**Update LLM API Key:**")

        new_api_key = st.text_input(
            "Groq API Key",
            placeholder="gsk_... (Enter your Groq API key)",
            help="💡 Get your free API key from: https://console.groq.com/keys",
            type="password"
        )

        col1, col2 = st.columns(2)
        with col1:
            if st.form_submit_button("🔄 Update Key"):
                if new_api_key.strip():
                    # Update the config (this would need to be implemented)
                    st.success("✅ API key updated!")
                    st.rerun()
                else:
                    st.success("✅ Using default key!")
                    st.rerun()

        with col2:
            if st.form_submit_button("🗑️ Reset to Default"):
                st.success("✅ Reset to default API key!")
                st.rerun()

    st.sidebar.markdown("---")

    # Dynamic MCP Server Management (keep this as the only extra feature)
    st.sidebar.header("🔧 Dynamic MCP Servers")

    # MCP Server Tabs
    tab1, tab2, tab3, tab4 = st.sidebar.tabs(["📋 Active", "🌐 Public", "➕ Custom", "🧪 Test"])

    with tab1:
        st.subheader("Active Servers")

        # Display active servers
        try:
            from mcp.dynamic_server_manager import dynamic_mcp_manager
            servers = dynamic_mcp_manager.get_server_status()

            if servers:
                for server_name, status in servers.items():
                    with st.expander(f"🟢 {server_name}"):
                        st.write(f"**Status:** {status.get('status', 'Unknown')}")
                        st.write(f"**Tools:** {status.get('tools_count', 0)}")

                        if st.button(f"🗑️ Remove {server_name}", key=f"remove_{server_name}"):
                            success = asyncio.run(dynamic_mcp_manager.stop_server(server_name))
                            if success:
                                st.success(f"Removed {server_name}")
                                st.rerun()
                            else:
                                st.error(f"Failed to remove {server_name}")
            else:
                st.info("No active MCP servers")

        except Exception as e:
            st.error(f"Error loading servers: {e}")

    with tab2:
        st.subheader("🌐 Public MCP Servers")
        st.write("*Popular pre-built MCP servers*")

        public_servers = {
            "🏨 Airbnb Search": {
                "description": "Search for accommodations and travel info",
                "tool_name": "airbnb_search",
                "category": "Travel"
            },
            "🔍 DuckDuckGo Search": {
                "description": "Web search and information retrieval",
                "tool_name": "web_search",
                "category": "Search"
            },
            "📰 News API": {
                "description": "Latest financial and market news",
                "tool_name": "news_search",
                "category": "News"
            },
            "💱 Currency Exchange": {
                "description": "Real-time currency conversion",
                "tool_name": "currency_convert",
                "category": "Finance"
            }
        }

        for server_name, info in public_servers.items():
            with st.expander(server_name):
                st.write(f"**Description:** {info['description']}")
                st.write(f"**Category:** {info['category']}")

                api_key = st.text_input(
                    "API Key (if required)",
                    type="password",
                    key=f"public_api_{info['tool_name']}"
                )

                if st.button(f"🚀 Add {server_name}", key=f"add_public_{info['tool_name']}"):
                    if api_key:
                        result = asyncio.run(dynamic_mcp_manager.create_server({
                            "name": f"public_{info['tool_name']}",
                            "tool_name": info['tool_name'],
                            "tool_description": info['description'],
                            "llm_api_key": api_key,
                            "type": "public"
                        }))

                        if result.get("success"):
                            st.success(f"Added {server_name}!")
                            st.rerun()
                        else:
                            st.error(f"Failed to add {server_name}")
                    else:
                        st.warning("API key required")

    with tab3:
        st.subheader("➕ Custom MCP Servers")

        # Custom server type selection
        custom_type = st.selectbox(
            "Server Type",
            ["🔧 Custom Tool", "💾 Vector Database", "🗄️ MongoDB", "⚡ Redis"],
            key="custom_server_type"
        )

        if custom_type == "🔧 Custom Tool":
            server_name = st.text_input("Server Name", placeholder="my_custom_server")
            tool_name = st.text_input("Tool Name", placeholder="custom_tool")
            tool_description = st.text_area(
                "Tool Description",
                placeholder="Describe what this tool does...",
                height=100
            )
            llm_api_key = st.text_input(
                "LLM API Key",
                placeholder="gsk_...",
                help="Enter your Groq API key (starts with gsk_)",
                type="password"
            )

        elif custom_type == "💾 Vector Database":
            server_name = st.text_input("Server Name", placeholder="vector_db_server")
            db_type = st.selectbox("Vector DB Type", ["Qdrant", "Pinecone", "Weaviate"])
            connection_string = st.text_input("Connection String", type="password")
            collection_name = st.text_input("Collection Name", placeholder="financial_docs")
            llm_api_key = st.text_input("LLM API Key", type="password")

            tool_name = "vector_search"
            tool_description = f"Search and retrieve information from {db_type} vector database"

        elif custom_type == "🗄️ MongoDB":
            server_name = st.text_input("Server Name", placeholder="mongo_server")
            connection_string = st.text_input("MongoDB URI", type="password")
            database_name = st.text_input("Database Name", placeholder="financial_data")
            collection_name = st.text_input("Collection Name", placeholder="user_data")
            llm_api_key = st.text_input("LLM API Key", type="password")

            tool_name = "mongo_query"
            tool_description = f"Query and manage data in MongoDB database: {database_name}"

        elif custom_type == "⚡ Redis":
            server_name = st.text_input("Server Name", placeholder="redis_server")
            connection_string = st.text_input("Redis URL", placeholder="redis://localhost:6379")
            key_pattern = st.text_input("Key Pattern", placeholder="user:*")
            llm_api_key = st.text_input("LLM API Key", type="password")

            tool_name = "redis_cache"
            tool_description = f"Cache and retrieve data from Redis with pattern: {key_pattern}"

        if st.button("🚀 Create Custom Server"):
            if server_name and tool_name and tool_description and llm_api_key:
                with st.spinner("Creating MCP server..."):
                    try:
                        from mcp.dynamic_server_manager import dynamic_mcp_manager

                        result = asyncio.run(dynamic_mcp_manager.create_server({
                            "name": server_name,
                            "tool_name": tool_name,
                            "tool_description": tool_description,
                            "llm_api_key": llm_api_key
                        }))

                        if result["success"]:
                            st.success(f"✅ Server '{server_name}' created!")
                            st.rerun()
                        else:
                            st.error(f"❌ Failed: {result['error']}")
                    except Exception as e:
                        st.error(f"❌ Error: {e}")
            else:
                st.error("Please fill all fields")

    with tab4:
        st.subheader("🧪 Test MCP Servers")

        try:
            from mcp.dynamic_server_manager import dynamic_mcp_manager
            servers = dynamic_mcp_manager.get_server_status()

            if servers:
                # Server selection for testing
                server_names = list(servers.keys())
                selected_server = st.selectbox("Select Server to Test", server_names, key="test_server_select")

                if selected_server:
                    # Show server info
                    server_status = servers[selected_server]
                    st.write(f"**Status:** {server_status.get('status', 'Unknown')}")
                    st.write(f"**Tool:** {server_status.get('tool_name', 'Unknown')}")
                    st.write(f"**Description:** {server_status.get('tool_description', 'No description')}")

                    # Test query input
                    test_query = st.text_input(
                        "Test Query",
                        placeholder="Enter a test query or leave empty for default",
                        key="test_query_input"
                    )

                    # Test button
                    if st.button("🧪 Run Test", key="run_test_button"):
                        with st.spinner("Testing server..."):
                            try:
                                result = asyncio.run(dynamic_mcp_manager.test_tool(selected_server, test_query))

                                if result.get("success"):
                                    st.success("✅ Test completed successfully!")

                                    # Show test details
                                    st.write("**Test Query:**", result.get("test_query", "N/A"))

                                    # Show result
                                    test_result = result.get("result", {})
                                    if isinstance(test_result, dict):
                                        if test_result.get("success"):
                                            st.write("**Result:**")
                                            st.json(test_result.get("result", "No result data"))
                                        else:
                                            st.error(f"Tool Error: {test_result.get('error', 'Unknown error')}")
                                    else:
                                        st.write("**Result:**", str(test_result))

                                else:
                                    st.error(f"❌ Test failed: {result.get('error', 'Unknown error')}")

                            except Exception as e:
                                st.error(f"❌ Test error: {e}")

                    # Quick test buttons for common queries
                    st.write("**Quick Tests:**")
                    col1, col2 = st.columns(2)

                    with col1:
                        if st.button("💰 Financial Query", key="quick_test_financial"):
                            test_query = "Get financial advice for retirement planning"
                            with st.spinner("Running financial test..."):
                                try:
                                    result = asyncio.run(dynamic_mcp_manager.test_tool(selected_server, test_query))
                                    if result.get("success"):
                                        st.success("✅ Financial test passed!")
                                        st.json(result.get("result", {}))
                                    else:
                                        st.error(f"❌ Test failed: {result.get('error')}")
                                except Exception as e:
                                    st.error(f"❌ Error: {e}")

                    with col2:
                        if st.button("📊 Data Query", key="quick_test_data"):
                            test_query = "Retrieve user financial data"
                            with st.spinner("Running data test..."):
                                try:
                                    result = asyncio.run(dynamic_mcp_manager.test_tool(selected_server, test_query))
                                    if result.get("success"):
                                        st.success("✅ Data test passed!")
                                        st.json(result.get("result", {}))
                                    else:
                                        st.error(f"❌ Test failed: {result.get('error')}")
                                except Exception as e:
                                    st.error(f"❌ Error: {e}")
            else:
                st.info("No servers available for testing. Create some servers first!")

        except Exception as e:
            st.error(f"Error loading test interface: {e}")

    # Show existing MCP servers
    try:
        from mcp.dynamic_server_manager import dynamic_mcp_manager
        servers = dynamic_mcp_manager.get_servers()

        if servers:
            st.sidebar.markdown("**Active Servers:**")
            for server_name, server_info in servers.items():
                status = server_info["status"]
                col1, col2 = st.sidebar.columns([3, 1])
                with col1:
                    st.text(f"🔧 {server_name}")
                with col2:
                    if st.button("🗑️", key=f"delete_{server_name}"):
                        asyncio.run(dynamic_mcp_manager.stop_server(server_name))
                        st.rerun()
        else:
            st.sidebar.info("No MCP servers running")
    except Exception as e:
        st.sidebar.error(f"Error loading servers: {e}")

def render_chat_interface():
    """Render the main chat interface"""
    # Header
    st.markdown('<h1 class="main-header">💰 PennyWise - AI Financial Advisor</h1>', unsafe_allow_html=True)
    st.markdown("*Your Personal Financial Advisor - Always Here to Help! 🌟*")

    # Main chat interface
    st.header("💬 Chat with PennyWise")

    # Display chat messages
    for message in st.session_state.messages:
        with st.chat_message(message["role"]):
            st.markdown(message["content"])

    # Handle example query
    prompt = None
    if hasattr(st.session_state, 'example_query'):
        prompt = st.session_state.example_query
        del st.session_state.example_query
        # Process the example query immediately

    # Always show chat input
    user_input = st.chat_input("Ask me anything about your finances! 💰")

    # Use either example query or user input
    if prompt or user_input:
        if not prompt:
            prompt = user_input
        # Add user message
        st.session_state.messages.append({"role": "user", "content": prompt})
        
        # Display user message
        with st.chat_message("user"):
            st.markdown(prompt)
        
        # Process with multi-agent system
        with st.chat_message("assistant"):
            with st.spinner("🤔 Let me think about that..."):
                try:
                    # Get orchestrator agent
                    agents = config_manager.get_agents()
                    if 'orchestrator' not in agents:
                        st.error("❌ Orchestrator agent not available")
                        return

                    orchestrator = agents['orchestrator']

                    # Process query through multi-agent system
                    start_time = time.time()
                    result = asyncio.run(orchestrator.process_user_query(
                        st.session_state.user_id,
                        prompt
                    ))
                    processing_time = time.time() - start_time

                    # Display response
                    response = result.get("response", "I apologize, but I couldn't process your request.")
                    st.markdown(response)

                    # Show confidence if available
                    if "confidence" in result:
                        confidence = result["confidence"]
                        if confidence < 0.7:
                            st.caption(f"⚠️ Confidence: {confidence:.1%} - Consider asking for clarification")
                        else:
                            st.caption(f"✅ Confidence: {confidence:.1%}")

                    # Show reasoning and planning steps (expandable)
                    if result.get("agent_responses"):
                        with st.expander("🧠 View Reasoning & Planning Steps"):
                            agent_responses = result["agent_responses"]

                            # Show reasoning steps
                            if "reasoning" in agent_responses:
                                st.subheader("🔍 Reasoning Analysis")
                                reasoning_data = agent_responses["reasoning"]
                                if "reasoning_steps" in reasoning_data:
                                    for i, step in enumerate(reasoning_data["reasoning_steps"], 1):
                                        st.write(f"{i}. {step}")

                                if "key_factors" in reasoning_data:
                                    st.write("**Key Factors Considered:**")
                                    for factor in reasoning_data["key_factors"]:
                                        st.write(f"• {factor}")

                            # Show planning steps
                            if "planning" in agent_responses:
                                st.subheader("📋 Planning Recommendations")
                                planning_data = agent_responses["planning"]
                                if "next_steps" in planning_data:
                                    st.write("**Immediate Next Steps:**")
                                    for i, step in enumerate(planning_data["next_steps"], 1):
                                        st.write(f"{i}. {step}")

                                if "recommendations" in planning_data:
                                    st.write("**Strategic Recommendations:**")
                                    for rec in planning_data["recommendations"]:
                                        st.write(f"• {rec}")

                            # Show MCP tools used
                            if result.get("mcp_tools_used"):
                                st.subheader("🔧 Tools Used")
                                for tool in result["mcp_tools_used"]:
                                    st.write(f"• {tool}")

                    # Show processing time
                    st.caption(f"⏱️ Processed in {processing_time:.2f} seconds")

                    # Add feedback system
                    st.markdown("---")
                    col1, col2, col3 = st.columns([1, 1, 2])

                    with col1:
                        if st.button("👍 Helpful", key=f"helpful_{len(st.session_state.messages)}"):
                            asyncio.run(store_feedback(prompt, response, "helpful", ""))
                            st.success("Thank you for your feedback!")

                    with col2:
                        if st.button("👎 Not Helpful", key=f"not_helpful_{len(st.session_state.messages)}"):
                            st.session_state[f"show_feedback_form_{len(st.session_state.messages)}"] = True

                    with col3:
                        if st.session_state.get(f"show_feedback_form_{len(st.session_state.messages)}", False):
                            feedback_text = st.text_input("What could be improved?", key=f"feedback_text_{len(st.session_state.messages)}")
                            if st.button("Submit Feedback", key=f"submit_feedback_{len(st.session_state.messages)}"):
                                asyncio.run(store_feedback(prompt, response, "not_helpful", feedback_text))
                                st.success("Thank you for your feedback!")
                                st.session_state[f"show_feedback_form_{len(st.session_state.messages)}"] = False

                    # Store message
                    st.session_state.messages.append({
                        "role": "assistant",
                        "content": response
                    })

                except Exception as e:
                    logger.error(f"Error processing query: {e}")
                    st.error(f"❌ Error: {str(e)}")
                    error_response = "I apologize, but I encountered an error. Please try asking about your financial goals, budgeting, or investment questions."
                    st.session_state.messages.append({"role": "assistant", "content": error_response})

async def store_feedback(query: str, response: str, feedback_type: str, feedback_text: str):
    """Store user feedback"""
    try:
        agents = st.session_state.get('agents', {})
        if 'memory' in agents:
            feedback_data = {
                "query": query,
                "response": response,
                "feedback_type": feedback_type,
                "feedback_text": feedback_text,
                "timestamp": datetime.now().isoformat(),
                "user_id": st.session_state.user_id
            }

            # Store feedback through memory agent
            await agents['memory'].store_feedback(st.session_state.user_id, feedback_data)
            logger.info(f"Stored feedback: {feedback_type}")

    except Exception as e:
        logger.error(f"Error storing feedback: {e}")

async def initialize_agents():
    """Initialize the multi-agent system"""
    if st.session_state.agents_initialized:
        return True

    try:
        # Initialize all agents
        from agents.memory_agent import MemoryAgent
        from agents.planning_agent import PlanningAgent
        from agents.reasoning_agent import ReasoningAgent
        from agents.financial_agent import FinancialAgent
        from agents.geography_agent import GeographyAgent
        from agents.market_agent import MarketAgent
        from agents.guardrails_agent import GuardrailsAgent
        from agents.feedback_agent import FeedbackAgent
        from agents.response_agent import ResponseAgent

        # Initialize agents
        agents = {
            'memory': MemoryAgent(),
            'planning': PlanningAgent(),
            'reasoning': ReasoningAgent(),
            'financial': FinancialAgent(),
            'geography': GeographyAgent(),
            'market': MarketAgent(),
            'guardrails': GuardrailsAgent(),
            'feedback': FeedbackAgent(),
            'response': ResponseAgent()
        }

        # Initialize each agent
        for name, agent in agents.items():
            await agent.initialize()
            logger.info(f"Agent '{name}' initialized successfully")

        # Create orchestrator and register agents
        orchestrator = OrchestratorAgent()
        await orchestrator.initialize()

        for name, agent in agents.items():
            orchestrator.register_agent(name, agent)

        # Add orchestrator to agents dict
        agents['orchestrator'] = orchestrator

        # Store in config manager
        config_manager.set_agents(agents)

        st.session_state.agents_initialized = True
        return True

    except Exception as e:
        st.error(f"Failed to initialize agents: {e}")
        logger.error(f"Agent initialization error: {e}")
        return False

def main():
    """Main application function"""
    initialize_session_state()
    
    # Render sidebar
    render_sidebar()
    
    # Initialize agents if needed
    if not st.session_state.agents_initialized:
        with st.spinner("🤖 Initializing multi-agent system..."):
            success = asyncio.run(initialize_agents())
            if not success:
                st.error("❌ Failed to initialize agents")
                st.stop()
            else:
                st.success("✅ Multi-agent system initialized!")
                time.sleep(1)
                st.rerun()

    # Render main chat interface
    render_chat_interface()

if __name__ == "__main__":
    main()
