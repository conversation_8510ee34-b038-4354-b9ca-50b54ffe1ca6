"""
PennyWise Multi-Agent Financial Advisor
Streamlit interface matching single agent UI with multi-agent backend
"""

import streamlit as st
import time
import uuid
import asyncio
import logging
from typing import Dict, Any

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Configure page
st.set_page_config(
    page_title="PennyWise - AI Financial Advisor",
    page_icon="💰",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Import configuration and agents
try:
    from config.dynamic_config import config_manager, get_database_config, update_database_config, test_database_connections, get_system_status
    from agents.orchestrator_agent import OrchestratorAgent
    from agents.memory_agent import MemoryAgent
except ImportError as e:
    st.error(f"Failed to import required modules: {e}")
    st.stop()

# Custom CSS
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }

    .user-profile {
        background-color: #f0f8ff;
        padding: 1rem;
        border-radius: 8px;
        margin: 1rem 0;
    }

    .chat-message {
        padding: 1rem;
        margin: 0.5rem 0;
        border-radius: 8px;
        font-weight: 500;
    }

    .streamlit-expanderHeader {
        background-color: #f8f9fa;
        border-radius: 5px;
        font-weight: bold;
    }
</style>
""", unsafe_allow_html=True)

def initialize_session_state():
    """Initialize session state variables"""
    if 'user_id' not in st.session_state:
        st.session_state.user_id = str(uuid.uuid4())
        print(f"DEBUG: Created new user session with user_id: {st.session_state.user_id}")
    else:
        print(f"DEBUG: Existing user session with user_id: {st.session_state.user_id}")

    if 'messages' not in st.session_state:
        st.session_state.messages = []

    if 'agents_initialized' not in st.session_state:
        st.session_state.agents_initialized = False

def get_user_profile(user_id: str) -> Dict[str, Any]:
    """Get user profile from memory agent"""
    try:
        agents = config_manager.get_agents()
        if 'memory' in agents:
            memory_agent = agents['memory']
            # Get user profile directly
            result = asyncio.run(memory_agent.execute_task("get_user_profile", {
                "user_id": user_id
            }))
            return result if isinstance(result, dict) and 'success' not in result else result.get('data', {})
        return {}
    except Exception as e:
        logger.error(f"Error getting user profile: {e}")
        return {}

def save_user_profile(user_id: str, profile_data: Dict[str, Any]) -> bool:
    """Save user profile through memory agent"""
    try:
        agents = config_manager.get_agents()
        if 'memory' in agents:
            memory_agent = agents['memory']
            # Store profile update
            result = asyncio.run(memory_agent.execute_task("update_user_profile", {
                "user_id": user_id,
                "profile_data": profile_data
            }))
            return result.get('success', False)
        return False
    except Exception as e:
        logger.error(f"Error saving user profile: {e}")
        return False

def render_sidebar():
    """Render sidebar with user profile and features"""
    st.sidebar.header("👤 Your Financial Profile")

    user_profile = get_user_profile(st.session_state.user_id)

    # User Profile Form
    with st.sidebar.form("user_profile_form"):
        st.write("**Quick Setup (Optional):**")

        col1, col2 = st.sidebar.columns(2)
        with col1:
            age = st.number_input("Age", min_value=18, max_value=100, value=user_profile.get("age", 30))
            income = st.number_input("Annual Income ($)", min_value=0, value=user_profile.get("income", 50000), step=5000)

        with col2:
            risk_tolerance = st.selectbox(
                "Risk Tolerance",
                ["Conservative", "Moderate", "Aggressive"],
                index=["conservative", "moderate", "aggressive"].index(user_profile.get("risk_tolerance", "moderate"))
            )

            goals = st.multiselect(
                "Financial Goals",
                ["Retirement", "House Purchase", "Education", "Wealth Building", "Emergency Fund"],
                default=user_profile.get("goals", [])
            )

        if st.form_submit_button("💾 Save Profile"):
            profile_data = {
                "age": age,
                "income": income,
                "risk_tolerance": risk_tolerance.lower(),
                "goals": goals
            }

            # Save to memory system
            if save_user_profile(st.session_state.user_id, profile_data):
                st.success("✅ Profile saved! I now know your preferences and will personalize my advice.")
                st.info("💡 Try asking me: 'What's my age?' or 'What are my financial goals?' to test it!")
            else:
                st.error("❌ Failed to save profile")

    # Show current profile if exists
    if user_profile and any(user_profile.values()):
        st.sidebar.markdown('<div class="user-profile">', unsafe_allow_html=True)
        st.sidebar.write("**Current Profile:**")
        if user_profile.get("age"):
            st.sidebar.write(f"🎂 Age: {user_profile['age']}")
        if user_profile.get("income"):
            st.sidebar.write(f"💰 Income: ${user_profile['income']:,}")
        if user_profile.get("risk_tolerance"):
            st.sidebar.write(f"⚖️ Risk: {user_profile['risk_tolerance'].title()}")
        if user_profile.get("goals"):
            st.sidebar.write(f"🎯 Goals: {', '.join(user_profile['goals'])}")
        st.sidebar.markdown('</div>', unsafe_allow_html=True)

    st.sidebar.markdown("---")

    st.sidebar.header("💡 What I Can Help With")
    st.sidebar.write("💰 Retirement Planning")
    st.sidebar.write("🏠 Home Buying")
    st.sidebar.write("📈 Investing")
    st.sidebar.write("💳 Debt Management")
    st.sidebar.write("🎯 Financial Goals")

    st.sidebar.markdown("---")

    # Try These section in sidebar
    st.sidebar.header("💡 Try These")

    example_queries = [
        "Help me plan for retirement",
        "Should I invest in stocks?",
        "How to buy my first house?",
        "Emergency fund advice",
        "Best way to invest $10,000",
        "Investment options for beginners"
    ]

    for i, query in enumerate(example_queries):
        if st.sidebar.button(query, key=f"sidebar_example_{i}"):
            st.session_state.example_query = query
            st.rerun()

    st.sidebar.markdown("---")

    # Dynamic MCP Server Management (keep this as the only extra feature)
    st.sidebar.header("🔧 Dynamic MCP Servers")

    with st.sidebar.expander("➕ Create New MCP Server"):
        server_name = st.text_input("Server Name", placeholder="my_custom_server")
        tool_name = st.text_input("Tool Name", placeholder="custom_tool")
        tool_description = st.text_area(
            "Tool Description",
            placeholder="Describe what this tool does...",
            height=100
        )

        if st.button("🚀 Create MCP Server"):
            if server_name and tool_name and tool_description:
                with st.spinner("Creating MCP server..."):
                    try:
                        from mcp.dynamic_server_manager import dynamic_mcp_manager

                        result = asyncio.run(dynamic_mcp_manager.create_server({
                            "name": server_name,
                            "tool_name": tool_name,
                            "tool_description": tool_description
                        }))

                        if result["success"]:
                            st.success(f"✅ Server '{server_name}' created!")
                            st.rerun()
                        else:
                            st.error(f"❌ Failed: {result['error']}")
                    except Exception as e:
                        st.error(f"❌ Error: {e}")
            else:
                st.error("Please fill all fields")

    # Show existing MCP servers
    try:
        from mcp.dynamic_server_manager import dynamic_mcp_manager
        servers = dynamic_mcp_manager.get_servers()

        if servers:
            st.sidebar.markdown("**Active Servers:**")
            for server_name, server_info in servers.items():
                status = server_info["status"]
                col1, col2 = st.sidebar.columns([3, 1])
                with col1:
                    st.text(f"🔧 {server_name}")
                with col2:
                    if st.button("🗑️", key=f"delete_{server_name}"):
                        asyncio.run(dynamic_mcp_manager.stop_server(server_name))
                        st.rerun()
        else:
            st.sidebar.info("No MCP servers running")
    except Exception as e:
        st.sidebar.error(f"Error loading servers: {e}")

def render_chat_interface():
    """Render the main chat interface"""
    # Header
    st.markdown('<h1 class="main-header">💰 PennyWise - AI Financial Advisor</h1>', unsafe_allow_html=True)
    st.markdown("*Your Personal Financial Advisor - Always Here to Help! 🌟*")

    # Main chat interface
    st.header("💬 Chat with PennyWise")

    # Display chat messages
    for message in st.session_state.messages:
        with st.chat_message(message["role"]):
            st.markdown(message["content"])

    # Handle example query
    if hasattr(st.session_state, 'example_query'):
        prompt = st.session_state.example_query
        del st.session_state.example_query
    else:
        prompt = st.chat_input("Ask me anything about your finances! 💰")

    if prompt:
        # Add user message
        st.session_state.messages.append({"role": "user", "content": prompt})
        
        # Display user message
        with st.chat_message("user"):
            st.markdown(prompt)
        
        # Process with multi-agent system
        with st.chat_message("assistant"):
            with st.spinner("🤔 Let me think about that..."):
                try:
                    # Get orchestrator agent
                    agents = config_manager.get_agents()
                    if 'orchestrator' not in agents:
                        st.error("❌ Orchestrator agent not available")
                        return

                    orchestrator = agents['orchestrator']

                    # Process query through multi-agent system
                    start_time = time.time()
                    result = asyncio.run(orchestrator.process_user_query(
                        st.session_state.user_id,
                        prompt
                    ))
                    processing_time = time.time() - start_time

                    # Display response
                    response = result.get("response", "I apologize, but I couldn't process your request.")
                    st.markdown(response)

                    # Store message
                    st.session_state.messages.append({
                        "role": "assistant",
                        "content": response
                    })

                except Exception as e:
                    logger.error(f"Error processing query: {e}")
                    st.error(f"❌ Error: {str(e)}")
                    error_response = "I apologize, but I encountered an error. Please try asking about your financial goals, budgeting, or investment questions."
                    st.session_state.messages.append({"role": "assistant", "content": error_response})

async def initialize_agents():
    """Initialize the multi-agent system"""
    if st.session_state.agents_initialized:
        return True

    try:
        # Initialize all agents
        from agents.memory_agent import MemoryAgent
        from agents.planning_agent import PlanningAgent
        from agents.reasoning_agent import ReasoningAgent
        from agents.financial_agent import FinancialAgent
        from agents.geography_agent import GeographyAgent
        from agents.market_agent import MarketAgent
        from agents.guardrails_agent import GuardrailsAgent
        from agents.feedback_agent import FeedbackAgent
        from agents.response_agent import ResponseAgent

        # Initialize agents
        agents = {
            'memory': MemoryAgent(),
            'planning': PlanningAgent(),
            'reasoning': ReasoningAgent(),
            'financial': FinancialAgent(),
            'geography': GeographyAgent(),
            'market': MarketAgent(),
            'guardrails': GuardrailsAgent(),
            'feedback': FeedbackAgent(),
            'response': ResponseAgent()
        }

        # Initialize each agent
        for name, agent in agents.items():
            await agent.initialize()
            logger.info(f"Agent '{name}' initialized successfully")

        # Create orchestrator and register agents
        orchestrator = OrchestratorAgent()
        await orchestrator.initialize()

        for name, agent in agents.items():
            orchestrator.register_agent(name, agent)

        # Add orchestrator to agents dict
        agents['orchestrator'] = orchestrator

        # Store in config manager
        config_manager.set_agents(agents)

        st.session_state.agents_initialized = True
        return True

    except Exception as e:
        st.error(f"Failed to initialize agents: {e}")
        logger.error(f"Agent initialization error: {e}")
        return False

def main():
    """Main application function"""
    initialize_session_state()
    
    # Render sidebar
    render_sidebar()
    
    # Initialize agents if needed
    if not st.session_state.agents_initialized:
        with st.spinner("🤖 Initializing multi-agent system..."):
            success = asyncio.run(initialize_agents())
            if not success:
                st.error("❌ Failed to initialize agents")
                st.stop()
            else:
                st.success("✅ Multi-agent system initialized!")
                time.sleep(1)
                st.rerun()

    # Render main chat interface
    render_chat_interface()

if __name__ == "__main__":
    main()
