version: '3.8'

services:
  pennywise-enhanced:
    build: .
    container_name: pennywise_enhanced_ai
    ports:
      - "8501:8501"
    environment:
      - GROQ_API_KEY=${GROQ_API_KEY}
      - ALPHA_VANTAGE_API_KEY=${ALPHA_VANTAGE_API_KEY}
      - REDIS_URL=redis://redis:6379  # Short-term memory (sessions, cache)
      - MONGODB_URL=mongodb://mongodb:27017  # Long-term memory (behavioral, episodic)
      - MONGODB_DATABASE=pennywise_financial_advisor
      - QDRANT_URL=http://qdrant:6333  # Semantic memory (financial knowledge)
      - LANGCHAIN_TRACING_V2=false  # Disable paid LangSmith service
    volumes:
      - ./logs:/app/logs
      - ./data:/app/data
    depends_on:
      - redis
      - mongodb
      - qdrant
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8501/_stcore/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis for short-term memory (sessions, cache)
  redis:
    image: redis:7-alpine
    container_name: pennywise_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    command: redis-server --appendonly yes

  # MongoDB for long-term memory (behavioral, episodic)
  mongodb:
    image: mongo:7
    container_name: pennywise_mongodb
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
    restart: unless-stopped
    environment:
      - MONGO_INITDB_DATABASE=pennywise_financial_advisor

  # Qdrant for semantic memory (financial knowledge)
  qdrant:
    image: qdrant/qdrant:latest
    container_name: pennywise_qdrant
    ports:
      - "6333:6333"
      - "6334:6334"
    volumes:
      - qdrant_data:/qdrant/storage
    restart: unless-stopped

volumes:
  logs:
  data:
  redis_data:      # Redis short-term memory storage
  mongodb_data:    # MongoDB long-term memory storage
  qdrant_data:     # Qdrant semantic memory storage