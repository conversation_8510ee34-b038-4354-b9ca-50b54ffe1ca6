"""
Behavioral Memory System for Multi-Agent Architecture
Tracks user patterns, preferences, and behavioral insights using MongoDB
"""

import asyncio
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict

logger = logging.getLogger(__name__)

@dataclass
class UserPattern:
    """User behavioral pattern"""
    pattern_type: str
    pattern_data: Dict[str, Any]
    frequency: int
    last_seen: datetime
    confidence: float

@dataclass
class UserPreference:
    """User preference"""
    preference_type: str
    value: Any
    strength: float
    learned_from: str
    updated_at: datetime

class BehavioralMemory:
    """Behavioral memory system using MongoDB"""
    
    def __init__(self):
        self.mongo_client = None
        self.database = None
        self.patterns_collection = None
        self.preferences_collection = None
        self.is_initialized = False
        
    async def initialize(self):
        """Initialize MongoDB connection"""
        try:
            from pymongo import MongoClient
            from config.dynamic_config import get_database_config
            
            config = get_database_config()
            
            # Initialize MongoDB client
            self.mongo_client = MongoClient(config.mongodb_url)
            self.database = self.mongo_client.financial_advisor
            self.patterns_collection = self.database.user_patterns
            self.preferences_collection = self.database.user_preferences
            
            # Test connection
            self.mongo_client.admin.command('ping')
            
            # Create indexes
            self.patterns_collection.create_index([("user_id", 1), ("pattern_type", 1)])
            self.preferences_collection.create_index([("user_id", 1), ("preference_type", 1)])
            
            self.is_initialized = True
            logger.info("✅ Behavioral memory initialized")
            
        except Exception as e:
            logger.error(f"Failed to initialize behavioral memory: {e}")
            raise
    
    async def get_user_patterns(self, user_id: str) -> Dict[str, Any]:
        """Get user behavioral patterns"""
        try:
            if not self.is_initialized:
                return {}
            
            patterns = list(self.patterns_collection.find({"user_id": user_id}))
            preferences = list(self.preferences_collection.find({"user_id": user_id}))
            
            # Process patterns
            pattern_summary = {}
            for pattern in patterns:
                pattern_type = pattern.get("pattern_type", "unknown")
                pattern_summary[pattern_type] = {
                    "frequency": pattern.get("frequency", 0),
                    "confidence": pattern.get("confidence", 0.5),
                    "last_seen": pattern.get("last_seen"),
                    "data": pattern.get("pattern_data", {})
                }
            
            # Process preferences
            preference_summary = {}
            for pref in preferences:
                pref_type = pref.get("preference_type", "unknown")
                preference_summary[pref_type] = {
                    "value": pref.get("value"),
                    "strength": pref.get("strength", 0.5),
                    "learned_from": pref.get("learned_from", "interaction"),
                    "updated_at": pref.get("updated_at")
                }
            
            return {
                "patterns": pattern_summary,
                "preferences": preference_summary,
                "user_id": user_id
            }
            
        except Exception as e:
            logger.error(f"Failed to get user patterns: {e}")
            return {}
    
    async def update_patterns(self, user_id: str, query: str, response: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Update user patterns based on interaction"""
        try:
            if not self.is_initialized:
                return {"success": False}
            
            # Analyze query patterns
            await self._analyze_query_patterns(user_id, query)
            
            # Analyze response preferences
            await self._analyze_response_preferences(user_id, response, context)
            
            # Analyze timing patterns
            await self._analyze_timing_patterns(user_id)
            
            # Analyze topic interests
            await self._analyze_topic_interests(user_id, query)
            
            return {"success": True, "patterns_updated": True}
            
        except Exception as e:
            logger.error(f"Failed to update patterns: {e}")
            return {"success": False, "error": str(e)}
    
    async def _analyze_query_patterns(self, user_id: str, query: str):
        """Analyze query patterns"""
        try:
            query_lower = query.lower()
            
            # Detect question types
            if any(word in query_lower for word in ['invest', 'investment', 'portfolio']):
                await self._update_pattern(user_id, "investment_interest", {"query": query})
            
            if any(word in query_lower for word in ['retire', 'retirement', '401k', 'ira']):
                await self._update_pattern(user_id, "retirement_planning", {"query": query})
            
            if any(word in query_lower for word in ['budget', 'save', 'saving', 'expense']):
                await self._update_pattern(user_id, "budgeting_interest", {"query": query})
            
            if any(word in query_lower for word in ['risk', 'safe', 'conservative', 'aggressive']):
                await self._update_pattern(user_id, "risk_discussion", {"query": query})
            
            # Analyze query complexity
            word_count = len(query.split())
            if word_count > 20:
                await self._update_pattern(user_id, "complex_queries", {"word_count": word_count})
            elif word_count < 5:
                await self._update_pattern(user_id, "simple_queries", {"word_count": word_count})
                
        except Exception as e:
            logger.error(f"Failed to analyze query patterns: {e}")
    
    async def _analyze_response_preferences(self, user_id: str, response: str, context: Dict[str, Any]):
        """Analyze response preferences"""
        try:
            response_length = len(response.split())
            confidence = context.get("confidence", 0.5)
            
            # Track preferred response length
            await self._update_preference(
                user_id, 
                "response_length", 
                response_length, 
                confidence,
                "response_analysis"
            )
            
            # Track confidence preferences (if user continues engaging with high/low confidence responses)
            await self._update_preference(
                user_id,
                "confidence_tolerance",
                confidence,
                0.7,
                "interaction_analysis"
            )
            
        except Exception as e:
            logger.error(f"Failed to analyze response preferences: {e}")
    
    async def _analyze_timing_patterns(self, user_id: str):
        """Analyze timing patterns"""
        try:
            current_time = datetime.now()
            hour = current_time.hour
            day_of_week = current_time.weekday()
            
            # Track active hours
            await self._update_pattern(user_id, "active_hours", {"hour": hour})
            
            # Track active days
            await self._update_pattern(user_id, "active_days", {"day": day_of_week})
            
        except Exception as e:
            logger.error(f"Failed to analyze timing patterns: {e}")
    
    async def _analyze_topic_interests(self, user_id: str, query: str):
        """Analyze topic interests"""
        try:
            query_lower = query.lower()
            
            # Financial topics
            topics = {
                "stocks": ["stock", "equity", "share", "dividend"],
                "bonds": ["bond", "treasury", "fixed income"],
                "crypto": ["crypto", "bitcoin", "ethereum", "blockchain"],
                "real_estate": ["real estate", "property", "mortgage", "rent"],
                "insurance": ["insurance", "coverage", "premium", "policy"],
                "taxes": ["tax", "deduction", "irs", "refund"],
                "debt": ["debt", "loan", "credit", "payment"]
            }
            
            for topic, keywords in topics.items():
                if any(keyword in query_lower for keyword in keywords):
                    await self._update_pattern(user_id, f"topic_interest_{topic}", {"query": query})
                    
        except Exception as e:
            logger.error(f"Failed to analyze topic interests: {e}")
    
    async def _update_pattern(self, user_id: str, pattern_type: str, pattern_data: Dict[str, Any]):
        """Update or create a behavioral pattern"""
        try:
            existing_pattern = self.patterns_collection.find_one({
                "user_id": user_id,
                "pattern_type": pattern_type
            })
            
            if existing_pattern:
                # Update existing pattern
                new_frequency = existing_pattern.get("frequency", 0) + 1
                new_confidence = min(1.0, existing_pattern.get("confidence", 0.5) + 0.1)
                
                self.patterns_collection.update_one(
                    {"user_id": user_id, "pattern_type": pattern_type},
                    {
                        "$set": {
                            "frequency": new_frequency,
                            "confidence": new_confidence,
                            "last_seen": datetime.now(),
                            "pattern_data": pattern_data
                        }
                    }
                )
            else:
                # Create new pattern
                pattern = {
                    "user_id": user_id,
                    "pattern_type": pattern_type,
                    "pattern_data": pattern_data,
                    "frequency": 1,
                    "confidence": 0.3,
                    "last_seen": datetime.now(),
                    "created_at": datetime.now()
                }
                self.patterns_collection.insert_one(pattern)
                
        except Exception as e:
            logger.error(f"Failed to update pattern: {e}")
    
    async def _update_preference(self, user_id: str, preference_type: str, value: Any, strength: float, learned_from: str):
        """Update or create a user preference"""
        try:
            existing_pref = self.preferences_collection.find_one({
                "user_id": user_id,
                "preference_type": preference_type
            })
            
            if existing_pref:
                # Update existing preference (weighted average)
                old_strength = existing_pref.get("strength", 0.5)
                old_value = existing_pref.get("value", value)
                
                # Calculate weighted average for numeric values
                if isinstance(value, (int, float)) and isinstance(old_value, (int, float)):
                    new_value = (old_value * old_strength + value * strength) / (old_strength + strength)
                    new_strength = min(1.0, (old_strength + strength) / 2)
                else:
                    new_value = value
                    new_strength = strength
                
                self.preferences_collection.update_one(
                    {"user_id": user_id, "preference_type": preference_type},
                    {
                        "$set": {
                            "value": new_value,
                            "strength": new_strength,
                            "learned_from": learned_from,
                            "updated_at": datetime.now()
                        }
                    }
                )
            else:
                # Create new preference
                preference = {
                    "user_id": user_id,
                    "preference_type": preference_type,
                    "value": value,
                    "strength": strength,
                    "learned_from": learned_from,
                    "updated_at": datetime.now(),
                    "created_at": datetime.now()
                }
                self.preferences_collection.insert_one(preference)
                
        except Exception as e:
            logger.error(f"Failed to update preference: {e}")
    
    async def store_feedback(self, user_id: str, query: str, response: str, feedback_data: Dict[str, Any]) -> Dict[str, Any]:
        """Store and analyze feedback"""
        try:
            rating = feedback_data.get('rating', 3)
            comment = feedback_data.get('comment', '')
            
            # Update response quality preference
            await self._update_preference(
                user_id,
                "response_quality_expectation",
                rating,
                0.8,
                "direct_feedback"
            )
            
            # Analyze feedback patterns
            if rating >= 4:
                await self._update_pattern(user_id, "positive_feedback", {"rating": rating, "comment": comment})
            elif rating <= 2:
                await self._update_pattern(user_id, "negative_feedback", {"rating": rating, "comment": comment})
            
            return {"success": True, "feedback_processed": True}
            
        except Exception as e:
            logger.error(f"Failed to store feedback: {e}")
            return {"success": False, "error": str(e)}
    
    async def get_user_insights(self, user_id: str) -> Dict[str, Any]:
        """Get behavioral insights for a user"""
        try:
            patterns = await self.get_user_patterns(user_id)
            
            insights = {
                "primary_interests": [],
                "engagement_level": "medium",
                "preferred_topics": [],
                "interaction_style": "standard",
                "risk_tolerance_indicators": []
            }
            
            # Analyze patterns for insights
            user_patterns = patterns.get("patterns", {})
            
            # Determine primary interests
            interest_patterns = {k: v for k, v in user_patterns.items() if "interest" in k}
            if interest_patterns:
                sorted_interests = sorted(interest_patterns.items(), key=lambda x: x[1]["frequency"], reverse=True)
                insights["primary_interests"] = [interest[0].replace("_interest", "") for interest in sorted_interests[:3]]
            
            # Determine engagement level
            total_interactions = sum(pattern["frequency"] for pattern in user_patterns.values())
            if total_interactions > 10:
                insights["engagement_level"] = "high"
            elif total_interactions < 3:
                insights["engagement_level"] = "low"
            
            # Determine preferred topics
            topic_patterns = {k: v for k, v in user_patterns.items() if "topic_interest" in k}
            if topic_patterns:
                sorted_topics = sorted(topic_patterns.items(), key=lambda x: x[1]["frequency"], reverse=True)
                insights["preferred_topics"] = [topic[0].replace("topic_interest_", "") for topic in sorted_topics[:3]]
            
            return insights
            
        except Exception as e:
            logger.error(f"Failed to get user insights: {e}")
            return {}
    
    def update_config(self, config):
        """Update configuration"""
        try:
            if hasattr(config, 'mongodb_url'):
                # Reinitialize with new config
                asyncio.create_task(self.initialize())
            logger.info("Updated behavioral memory configuration")
        except Exception as e:
            logger.error(f"Failed to update config: {e}")
