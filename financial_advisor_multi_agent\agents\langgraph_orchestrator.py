"""
LangGraph Orchestrator - Advanced workflow management using LangGraph
Coordinates agents using proper graph-based workflows
"""

import asyncio
import logging
from typing import Dict, Any, List, Optional, TypedDict, Annotated
from datetime import datetime

try:
    from langgraph.graph import StateGraph, END
    from langgraph.prebuilt import ToolExecutor
    from langchain_core.messages import BaseMessage, HumanMessage, AIMessage
    LANGGRAPH_AVAILABLE = True
except ImportError:
    LANGGRAPH_AVAILABLE = False
    print("⚠️ LangGraph not available. Install with: pip install langgraph")

logger = logging.getLogger(__name__)

class AgentState(TypedDict):
    """State shared across all agents in the graph"""
    user_id: str
    query: str
    user_profile: Dict[str, Any]
    conversation_history: List[BaseMessage]
    agent_responses: Dict[str, Any]
    mcp_tools_used: List[str]
    final_response: str
    confidence: float
    next_agent: Optional[str]

class LangGraphOrchestrator:
    """Advanced orchestrator using LangGraph for workflow management"""
    
    def __init__(self):
        self.graph = None
        self.agents = {}
        self.mcp_manager = None
        self.is_initialized = False
        
    async def initialize(self):
        """Initialize LangGraph workflow"""
        try:
            if not LANGGRAPH_AVAILABLE:
                logger.warning("LangGraph not available, falling back to basic orchestrator")
                return False
                
            logger.info("Initializing LangGraph Orchestrator")
            
            # Import MCP manager
            from mcp.dynamic_server_manager import dynamic_mcp_manager
            self.mcp_manager = dynamic_mcp_manager
            
            # Build the workflow graph
            await self._build_workflow_graph()
            
            self.is_initialized = True
            logger.info("✅ LangGraph Orchestrator initialized")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize LangGraph Orchestrator: {e}")
            return False
    
    async def _build_workflow_graph(self):
        """Build the LangGraph workflow"""
        workflow = StateGraph(AgentState)
        
        # Add nodes (agents)
        workflow.add_node("mcp_checker", self._check_mcp_tools)
        workflow.add_node("financial_agent", self._execute_financial_agent)
        workflow.add_node("memory_agent", self._execute_memory_agent)
        workflow.add_node("planning_agent", self._execute_planning_agent)
        workflow.add_node("response_synthesizer", self._synthesize_response)
        
        # Define the workflow edges
        workflow.set_entry_point("mcp_checker")
        
        # MCP checker decides next step
        workflow.add_conditional_edges(
            "mcp_checker",
            self._route_after_mcp,
            {
                "financial": "financial_agent",
                "memory": "memory_agent", 
                "planning": "planning_agent",
                "synthesize": "response_synthesizer"
            }
        )
        
        # All agents flow to response synthesizer
        workflow.add_edge("financial_agent", "response_synthesizer")
        workflow.add_edge("memory_agent", "response_synthesizer")
        workflow.add_edge("planning_agent", "response_synthesizer")
        workflow.add_edge("response_synthesizer", END)
        
        # Compile the graph
        self.graph = workflow.compile()
        
    async def _check_mcp_tools(self, state: AgentState) -> AgentState:
        """Check and execute MCP tools if relevant"""
        try:
            query = state["query"]
            
            # Get available MCP tools
            available_tools = self.mcp_manager.get_available_tools()
            
            for tool in available_tools:
                # Simple keyword matching
                if any(word in tool["description"].lower() for word in query.lower().split()):
                    # Execute the tool
                    result = await self.mcp_manager.execute_server_tool(
                        tool["server_name"], query, {"user_profile": state["user_profile"]}
                    )
                    
                    if result.get("success"):
                        state["agent_responses"]["mcp_tools"] = result
                        state["mcp_tools_used"].append(tool["name"])
                        break
            
            return state
            
        except Exception as e:
            logger.error(f"Error in MCP checker: {e}")
            return state
    
    def _route_after_mcp(self, state: AgentState) -> str:
        """Decide which agent to use next based on query"""
        query_lower = state["query"].lower()
        
        # If MCP tool handled it well, go straight to synthesis
        if state["agent_responses"].get("mcp_tools", {}).get("success"):
            return "synthesize"
        
        # Route based on query content
        if any(word in query_lower for word in ['invest', 'stock', 'portfolio', 'retirement']):
            return "financial"
        elif any(word in query_lower for word in ['remember', 'profile', 'save']):
            return "memory"
        elif any(word in query_lower for word in ['plan', 'strategy', 'goal']):
            return "planning"
        else:
            return "financial"  # Default to financial
    
    async def _execute_financial_agent(self, state: AgentState) -> AgentState:
        """Execute financial agent"""
        try:
            if "financial" in self.agents:
                result = await self.agents["financial"].process_query(
                    state["user_id"], 
                    state["query"], 
                    {"user_profile": state["user_profile"]}
                )
                if result.get("success"):
                    state["agent_responses"]["financial"] = result
            
            return state
            
        except Exception as e:
            logger.error(f"Error in financial agent: {e}")
            return state
    
    async def _execute_memory_agent(self, state: AgentState) -> AgentState:
        """Execute memory agent"""
        try:
            if "memory" in self.agents:
                result = await self.agents["memory"].process_query(
                    state["user_id"],
                    state["query"],
                    {"user_profile": state["user_profile"]}
                )
                if result.get("success"):
                    state["agent_responses"]["memory"] = result
            
            return state
            
        except Exception as e:
            logger.error(f"Error in memory agent: {e}")
            return state
    
    async def _execute_planning_agent(self, state: AgentState) -> AgentState:
        """Execute planning agent"""
        try:
            if "planning" in self.agents:
                result = await self.agents["planning"].process_query(
                    state["user_id"],
                    state["query"], 
                    {"user_profile": state["user_profile"]}
                )
                if result.get("success"):
                    state["agent_responses"]["planning"] = result
            
            return state
            
        except Exception as e:
            logger.error(f"Error in planning agent: {e}")
            return state
    
    async def _synthesize_response(self, state: AgentState) -> AgentState:
        """Synthesize final response from all agent outputs"""
        try:
            if "response" in self.agents:
                result = await self.agents["response"].generate_response(
                    state["user_id"],
                    state["query"],
                    {"user_profile": state["user_profile"]},
                    state["agent_responses"]
                )
                
                if result.get("success"):
                    state["final_response"] = result.get("response", "")
                    state["confidence"] = result.get("confidence", 0.7)
                else:
                    # Fallback synthesis
                    state["final_response"] = self._fallback_synthesis(state)
                    state["confidence"] = 0.6
            else:
                state["final_response"] = self._fallback_synthesis(state)
                state["confidence"] = 0.5
            
            return state
            
        except Exception as e:
            logger.error(f"Error in response synthesis: {e}")
            state["final_response"] = "I apologize, but I encountered an error. Please try again."
            state["confidence"] = 0.1
            return state
    
    def _fallback_synthesis(self, state: AgentState) -> str:
        """Fallback response synthesis"""
        agent_responses = state["agent_responses"]
        
        # Use MCP tool response if available
        if "mcp_tools" in agent_responses:
            mcp_result = agent_responses["mcp_tools"]
            return f"Using {state['mcp_tools_used'][0]}: {mcp_result.get('response', '')}"
        
        # Use primary agent response
        for agent_type in ["financial", "memory", "planning"]:
            if agent_type in agent_responses:
                return agent_responses[agent_type].get("analysis", "I can help with your financial questions.")
        
        return "I'm here to help with your financial planning needs. What would you like to know?"
    
    def register_agent(self, name: str, agent: Any):
        """Register an agent with the orchestrator"""
        self.agents[name] = agent
        logger.info(f"Registered agent: {name}")
    
    async def process_query(self, user_id: str, query: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Process query using LangGraph workflow"""
        try:
            if not self.is_initialized or not self.graph:
                return {"error": "LangGraph orchestrator not initialized"}
            
            # Initialize state
            initial_state = AgentState(
                user_id=user_id,
                query=query,
                user_profile=context.get("user_profile", {}),
                conversation_history=context.get("conversation_history", []),
                agent_responses={},
                mcp_tools_used=[],
                final_response="",
                confidence=0.0,
                next_agent=None
            )
            
            # Execute the workflow
            final_state = await self.graph.ainvoke(initial_state)
            
            return {
                "response": final_state["final_response"],
                "confidence": final_state["confidence"],
                "agent_responses": final_state["agent_responses"],
                "mcp_tools_used": final_state["mcp_tools_used"]
            }
            
        except Exception as e:
            logger.error(f"Error in LangGraph workflow: {e}")
            return {
                "response": "I encountered an error processing your request. Please try again.",
                "confidence": 0.1,
                "error": str(e)
            }
