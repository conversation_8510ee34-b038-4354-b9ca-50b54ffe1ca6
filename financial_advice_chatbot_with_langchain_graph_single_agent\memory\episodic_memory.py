"""
Episodic Memory System using MongoDB
Handles conversation episodes, user experiences, and contextual memories
"""

from typing import Dict, List, Any, Optional
import os
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum

import pymongo
from pymongo import MongoClient
from mongoengine import Document, EmbeddedDocument, fields, connect
from dotenv import load_dotenv

load_dotenv()

class ConversationTurn(EmbeddedDocument):
    """Individual turn in a conversation"""
    speaker = fields.StringField(choices=["user", "assistant"])
    message = fields.StringField(required=True)
    timestamp = fields.DateTimeField(default=datetime.utcnow)
    intent = fields.StringField()  # detected intent
    entities = fields.DictField()  # extracted entities
    sentiment = fields.StringField()  # positive, negative, neutral
    confidence = fields.FloatField()  # assistant confidence score

class ConversationEpisode(Document):
    """Complete conversation episode"""
    user_id = fields.StringField(required=True)
    session_id = fields.StringField(required=True)
    
    # Episode metadata
    start_time = fields.DateTimeField(default=datetime.utcnow)
    end_time = fields.DateTimeField()
    duration_minutes = fields.IntField()
    
    # Conversation content
    turns = fields.ListField(fields.EmbeddedDocumentField(ConversationTurn))
    summary = fields.StringField()  # AI-generated summary
    key_topics = fields.ListField(fields.StringField())
    
    # Context and outcomes
    user_goals = fields.ListField(fields.StringField())
    goals_achieved = fields.ListField(fields.StringField())
    action_items = fields.ListField(fields.StringField())
    follow_up_needed = fields.BooleanField(default=False)
    
    # Emotional and engagement metrics
    user_satisfaction = fields.FloatField()  # 0.0 to 1.0
    engagement_level = fields.StringField()  # high, medium, low
    emotional_journey = fields.ListField(fields.StringField())
    
    # Learning and insights
    new_information_learned = fields.ListField(fields.StringField())
    user_preferences_discovered = fields.DictField()
    behavioral_observations = fields.ListField(fields.StringField())
    
    meta = {
        'collection': 'conversation_episodes',
        'indexes': ['user_id', 'session_id', 'start_time']
    }

class FinancialExperience(Document):
    """Significant financial experiences and events"""
    user_id = fields.StringField(required=True)
    
    # Experience details
    experience_type = fields.StringField()  # "investment", "loss", "gain", "decision"
    title = fields.StringField(required=True)
    description = fields.StringField()
    date_occurred = fields.DateTimeField()
    
    # Financial impact
    financial_impact = fields.FloatField()  # monetary impact
    emotional_impact = fields.StringField()  # positive, negative, neutral
    lessons_learned = fields.ListField(fields.StringField())
    
    # Context
    market_conditions = fields.DictField()
    personal_circumstances = fields.DictField()
    decision_factors = fields.ListField(fields.StringField())
    
    # Outcomes and reflection
    outcome_satisfaction = fields.FloatField()  # 0.0 to 1.0
    would_repeat = fields.BooleanField()
    advice_for_future = fields.StringField()
    
    # Memory strength and importance
    memory_strength = fields.FloatField(default=1.0)  # how well remembered
    importance_score = fields.FloatField(default=0.5)  # personal importance
    
    created_at = fields.DateTimeField(default=datetime.utcnow)
    
    meta = {
        'collection': 'financial_experiences',
        'indexes': ['user_id', 'date_occurred', 'importance_score']
    }

class ContextualMemory(Document):
    """Contextual memories tied to specific situations"""
    user_id = fields.StringField(required=True)
    
    # Context identifiers
    context_type = fields.StringField()  # "market_event", "life_event", "decision_point"
    context_description = fields.StringField()
    trigger_conditions = fields.DictField()
    
    # Memory content
    user_state = fields.DictField()  # user's state during this context
    decisions_made = fields.ListField(fields.StringField())
    reasoning_process = fields.ListField(fields.StringField())
    external_factors = fields.DictField()
    
    # Temporal information
    occurred_at = fields.DateTimeField()
    duration = fields.IntField()  # minutes
    frequency = fields.IntField(default=1)  # how often this context occurs
    
    # Relevance and retrieval
    relevance_score = fields.FloatField(default=0.5)
    last_accessed = fields.DateTimeField()
    access_count = fields.IntField(default=0)
    
    meta = {
        'collection': 'contextual_memories',
        'indexes': ['user_id', 'context_type', 'relevance_score']
    }

class EpisodicMemory:
    """Episodic memory system using MongoDB"""
    
    def __init__(self, 
                 mongodb_url: str = None,
                 database_name: str = None):
        
        self.mongodb_url = mongodb_url or os.getenv("MONGODB_URL", "mongodb://localhost:27017")
        self.database_name = database_name or os.getenv("MONGODB_DATABASE", "pennywise_financial_advisor")
        
        # Connect to MongoDB
        try:
            connect(
                db=self.database_name,
                host=self.mongodb_url,
                alias='episodic_memory'
            )
            print(f"✅ Connected to MongoDB for episodic memory: {self.database_name}")
        except Exception as e:
            print(f"⚠️ MongoDB connection warning: {e}")
    
    def start_conversation_episode(self, user_id: str, session_id: str) -> ConversationEpisode:
        """Start a new conversation episode"""
        try:
            episode = ConversationEpisode(
                user_id=user_id,
                session_id=session_id,
                turns=[],
                key_topics=[],
                user_goals=[],
                goals_achieved=[],
                action_items=[]
            )
            episode.save()
            
            print(f"✅ Started conversation episode: {session_id}")
            return episode
            
        except Exception as e:
            print(f"⚠️ Error starting conversation episode: {e}")
            return None
    
    def add_conversation_turn(self, session_id: str, speaker: str, message: str, 
                            intent: str = None, entities: Dict = None, 
                            sentiment: str = None, confidence: float = None):
        """Add a turn to the current conversation episode"""
        try:
            episode = ConversationEpisode.objects(session_id=session_id).first()
            if not episode:
                print(f"⚠️ No episode found for session: {session_id}")
                return
            
            turn = ConversationTurn(
                speaker=speaker,
                message=message,
                intent=intent,
                entities=entities or {},
                sentiment=sentiment,
                confidence=confidence
            )
            
            episode.turns.append(turn)
            episode.save()
            
        except Exception as e:
            print(f"⚠️ Error adding conversation turn: {e}")
    
    def end_conversation_episode(self, session_id: str, summary: str = None, 
                               user_satisfaction: float = None):
        """End and finalize a conversation episode"""
        try:
            episode = ConversationEpisode.objects(session_id=session_id).first()
            if not episode:
                return
            
            episode.end_time = datetime.utcnow()
            episode.duration_minutes = int(
                (episode.end_time - episode.start_time).total_seconds() / 60
            )
            
            if summary:
                episode.summary = summary
            
            if user_satisfaction:
                episode.user_satisfaction = user_satisfaction
            
            # Extract key topics from conversation
            episode.key_topics = self._extract_key_topics(episode.turns)
            
            episode.save()
            
            print(f"✅ Ended conversation episode: {session_id}")
            
        except Exception as e:
            print(f"⚠️ Error ending conversation episode: {e}")
    
    def add_financial_experience(self, user_id: str, experience_type: str, 
                               title: str, description: str, 
                               financial_impact: float = None,
                               emotional_impact: str = None,
                               lessons_learned: List[str] = None) -> FinancialExperience:
        """Add a significant financial experience"""
        try:
            experience = FinancialExperience(
                user_id=user_id,
                experience_type=experience_type,
                title=title,
                description=description,
                date_occurred=datetime.utcnow(),
                financial_impact=financial_impact or 0.0,
                emotional_impact=emotional_impact or "neutral",
                lessons_learned=lessons_learned or []
            )
            experience.save()
            
            print(f"✅ Added financial experience: {title}")
            return experience
            
        except Exception as e:
            print(f"⚠️ Error adding financial experience: {e}")
            return None
    
    def add_contextual_memory(self, user_id: str, context_type: str, 
                            context_description: str, user_state: Dict,
                            decisions_made: List[str] = None) -> ContextualMemory:
        """Add a contextual memory"""
        try:
            memory = ContextualMemory(
                user_id=user_id,
                context_type=context_type,
                context_description=context_description,
                user_state=user_state,
                decisions_made=decisions_made or [],
                occurred_at=datetime.utcnow()
            )
            memory.save()
            
            print(f"✅ Added contextual memory: {context_type}")
            return memory
            
        except Exception as e:
            print(f"⚠️ Error adding contextual memory: {e}")
            return None
    
    def get_recent_conversations(self, user_id: str, days: int = 7) -> List[ConversationEpisode]:
        """Get recent conversation episodes for user"""
        try:
            since_date = datetime.utcnow() - timedelta(days=days)
            episodes = ConversationEpisode.objects(
                user_id=user_id,
                start_time__gte=since_date
            ).order_by('-start_time')
            
            return list(episodes)
            
        except Exception as e:
            print(f"⚠️ Error getting recent conversations: {e}")
            return []
    
    def get_relevant_experiences(self, user_id: str, context: str, limit: int = 5) -> List[FinancialExperience]:
        """Get relevant financial experiences based on context"""
        try:
            # Simple text matching for now - could be enhanced with semantic search
            experiences = FinancialExperience.objects(
                user_id=user_id
            ).order_by('-importance_score', '-date_occurred')[:limit]
            
            return list(experiences)
            
        except Exception as e:
            print(f"⚠️ Error getting relevant experiences: {e}")
            return []
    
    def get_contextual_memories(self, user_id: str, context_type: str = None) -> List[ContextualMemory]:
        """Get contextual memories for user"""
        try:
            query = {"user_id": user_id}
            if context_type:
                query["context_type"] = context_type
            
            memories = ContextualMemory.objects(**query).order_by('-relevance_score')
            return list(memories)
            
        except Exception as e:
            print(f"⚠️ Error getting contextual memories: {e}")
            return []
    
    def _extract_key_topics(self, turns: List[ConversationTurn]) -> List[str]:
        """Extract key topics from conversation turns"""
        # Simple keyword extraction - could be enhanced with NLP
        topics = set()
        financial_keywords = [
            "investment", "retirement", "portfolio", "stocks", "bonds",
            "savings", "budget", "debt", "insurance", "tax", "planning"
        ]
        
        for turn in turns:
            message_lower = turn.message.lower()
            for keyword in financial_keywords:
                if keyword in message_lower:
                    topics.add(keyword)
        
        return list(topics)
    
    def get_episode_summary(self, user_id: str, days: int = 30) -> Dict[str, Any]:
        """Get summary of user's episodic memories"""
        try:
            since_date = datetime.utcnow() - timedelta(days=days)
            
            # Conversation statistics
            conversations = ConversationEpisode.objects(
                user_id=user_id,
                start_time__gte=since_date
            )
            
            # Experience statistics
            experiences = FinancialExperience.objects(
                user_id=user_id,
                date_occurred__gte=since_date
            )
            
            summary = {
                "conversations_count": conversations.count(),
                "total_conversation_time": sum(c.duration_minutes or 0 for c in conversations),
                "experiences_count": experiences.count(),
                "avg_satisfaction": sum(c.user_satisfaction or 0 for c in conversations) / max(conversations.count(), 1),
                "key_topics": self._get_trending_topics(conversations),
                "recent_experiences": [exp.title for exp in experiences.order_by('-date_occurred')[:3]]
            }
            
            return summary
            
        except Exception as e:
            return {"error": str(e)}
    
    def _get_trending_topics(self, conversations) -> List[str]:
        """Get trending topics from recent conversations"""
        topic_counts = {}
        for conv in conversations:
            for topic in conv.key_topics:
                topic_counts[topic] = topic_counts.get(topic, 0) + 1
        
        # Sort by frequency and return top 5
        sorted_topics = sorted(topic_counts.items(), key=lambda x: x[1], reverse=True)
        return [topic for topic, count in sorted_topics[:5]]

# Global episodic memory instance
episodic_memory = EpisodicMemory()
