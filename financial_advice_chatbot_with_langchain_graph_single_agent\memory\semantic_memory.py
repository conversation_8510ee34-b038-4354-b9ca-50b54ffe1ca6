"""
Semantic Memory System using Qdrant Vector Database
Handles financial knowledge, concepts, and semantic relationships
"""

from typing import List, Dict, Any, Optional
import os
import uuid
from datetime import datetime

# Qdrant and LangChain imports
from qdrant_client import QdrantClient
from qdrant_client.models import Distance, VectorParams, PointStruct
from langchain_qdrant import QdrantVectorStore
from langchain_huggingface import HuggingFaceEmbeddings
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain.schema import Document
from langchain.retrievers import ContextualCompressionRetriever
from langchain.retrievers.document_compressors import LLMChainExtractor
from langchain_groq import ChatGroq
from dotenv import load_dotenv

load_dotenv()

class SemanticMemory:
    """Semantic memory system using Qdrant for financial knowledge and concepts"""
    
    def __init__(self,
                 qdrant_url: str = None,
                 collection_name: str = "financial_knowledge"):

        # Get Qdrant URL from environment or use default
        self.qdrant_url = qdrant_url or os.getenv("QDRANT_URL", "http://localhost:6333")
        
        # Initialize embeddings
        self.embeddings = HuggingFaceEmbeddings(
            model_name="sentence-transformers/all-MiniLM-L6-v2",
            model_kwargs={'device': 'cpu'}
        )
        
        # Initialize LLM for compression
        self.llm = ChatGroq(
            model="llama3-8b-8192",
            api_key=os.getenv("GROQ_API_KEY"),
            temperature=0.1
        )
        
        # Initialize text splitter
        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=1000,
            chunk_overlap=200,
            length_function=len
        )
        
        # Initialize Qdrant client with fallback
        self.fallback_mode = False
        try:
            self.qdrant_client = QdrantClient(url=self.qdrant_url)
            self.collection_name = collection_name

            # Test connection
            self.qdrant_client.get_collections()

            # Initialize collection if it doesn't exist
            self._initialize_collection()

            # Initialize Qdrant vector store
            self.vector_store = QdrantVectorStore(
                client=self.qdrant_client,
                collection_name=collection_name,
                embedding=self.embeddings
            )

            # Initialize retriever with compression
            base_retriever = self.vector_store.as_retriever(
                search_type="similarity",
                search_kwargs={"k": 5}
            )

            compressor = LLMChainExtractor.from_llm(self.llm)
            self.retriever = ContextualCompressionRetriever(
                base_compressor=compressor,
                base_retriever=base_retriever
            )

            print(f"✅ Semantic memory initialized with Qdrant at {self.qdrant_url}")

        except Exception as e:
            print(f"⚠️ Qdrant not available: {e}")
            print("💡 Semantic memory running in fallback mode (no vector search)")
            self.fallback_mode = True
            self.qdrant_client = None
            self.vector_store = None
            self.retriever = None
        
        # Initialize with financial knowledge
        self._initialize_financial_knowledge()
    
    def _initialize_collection(self):
        """Initialize Qdrant collection with proper configuration"""
        try:
            # Check if collection exists
            collections = self.qdrant_client.get_collections()
            collection_exists = any(
                collection.name == self.collection_name 
                for collection in collections.collections
            )
            
            if not collection_exists:
                # Create collection with vector configuration
                self.qdrant_client.create_collection(
                    collection_name=self.collection_name,
                    vectors_config=VectorParams(
                        size=384,  # all-MiniLM-L6-v2 embedding size
                        distance=Distance.COSINE
                    )
                )
                print(f"✅ Created Qdrant collection: {self.collection_name}")
            else:
                print(f"✅ Qdrant collection exists: {self.collection_name}")
                
        except Exception as e:
            print(f"⚠️ Qdrant collection initialization warning: {e}")
    
    def _initialize_financial_knowledge(self):
        """Initialize vector store with comprehensive financial knowledge"""

        if self.fallback_mode:
            print("⚠️ Skipping financial knowledge initialization (fallback mode)")
            return

        # Check if knowledge already exists
        try:
            search_result = self.qdrant_client.search(
                collection_name=self.collection_name,
                query_vector=self.embeddings.embed_query("financial planning"),
                limit=1
            )

            if len(search_result) > 0:
                print("✅ Financial knowledge already initialized")
                return

        except Exception as e:
            print(f"⚠️ Checking existing knowledge: {e}")

        # Comprehensive financial knowledge base
        financial_knowledge = [
            {
                "content": """
                Retirement Planning Fundamentals:
                - The 4% withdrawal rule for sustainable retirement income
                - Target 10-12x annual income saved by retirement age
                - Start early to maximize compound interest benefits
                - Maximize employer 401(k) matching contributions
                - Consider Roth vs Traditional IRA based on tax implications
                - Diversify retirement accounts across different tax treatments
                """,
                "metadata": {"category": "retirement", "topic": "planning_basics", "importance": "high"}
            },
            {
                "content": """
                Investment Portfolio Management:
                - Asset allocation based on age and risk tolerance
                - Diversification across asset classes and geographic regions
                - Regular rebalancing to maintain target allocations
                - Dollar-cost averaging for systematic investing
                - Low-cost index funds for broad market exposure
                - Emergency fund of 3-6 months expenses before investing
                """,
                "metadata": {"category": "investment", "topic": "portfolio_management", "importance": "high"}
            },
            {
                "content": """
                Risk Management and Insurance:
                - Life insurance needs analysis based on dependents and debts
                - Disability insurance to protect income earning ability
                - Health insurance with appropriate deductibles and coverage
                - Property and casualty insurance for asset protection
                - Umbrella insurance for high net worth individuals
                - Self-insurance vs commercial insurance trade-offs
                """,
                "metadata": {"category": "insurance", "topic": "risk_management", "importance": "medium"}
            },
            {
                "content": """
                Tax Planning Strategies:
                - Tax-advantaged accounts (401k, IRA, HSA, 529)
                - Tax-loss harvesting in taxable accounts
                - Roth conversion strategies during low-income years
                - Municipal bonds for high-income taxpayers
                - Charitable giving strategies for tax benefits
                - Estate planning to minimize inheritance taxes
                """,
                "metadata": {"category": "tax", "topic": "tax_optimization", "importance": "high"}
            },
            {
                "content": """
                Debt Management Principles:
                - Prioritize high-interest debt elimination
                - Mortgage vs investment decision analysis
                - Student loan repayment vs investment strategies
                - Credit score optimization techniques
                - Debt consolidation and refinancing options
                - Good debt vs bad debt classification
                """,
                "metadata": {"category": "debt", "topic": "debt_management", "importance": "medium"}
            }
        ]
        
        # Add financial knowledge to vector store
        documents = []
        for knowledge in financial_knowledge:
            doc = Document(
                page_content=knowledge["content"],
                metadata=knowledge["metadata"]
            )
            documents.append(doc)
        
        # Split documents and add to vector store
        split_docs = self.text_splitter.split_documents(documents)
        self.vector_store.add_documents(split_docs)
        
        print(f"✅ Initialized {len(split_docs)} financial knowledge chunks")
    
    def add_financial_concept(self, concept: str, category: str = "general", importance: str = "medium"):
        """Add new financial concept to semantic memory"""
        if self.fallback_mode:
            print(f"⚠️ Cannot add concept (fallback mode): {category}")
            return

        doc = Document(
            page_content=concept,
            metadata={
                "category": category,
                "importance": importance,
                "timestamp": datetime.now().isoformat(),
                "type": "concept"
            }
        )

        split_docs = self.text_splitter.split_documents([doc])
        self.vector_store.add_documents(split_docs)

        print(f"✅ Added financial concept to semantic memory: {category}")

    def search_semantic_knowledge(self, query: str, k: int = 5) -> List[Document]:
        """Search for semantically related financial knowledge"""
        if self.fallback_mode:
            print(f"⚠️ Semantic search not available (fallback mode)")
            return []

        try:
            relevant_docs = self.retriever.get_relevant_documents(query)
            return relevant_docs[:k]
        except Exception as e:
            print(f"⚠️ Semantic search error: {e}")
            return []
    
    def get_financial_context(self, query: str, category_filter: Optional[str] = None) -> str:
        """Get formatted financial context for query with optional category filtering"""
        relevant_docs = self.search_semantic_knowledge(query)
        
        if category_filter:
            relevant_docs = [
                doc for doc in relevant_docs 
                if doc.metadata.get('category') == category_filter
            ]
        
        if not relevant_docs:
            return "No specific financial knowledge found for this query."
        
        context_parts = []
        for doc in relevant_docs:
            category = doc.metadata.get('category', 'general')
            importance = doc.metadata.get('importance', 'medium')
            content = doc.page_content.strip()
            context_parts.append(f"[{category.upper()}|{importance.upper()}] {content}")
        
        return "\n\n".join(context_parts)
    
    def get_collection_stats(self) -> Dict[str, Any]:
        """Get statistics about the semantic memory collection"""
        if self.fallback_mode:
            return {
                "collection_name": self.collection_name,
                "status": "fallback_mode",
                "message": "Qdrant not available"
            }

        try:
            collection_info = self.qdrant_client.get_collection(self.collection_name)
            return {
                "collection_name": self.collection_name,
                "points_count": collection_info.points_count,
                "vectors_count": collection_info.vectors_count,
                "status": collection_info.status
            }
        except Exception as e:
            return {"error": str(e)}

# Global semantic memory instance with error handling
try:
    semantic_memory = SemanticMemory()
except Exception as e:
    print(f"⚠️ Failed to initialize semantic memory: {e}")
    print("💡 Creating fallback semantic memory instance")
    # Create a minimal fallback instance
    class FallbackSemanticMemory:
        def __init__(self):
            self.fallback_mode = True
        def search_semantic_knowledge(self, query, k=5):
            return []
        def add_financial_concept(self, concept, category="general", importance="medium"):
            pass
        def get_financial_context(self, query, category_filter=None):
            return "Semantic memory not available (Qdrant connection failed)"
        def get_collection_stats(self):
            return {"status": "fallback_mode", "message": "Qdrant not available"}

    semantic_memory = FallbackSemanticMemory()
