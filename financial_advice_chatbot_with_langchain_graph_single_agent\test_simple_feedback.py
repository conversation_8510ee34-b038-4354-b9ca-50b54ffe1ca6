#!/usr/bin/env python3
"""
Simple test to verify feedback is stored in short-term memory
"""

def test_simple_feedback():
    """Test that feedback is stored and retrieved from short-term memory"""
    print("🧪 TESTING SIMPLE FEEDBACK STORAGE")
    print("=" * 50)
    
    try:
        from memory.enhanced_memory import memory
        
        user_id = "test_user_123"
        query = "What should I invest in?"
        response = "I recommend index funds."
        feedback = "Too vague, consider my age"
        rating = 2
        
        print(f"1. Clearing history for user: {user_id}")
        memory.clear_conversation_history(user_id)
        
        print(f"2. Storing feedback...")
        memory.store_feedback(user_id, query, response, feedback, rating)
        
        print(f"3. Getting conversation history...")
        history = memory.get_conversation_history(user_id)
        
        print(f"4. History length: {len(history) if history else 0}")
        print(f"5. History content: {history}")
        
        if history and feedback in history:
            print("✅ SUCCESS: Feedback found in conversation history!")
            return True
        else:
            print("❌ FAILED: Feedback not found in conversation history")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_simple_feedback()
    
    if success:
        print("\n🎉 FEEDBACK STORAGE IS WORKING!")
        print("Now restart the app and test with real feedback buttons.")
    else:
        print("\n⚠️ FEEDBACK STORAGE STILL NOT WORKING!")
        print("Check the debug output above.")
