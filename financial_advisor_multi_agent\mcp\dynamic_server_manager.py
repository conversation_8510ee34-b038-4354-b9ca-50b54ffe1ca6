"""
Dynamic MCP Server Manager
Creates and manages MCP servers dynamically based on user input
"""

import asyncio
import logging
import importlib
import tempfile
import os
from typing import Dict, Any, List, Optional
from pathlib import Path

logger = logging.getLogger(__name__)

class DynamicMCPServer:
    """A dynamic MCP server that can be created at runtime"""

    def __init__(self, name: str, tool_name: str, tool_description: str, llm_api_key: str):
        self.name = name
        self.tool_name = tool_name
        self.tool_description = tool_description
        self.llm_api_key = llm_api_key
        self.llm = None
        self.is_active = False

    async def initialize(self):
        """Initialize the MCP server with LLM"""
        try:
            from langchain_groq import ChatGroq

            self.llm = ChatGroq(
                groq_api_key=self.llm_api_key,
                model_name="llama3-70b-8192",
                temperature=0.1,
                max_tokens=1000
            )

            self.is_active = True
            logger.info(f"MCP Server {self.name} initialized successfully")

        except Exception as e:
            logger.error(f"Failed to initialize MCP server {self.name}: {e}")
            raise

    async def execute_tool(self, query: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """Execute the custom tool with the given query"""
        try:
            if not self.is_active or not self.llm:
                return {"error": "Server not initialized"}

            # Build prompt for the custom tool
            system_prompt = f"""You are a specialized tool called '{self.tool_name}'.

Tool Description: {self.tool_description}

Your job is to provide helpful, accurate responses based on your specialized function.
Be specific, actionable, and helpful in your responses.

Context: {context or {}}
"""

            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": query}
            ]

            # Get LLM response
            response = await self.llm.ainvoke(messages)

            return {
                "success": True,
                "tool_name": self.tool_name,
                "response": response.content,
                "server_name": self.name
            }

        except Exception as e:
            logger.error(f"Error executing tool {self.tool_name}: {e}")
            return {"error": str(e)}

    def get_tool_info(self) -> Dict[str, Any]:
        """Get information about this tool"""
        return {
            "name": self.tool_name,
            "description": self.tool_description,
            "server_name": self.name,
            "active": self.is_active
        }

    def get_status(self) -> Dict[str, Any]:
        """Get server status"""
        return {
            "active": self.is_active,
            "tool_name": self.tool_name,
            "tool_description": self.tool_description,
            "llm_available": self.llm is not None
        }

class DynamicMCPServerManager:
    """Manages dynamic creation and execution of MCP servers"""
    
    def __init__(self):
        self.servers = {}
        self.next_port = 8010
        
    async def create_server(self, server_config: Dict[str, Any]) -> Dict[str, Any]:
        """Create a new MCP server dynamically"""
        try:
            server_name = server_config.get("name", f"dynamic_server_{len(self.servers)}")
            llm_api_key = server_config.get("llm_api_key")
            tool_description = server_config.get("tool_description")
            tool_name = server_config.get("tool_name", "custom_tool")

            if not llm_api_key or not tool_description:
                return {"success": False, "error": "Missing LLM API key or tool description"}

            # Create a real MCP server instance
            server_instance = DynamicMCPServer(
                name=server_name,
                tool_name=tool_name,
                tool_description=tool_description,
                llm_api_key=llm_api_key
            )

            # Initialize the server
            await server_instance.initialize()

            # Store server
            self.servers[server_name] = {
                "server": server_instance,
                "config": server_config,
                "port": self.next_port,
                "status": {
                    "active": True,
                    "tools_count": 1,
                    "created_at": asyncio.get_event_loop().time()
                }
            }
            self.next_port += 1

            logger.info(f"Created MCP server: {server_name}")
            return {
                "success": True,
                "server_name": server_name,
                "port": self.next_port - 1,
                "status": "running"
            }
                
        except Exception as e:
            logger.error(f"Failed to create dynamic server: {e}")
            return {"success": False, "error": str(e)}
    
    def _generate_server_code(self, server_name: str, tool_name: str, 
                            tool_description: str, llm_api_key: str) -> str:
        """Generate MCP server code dynamically"""
        return f'''
import asyncio
import logging
from typing import Dict, Any
from mcp.base_server import BaseMCPServer

logger = logging.getLogger(__name__)

class {server_name.title().replace("_", "")}Server(BaseMCPServer):
    """Dynamically generated MCP server for {tool_name}"""
    
    def __init__(self):
        super().__init__("{server_name}", {self.next_port})
        self.llm_api_key = "{llm_api_key}"
        self.tool_description = """{tool_description}"""
    
    async def initialize_tools(self):
        """Initialize the custom tool"""
        self.tools["{tool_name}"] = {{
            "name": "{tool_name}",
            "description": self.tool_description,
            "parameters": {{
                "type": "object",
                "properties": {{
                    "query": {{
                        "type": "string",
                        "description": "User query for the tool"
                    }}
                }},
                "required": ["query"]
            }}
        }}
        
        logger.info(f"Initialized tool: {tool_name}")
    
    async def execute_tool(self, tool_name: str, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Execute the custom tool"""
        try:
            if tool_name == "{tool_name}":
                query = parameters.get("query", "")
                
                # Use LLM to process the query
                result = await self._process_with_llm(query)
                
                return {{
                    "success": True,
                    "result": result,
                    "tool_used": "{tool_name}"
                }}
            else:
                return {{"success": False, "error": "Tool not found"}}
                
        except Exception as e:
            logger.error(f"Tool execution failed: {{e}}")
            return {{"success": False, "error": str(e)}}
    
    async def _process_with_llm(self, query: str) -> str:
        """Process query using the configured LLM"""
        try:
            from langchain_groq import ChatGroq
            
            llm = ChatGroq(
                groq_api_key=self.llm_api_key,
                model_name="llama3-70b-8192",
                temperature=0.1
            )
            
            prompt = f"""
            Tool Description: {{self.tool_description}}
            
            User Query: {{query}}
            
            Please provide a helpful response based on the tool's purpose:
            """
            
            response = await llm.ainvoke(prompt)
            return response.content
            
        except Exception as e:
            logger.error(f"LLM processing failed: {{e}}")
            return f"Tool processed query: {{query}} (LLM unavailable)"

# Create server instance
server_instance = {server_name.title().replace("_", "")}Server()
'''
    
    async def _instantiate_server(self, server_name: str, server_code: str):
        """Create and start the server instance"""
        try:
            # Create temporary module
            temp_dir = tempfile.mkdtemp()
            module_path = os.path.join(temp_dir, f"{server_name}.py")
            
            with open(module_path, 'w') as f:
                f.write(server_code)
            
            # Import and instantiate
            spec = importlib.util.spec_from_file_location(server_name, module_path)
            module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(module)
            
            server = module.server_instance
            await server.start()
            
            return server
            
        except Exception as e:
            logger.error(f"Failed to instantiate server: {e}")
            return None
    
    def get_servers(self) -> Dict[str, Any]:
        """Get all running servers"""
        return {
            name: {
                "status": info["server"].get_status(),
                "config": info["config"]
            }
            for name, info in self.servers.items()
        }
    
    async def stop_server(self, server_name: str) -> bool:
        """Stop and remove a specific server"""
        try:
            if server_name in self.servers:
                # Mark server as inactive
                server_instance = self.servers[server_name]["server"]
                server_instance.is_active = False

                # Remove from servers dict
                del self.servers[server_name]

                logger.info(f"Stopped and removed MCP server: {server_name}")
                return True
            return False
        except Exception as e:
            logger.error(f"Failed to stop server {server_name}: {e}")
            return False

    async def execute_server_tool(self, server_name: str, query: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """Execute a tool from a specific server"""
        try:
            if server_name not in self.servers:
                return {"error": f"Server {server_name} not found"}

            server_instance = self.servers[server_name]["server"]
            return await server_instance.execute_tool(query, context)

        except Exception as e:
            logger.error(f"Failed to execute tool on server {server_name}: {e}")
            return {"error": str(e)}

    def get_available_tools(self) -> List[Dict[str, Any]]:
        """Get list of all available tools from all servers"""
        tools = []
        for server_name, server_info in self.servers.items():
            server_instance = server_info["server"]
            tool_info = server_instance.get_tool_info()
            tools.append(tool_info)
        return tools

    def get_server_status(self) -> Dict[str, Any]:
        """Get status of all servers"""
        status = {}
        for server_name, server_info in self.servers.items():
            server_instance = server_info["server"]
            status[server_name] = {
                "status": "active" if server_instance.is_active else "inactive",
                "tools_count": 1,  # Each server has one tool
                "tool_name": server_instance.tool_name,
                "tool_description": server_instance.tool_description
            }
        return status

    async def test_tool(self, tool_name: str, test_query: str = None) -> Dict[str, Any]:
        """Test an MCP tool with a sample query"""
        try:
            logger.info(f"Testing tool: {tool_name}")

            if tool_name not in self.servers:
                available_tools = list(self.servers.keys())
                return {"success": False, "error": f"Tool '{tool_name}' not found. Available tools: {available_tools}"}

            server_info = self.servers[tool_name]
            server_instance = server_info["server"]

            logger.info(f"Server instance type: {type(server_instance)}")
            logger.info(f"Server active status: {getattr(server_instance, 'is_active', 'Unknown')}")

            if not hasattr(server_instance, 'is_active') or not server_instance.is_active:
                return {"success": False, "error": f"Tool '{tool_name}' is not active or not properly initialized"}

            # Use provided test query or generate one based on tool type
            if not test_query:
                test_query = self._get_default_test_query(getattr(server_instance, 'tool_name', tool_name))

            logger.info(f"Using test query: {test_query}")

            # Execute the tool directly on the server instance
            if hasattr(server_instance, 'execute_tool'):
                result = await server_instance.execute_tool(test_query, {})
            else:
                return {"success": False, "error": f"Server instance does not have execute_tool method. Available methods: {dir(server_instance)}"}

            return {
                "success": True,
                "tool_name": tool_name,
                "test_query": test_query,
                "result": result,
                "tool_description": getattr(server_instance, 'tool_description', 'No description available')
            }

        except Exception as e:
            logger.error(f"Tool testing failed: {e}")
            import traceback
            traceback.print_exc()
            return {"success": False, "error": str(e)}

    def _get_default_test_query(self, tool_name: str) -> str:
        """Generate default test queries based on tool name"""
        tool_lower = tool_name.lower()

        if "mongodb" in tool_lower or "database" in tool_lower:
            return "Find recent financial records"
        elif "redis" in tool_lower or "cache" in tool_lower:
            return "Get cached user preferences"
        elif "vector" in tool_lower or "search" in tool_lower:
            return "Search for investment advice"
        elif "market" in tool_lower or "stock" in tool_lower:
            return "Get current market data"
        elif "news" in tool_lower:
            return "Get latest financial news"
        else:
            return f"Test {tool_name} functionality"

    def get_tool_info(self, tool_name: str) -> Dict[str, Any]:
        """Get detailed information about a specific tool"""
        if tool_name not in self.servers:
            return {"success": False, "error": f"Tool '{tool_name}' not found"}

        server_info = self.servers[tool_name]
        server_instance = server_info["server"]

        return {
            "success": True,
            "name": tool_name,
            "tool_name": server_instance.tool_name,
            "description": server_instance.tool_description,
            "status": "active" if server_instance.is_active else "inactive",
            "created_at": server_info.get("created_at", ""),
            "server_type": type(server_instance).__name__
        }

    async def validate_all_servers(self) -> Dict[str, Any]:
        """Validate all MCP servers and their functionality"""
        validation_results = {}

        for server_name, server_info in self.servers.items():
            try:
                server_instance = server_info["server"]

                # Basic validation
                validation = {
                    "server_exists": True,
                    "is_active": getattr(server_instance, 'is_active', False),
                    "has_execute_tool": hasattr(server_instance, 'execute_tool'),
                    "has_llm": getattr(server_instance, 'llm', None) is not None,
                    "tool_name": getattr(server_instance, 'tool_name', 'Unknown'),
                    "tool_description": getattr(server_instance, 'tool_description', 'No description'),
                    "errors": []
                }

                # Test basic functionality
                if validation["is_active"] and validation["has_execute_tool"]:
                    try:
                        test_result = await server_instance.execute_tool("test query", {})
                        validation["test_execution"] = True
                        validation["test_result"] = test_result.get("success", False) if isinstance(test_result, dict) else False
                    except Exception as e:
                        validation["test_execution"] = False
                        validation["errors"].append(f"Test execution failed: {str(e)}")
                else:
                    validation["test_execution"] = False
                    if not validation["is_active"]:
                        validation["errors"].append("Server is not active")
                    if not validation["has_execute_tool"]:
                        validation["errors"].append("Server missing execute_tool method")

                # Overall health score
                health_score = 0
                if validation["server_exists"]: health_score += 20
                if validation["is_active"]: health_score += 20
                if validation["has_execute_tool"]: health_score += 20
                if validation["has_llm"]: health_score += 20
                if validation.get("test_execution", False): health_score += 20

                validation["health_score"] = health_score
                validation["status"] = "healthy" if health_score >= 80 else "issues" if health_score >= 60 else "critical"

                validation_results[server_name] = validation

            except Exception as e:
                validation_results[server_name] = {
                    "server_exists": False,
                    "error": str(e),
                    "health_score": 0,
                    "status": "critical"
                }

        return validation_results

    def get_server_recommendations(self) -> Dict[str, str]:
        """Get recommendations for different server types"""
        return {
            "custom": "Best for: General-purpose tools, custom business logic, flexible AI-powered responses",
            "mongodb": "Best for: Storing user profiles, transaction history, complex financial data with relationships",
            "redis": "Best for: Caching user sessions, temporary data, real-time notifications, fast lookups",
            "vector_db": "Best for: Semantic search, document retrieval, finding similar financial advice, RAG systems",
            "web_search": "Best for: Real-time market data, news, external information retrieval",
            "api_integration": "Best for: Connecting to external financial APIs, third-party services"
        }

# Global manager instance
dynamic_mcp_manager = DynamicMCPServerManager()