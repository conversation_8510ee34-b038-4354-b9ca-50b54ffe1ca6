"""
Dynamic MCP Server Manager
Creates and manages MCP servers dynamically based on user input
"""

import asyncio
import logging
import importlib
import tempfile
import os
from typing import Dict, Any, List, Optional
from pathlib import Path

logger = logging.getLogger(__name__)

class DynamicMCPServer:
    """A dynamic MCP server that can be created at runtime"""

    def __init__(self, name: str, tool_name: str, tool_description: str, llm_api_key: str):
        self.name = name
        self.tool_name = tool_name
        self.tool_description = tool_description
        self.llm_api_key = llm_api_key
        self.llm = None
        self.is_active = False

    async def initialize(self):
        """Initialize the MCP server with LLM"""
        try:
            from langchain_groq import ChatGroq

            self.llm = ChatGroq(
                groq_api_key=self.llm_api_key,
                model_name="llama3-70b-8192",
                temperature=0.1,
                max_tokens=1000
            )

            self.is_active = True
            logger.info(f"MCP Server {self.name} initialized successfully")

        except Exception as e:
            logger.error(f"Failed to initialize MCP server {self.name}: {e}")
            raise

    async def execute_tool(self, query: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """Execute the custom tool with the given query"""
        try:
            if not self.is_active or not self.llm:
                return {"error": "Server not initialized"}

            # Build prompt for the custom tool
            system_prompt = f"""You are a specialized tool called '{self.tool_name}'.

Tool Description: {self.tool_description}

Your job is to provide helpful, accurate responses based on your specialized function.
Be specific, actionable, and helpful in your responses.

Context: {context or {}}
"""

            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": query}
            ]

            # Get LLM response
            response = await self.llm.ainvoke(messages)

            return {
                "success": True,
                "tool_name": self.tool_name,
                "response": response.content,
                "server_name": self.name
            }

        except Exception as e:
            logger.error(f"Error executing tool {self.tool_name}: {e}")
            return {"error": str(e)}

    def get_tool_info(self) -> Dict[str, Any]:
        """Get information about this tool"""
        return {
            "name": self.tool_name,
            "description": self.tool_description,
            "server_name": self.name,
            "active": self.is_active
        }

    def get_status(self) -> Dict[str, Any]:
        """Get server status"""
        return {
            "active": self.is_active,
            "tool_name": self.tool_name,
            "tool_description": self.tool_description,
            "llm_available": self.llm is not None
        }

class DynamicMCPServerManager:
    """Manages dynamic creation and execution of MCP servers"""
    
    def __init__(self):
        self.servers = {}
        self.next_port = 8010
        
    async def create_server(self, server_config: Dict[str, Any]) -> Dict[str, Any]:
        """Create a new MCP server dynamically"""
        try:
            server_name = server_config.get("name", f"dynamic_server_{len(self.servers)}")
            llm_api_key = server_config.get("llm_api_key")
            tool_description = server_config.get("tool_description")
            tool_name = server_config.get("tool_name", "custom_tool")

            if not llm_api_key or not tool_description:
                return {"success": False, "error": "Missing LLM API key or tool description"}

            # Create a real MCP server instance
            server_instance = DynamicMCPServer(
                name=server_name,
                tool_name=tool_name,
                tool_description=tool_description,
                llm_api_key=llm_api_key
            )

            # Initialize the server
            await server_instance.initialize()

            # Store server
            self.servers[server_name] = {
                "server": server_instance,
                "config": server_config,
                "port": self.next_port,
                "status": {
                    "active": True,
                    "tools_count": 1,
                    "created_at": asyncio.get_event_loop().time()
                }
            }
            self.next_port += 1

            logger.info(f"Created MCP server: {server_name}")
            return {
                "success": True,
                "server_name": server_name,
                "port": self.next_port - 1,
                "status": "running"
            }
                
        except Exception as e:
            logger.error(f"Failed to create dynamic server: {e}")
            return {"success": False, "error": str(e)}
    
    def _generate_server_code(self, server_name: str, tool_name: str, 
                            tool_description: str, llm_api_key: str) -> str:
        """Generate MCP server code dynamically"""
        return f'''
import asyncio
import logging
from typing import Dict, Any
from mcp.base_server import BaseMCPServer

logger = logging.getLogger(__name__)

class {server_name.title().replace("_", "")}Server(BaseMCPServer):
    """Dynamically generated MCP server for {tool_name}"""
    
    def __init__(self):
        super().__init__("{server_name}", {self.next_port})
        self.llm_api_key = "{llm_api_key}"
        self.tool_description = """{tool_description}"""
    
    async def initialize_tools(self):
        """Initialize the custom tool"""
        self.tools["{tool_name}"] = {{
            "name": "{tool_name}",
            "description": self.tool_description,
            "parameters": {{
                "type": "object",
                "properties": {{
                    "query": {{
                        "type": "string",
                        "description": "User query for the tool"
                    }}
                }},
                "required": ["query"]
            }}
        }}
        
        logger.info(f"Initialized tool: {tool_name}")
    
    async def execute_tool(self, tool_name: str, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Execute the custom tool"""
        try:
            if tool_name == "{tool_name}":
                query = parameters.get("query", "")
                
                # Use LLM to process the query
                result = await self._process_with_llm(query)
                
                return {{
                    "success": True,
                    "result": result,
                    "tool_used": "{tool_name}"
                }}
            else:
                return {{"success": False, "error": "Tool not found"}}
                
        except Exception as e:
            logger.error(f"Tool execution failed: {{e}}")
            return {{"success": False, "error": str(e)}}
    
    async def _process_with_llm(self, query: str) -> str:
        """Process query using the configured LLM"""
        try:
            from langchain_groq import ChatGroq
            
            llm = ChatGroq(
                groq_api_key=self.llm_api_key,
                model_name="llama3-70b-8192",
                temperature=0.1
            )
            
            prompt = f"""
            Tool Description: {{self.tool_description}}
            
            User Query: {{query}}
            
            Please provide a helpful response based on the tool's purpose:
            """
            
            response = await llm.ainvoke(prompt)
            return response.content
            
        except Exception as e:
            logger.error(f"LLM processing failed: {{e}}")
            return f"Tool processed query: {{query}} (LLM unavailable)"

# Create server instance
server_instance = {server_name.title().replace("_", "")}Server()
'''
    
    async def _instantiate_server(self, server_name: str, server_code: str):
        """Create and start the server instance"""
        try:
            # Create temporary module
            temp_dir = tempfile.mkdtemp()
            module_path = os.path.join(temp_dir, f"{server_name}.py")
            
            with open(module_path, 'w') as f:
                f.write(server_code)
            
            # Import and instantiate
            spec = importlib.util.spec_from_file_location(server_name, module_path)
            module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(module)
            
            server = module.server_instance
            await server.start()
            
            return server
            
        except Exception as e:
            logger.error(f"Failed to instantiate server: {e}")
            return None
    
    def get_servers(self) -> Dict[str, Any]:
        """Get all running servers"""
        return {
            name: {
                "status": info["server"].get_status(),
                "config": info["config"]
            }
            for name, info in self.servers.items()
        }
    
    async def stop_server(self, server_name: str) -> bool:
        """Stop and remove a specific server"""
        try:
            if server_name in self.servers:
                # Mark server as inactive
                server_instance = self.servers[server_name]["server"]
                server_instance.is_active = False

                # Remove from servers dict
                del self.servers[server_name]

                logger.info(f"Stopped and removed MCP server: {server_name}")
                return True
            return False
        except Exception as e:
            logger.error(f"Failed to stop server {server_name}: {e}")
            return False

    async def execute_server_tool(self, server_name: str, query: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """Execute a tool from a specific server"""
        try:
            if server_name not in self.servers:
                return {"error": f"Server {server_name} not found"}

            server_instance = self.servers[server_name]["server"]
            return await server_instance.execute_tool(query, context)

        except Exception as e:
            logger.error(f"Failed to execute tool on server {server_name}: {e}")
            return {"error": str(e)}

    def get_available_tools(self) -> List[Dict[str, Any]]:
        """Get list of all available tools from all servers"""
        tools = []
        for server_name, server_info in self.servers.items():
            server_instance = server_info["server"]
            tool_info = server_instance.get_tool_info()
            tools.append(tool_info)
        return tools

    def get_server_status(self) -> Dict[str, Any]:
        """Get status of all servers"""
        status = {}
        for server_name, server_info in self.servers.items():
            server_instance = server_info["server"]
            status[server_name] = {
                "status": "active" if server_instance.is_active else "inactive",
                "tools_count": 1,  # Each server has one tool
                "tool_name": server_instance.tool_name,
                "tool_description": server_instance.tool_description
            }
        return status

# Global manager instance
dynamic_mcp_manager = DynamicMCPServerManager()