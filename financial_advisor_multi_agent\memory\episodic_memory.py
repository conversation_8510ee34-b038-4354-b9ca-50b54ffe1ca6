"""
Episodic Memory System for Multi-Agent Architecture
Stores and retrieves specific interaction episodes using MongoDB
"""

import asyncio
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
import uuid

logger = logging.getLogger(__name__)

@dataclass
class Episode:
    """Single interaction episode"""
    episode_id: str
    user_id: str
    query: str
    response: str
    context: Dict[str, Any]
    timestamp: datetime
    confidence: float
    feedback: Optional[Dict[str, Any]] = None
    tags: List[str] = None

class EpisodicMemory:
    """Episodic memory system using MongoDB"""
    
    def __init__(self):
        self.mongo_client = None
        self.database = None
        self.episodes_collection = None
        self.is_initialized = False
        self.in_memory_episodes = []  # Fallback storage
        
    async def initialize(self):
        """Initialize MongoDB connection"""
        try:
            from pymongo import MongoClient
            from config.dynamic_config import get_database_config
            
            config = get_database_config()
            
            # Initialize MongoDB client
            self.mongo_client = MongoClient(config.mongodb_url)
            self.database = self.mongo_client.financial_advisor
            self.episodes_collection = self.database.episodes
            
            # Test connection
            self.mongo_client.admin.command('ping')
            
            # Create indexes
            self.episodes_collection.create_index([("user_id", 1), ("timestamp", -1)])
            self.episodes_collection.create_index([("user_id", 1), ("tags", 1)])
            self.episodes_collection.create_index("episode_id", unique=True)
            
            self.is_initialized = True
            logger.info("✅ Episodic memory initialized")
            
        except Exception as e:
            logger.warning(f"⚠️ MongoDB not available for episodic memory, using in-memory storage: {e}")
            self.mongo_client = None
            self.database = None
            self.episodes_collection = None
            self.is_initialized = True  # Still mark as initialized, just using fallback
    
    async def store_episode(self, user_id: str, query: str, response: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Store a new interaction episode"""
        try:
            if not self.is_initialized:
                return {"success": False, "error": "Not initialized"}
            
            # Generate episode ID
            episode_id = str(uuid.uuid4())
            
            # Extract tags from query and context
            tags = self._extract_tags(query, context)
            
            # Create episode
            episode = {
                "episode_id": episode_id,
                "user_id": user_id,
                "query": query,
                "response": response,
                "context": context,
                "timestamp": datetime.now(),
                "confidence": context.get("confidence", 0.5),
                "tags": tags,
                "feedback": None
            }
            
            # Store in MongoDB
            result = self.episodes_collection.insert_one(episode)
            
            logger.info(f"Stored episode {episode_id} for user {user_id}")
            
            return {
                "success": True,
                "episode_id": episode_id,
                "inserted_id": str(result.inserted_id)
            }
            
        except Exception as e:
            logger.error(f"Failed to store episode: {e}")
            return {"success": False, "error": str(e)}
    
    async def get_relevant_episodes(self, user_id: str, query: str, limit: int = 5) -> List[Dict[str, Any]]:
        """Get relevant episodes for a user query"""
        try:
            if not self.is_initialized:
                return []
            
            # Extract tags from current query
            query_tags = self._extract_tags(query, {})
            
            # Build search criteria
            search_criteria = {"user_id": user_id}
            
            # If we have tags, search for episodes with similar tags
            if query_tags:
                search_criteria["tags"] = {"$in": query_tags}
            
            # Get recent episodes with similar tags
            episodes = list(self.episodes_collection.find(
                search_criteria,
                {"_id": 0}  # Exclude MongoDB _id field
            ).sort("timestamp", -1).limit(limit))
            
            # If no tagged episodes found, get recent episodes
            if not episodes:
                episodes = list(self.episodes_collection.find(
                    {"user_id": user_id},
                    {"_id": 0}
                ).sort("timestamp", -1).limit(limit))
            
            return episodes
            
        except Exception as e:
            logger.error(f"Failed to get relevant episodes: {e}")
            return []
    
    async def store_feedback_episode(self, user_id: str, query: str, response: str, feedback_data: Dict[str, Any]) -> Dict[str, Any]:
        """Store feedback for an episode"""
        try:
            if not self.is_initialized:
                return {"success": False, "error": "Not initialized"}
            
            # Find the most recent episode for this user with matching query
            recent_episode = self.episodes_collection.find_one(
                {
                    "user_id": user_id,
                    "query": query
                },
                sort=[("timestamp", -1)]
            )
            
            if recent_episode:
                # Update existing episode with feedback
                self.episodes_collection.update_one(
                    {"episode_id": recent_episode["episode_id"]},
                    {
                        "$set": {
                            "feedback": feedback_data,
                            "feedback_timestamp": datetime.now()
                        }
                    }
                )
                
                return {
                    "success": True,
                    "episode_id": recent_episode["episode_id"],
                    "feedback_added": True
                }
            else:
                # Create new feedback episode
                episode_id = str(uuid.uuid4())
                
                episode = {
                    "episode_id": episode_id,
                    "user_id": user_id,
                    "query": query,
                    "response": response,
                    "context": {"feedback_only": True},
                    "timestamp": datetime.now(),
                    "confidence": 0.5,
                    "tags": ["feedback"],
                    "feedback": feedback_data,
                    "feedback_timestamp": datetime.now()
                }
                
                result = self.episodes_collection.insert_one(episode)
                
                return {
                    "success": True,
                    "episode_id": episode_id,
                    "new_episode_created": True
                }
                
        except Exception as e:
            logger.error(f"Failed to store feedback episode: {e}")
            return {"success": False, "error": str(e)}
    
    async def get_conversation_stats(self, user_id: str) -> Dict[str, Any]:
        """Get conversation statistics for a user"""
        try:
            if not self.is_initialized:
                return {}
            
            # Total conversations
            total_conversations = self.episodes_collection.count_documents({"user_id": user_id})
            
            # Recent conversations (last 30 days)
            thirty_days_ago = datetime.now() - timedelta(days=30)
            recent_conversations = self.episodes_collection.count_documents({
                "user_id": user_id,
                "timestamp": {"$gte": thirty_days_ago}
            })
            
            # Get most common tags
            pipeline = [
                {"$match": {"user_id": user_id}},
                {"$unwind": "$tags"},
                {"$group": {"_id": "$tags", "count": {"$sum": 1}}},
                {"$sort": {"count": -1}},
                {"$limit": 5}
            ]
            
            tag_stats = list(self.episodes_collection.aggregate(pipeline))
            topics_discussed = [tag["_id"] for tag in tag_stats]
            
            # Get feedback summary
            feedback_pipeline = [
                {"$match": {"user_id": user_id, "feedback": {"$exists": True, "$ne": None}}},
                {"$group": {
                    "_id": None,
                    "total_feedback": {"$sum": 1},
                    "avg_rating": {"$avg": "$feedback.rating"},
                    "positive_feedback": {
                        "$sum": {"$cond": [{"$gte": ["$feedback.rating", 4]}, 1, 0]}
                    },
                    "negative_feedback": {
                        "$sum": {"$cond": [{"$lte": ["$feedback.rating", 2]}, 1, 0]}
                    }
                }}
            ]
            
            feedback_stats = list(self.episodes_collection.aggregate(feedback_pipeline))
            feedback_summary = feedback_stats[0] if feedback_stats else {}
            
            # Get first and last interaction
            first_episode = self.episodes_collection.find_one(
                {"user_id": user_id},
                sort=[("timestamp", 1)]
            )
            
            last_episode = self.episodes_collection.find_one(
                {"user_id": user_id},
                sort=[("timestamp", -1)]
            )
            
            return {
                "total_conversations": total_conversations,
                "recent_conversations": recent_conversations,
                "topics_discussed": topics_discussed,
                "feedback_summary": {
                    "total_feedback": feedback_summary.get("total_feedback", 0),
                    "average_rating": round(feedback_summary.get("avg_rating", 0), 1),
                    "positive_feedback": feedback_summary.get("positive_feedback", 0),
                    "negative_feedback": feedback_summary.get("negative_feedback", 0)
                },
                "first_interaction": first_episode["timestamp"] if first_episode else None,
                "last_interaction": last_episode["timestamp"] if last_episode else None
            }
            
        except Exception as e:
            logger.error(f"Failed to get conversation stats: {e}")
            return {}
    
    def _extract_tags(self, query: str, context: Dict[str, Any]) -> List[str]:
        """Extract tags from query and context"""
        tags = []
        query_lower = query.lower()
        
        # Financial topic tags
        if any(word in query_lower for word in ['invest', 'investment', 'portfolio', 'stock', 'bond']):
            tags.append("investing")
        
        if any(word in query_lower for word in ['retire', 'retirement', '401k', 'ira']):
            tags.append("retirement")
        
        if any(word in query_lower for word in ['budget', 'save', 'saving', 'expense']):
            tags.append("budgeting")
        
        if any(word in query_lower for word in ['loan', 'debt', 'credit', 'mortgage']):
            tags.append("debt")
        
        if any(word in query_lower for word in ['tax', 'taxes', 'deduction', 'irs']):
            tags.append("taxes")
        
        if any(word in query_lower for word in ['insurance', 'coverage', 'policy']):
            tags.append("insurance")
        
        if any(word in query_lower for word in ['risk', 'safe', 'conservative', 'aggressive']):
            tags.append("risk_management")
        
        # Context-based tags
        analysis_type = context.get("analysis_type", "")
        if analysis_type:
            tags.append(f"analysis_{analysis_type}")
        
        confidence = context.get("confidence", 0)
        if confidence > 0.8:
            tags.append("high_confidence")
        elif confidence < 0.4:
            tags.append("low_confidence")
        
        # Default tag if no specific tags found
        if not tags:
            tags.append("general_financial")
        
        return tags
    
    async def get_episodes_by_tag(self, user_id: str, tag: str, limit: int = 10) -> List[Dict[str, Any]]:
        """Get episodes by specific tag"""
        try:
            if not self.is_initialized:
                return []
            
            episodes = list(self.episodes_collection.find(
                {
                    "user_id": user_id,
                    "tags": tag
                },
                {"_id": 0}
            ).sort("timestamp", -1).limit(limit))
            
            return episodes
            
        except Exception as e:
            logger.error(f"Failed to get episodes by tag: {e}")
            return []
    
    async def delete_old_episodes(self, days_old: int = 90) -> Dict[str, Any]:
        """Delete episodes older than specified days"""
        try:
            if not self.is_initialized:
                return {"success": False, "error": "Not initialized"}
            
            cutoff_date = datetime.now() - timedelta(days=days_old)
            
            result = self.episodes_collection.delete_many({
                "timestamp": {"$lt": cutoff_date}
            })
            
            logger.info(f"Deleted {result.deleted_count} old episodes")
            
            return {
                "success": True,
                "deleted_count": result.deleted_count
            }
            
        except Exception as e:
            logger.error(f"Failed to delete old episodes: {e}")
            return {"success": False, "error": str(e)}
    
    def update_config(self, config):
        """Update configuration"""
        try:
            if hasattr(config, 'mongodb_url'):
                # Reinitialize with new config
                asyncio.create_task(self.initialize())
            logger.info("Updated episodic memory configuration")
        except Exception as e:
            logger.error(f"Failed to update config: {e}")
