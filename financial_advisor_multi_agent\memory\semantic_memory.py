"""
Semantic Memory System for Multi-Agent Architecture
Handles vector-based knowledge storage and retrieval using Qdrant
"""

import asyncio
import logging
from typing import Dict, Any, List, Optional
import numpy as np

logger = logging.getLogger(__name__)

class SemanticMemory:
    """Semantic memory system using Qdrant for vector storage"""
    
    def __init__(self):
        self.qdrant_client = None
        self.embeddings_model = None
        self.is_initialized = False
        self.collection_name = "financial_knowledge"
        
    async def initialize(self):
        """Initialize Qdrant connection and embeddings model"""
        try:
            from qdrant_client import QdrantClient
            from qdrant_client.models import Distance, VectorParams, PointStruct
            from sentence_transformers import SentenceTransformer
            from config.dynamic_config import get_database_config
            
            config = get_database_config()
            
            # Initialize Qdrant client
            self.qdrant_client = QdrantClient(url=config.qdrant_url)
            
            # Initialize embeddings model
            self.embeddings_model = SentenceTransformer('all-MiniLM-L6-v2')
            
            # Create collection if it doesn't exist
            try:
                collections = self.qdrant_client.get_collections()
                collection_exists = any(
                    collection.name == self.collection_name 
                    for collection in collections.collections
                )
                
                if not collection_exists:
                    self.qdrant_client.create_collection(
                        collection_name=self.collection_name,
                        vectors_config=VectorParams(
                            size=384,  # all-MiniLM-L6-v2 embedding size
                            distance=Distance.COSINE
                        )
                    )
                    
                    # Add initial financial knowledge
                    await self._populate_initial_knowledge()
                    
            except Exception as e:
                logger.warning(f"Collection setup warning: {e}")
            
            self.is_initialized = True
            logger.info("✅ Semantic memory initialized")
            
        except Exception as e:
            logger.error(f"Failed to initialize semantic memory: {e}")
            raise
    
    async def _populate_initial_knowledge(self):
        """Populate with initial financial knowledge"""
        try:
            initial_knowledge = [
                {
                    "id": "stocks_basics",
                    "content": "Stocks represent ownership shares in companies. When you buy stocks, you become a partial owner of the company and can benefit from its growth through price appreciation and dividends.",
                    "category": "investing",
                    "topic": "stocks"
                },
                {
                    "id": "bonds_basics", 
                    "content": "Bonds are debt securities where you lend money to governments or corporations in exchange for regular interest payments and return of principal at maturity.",
                    "category": "investing",
                    "topic": "bonds"
                },
                {
                    "id": "diversification",
                    "content": "Diversification involves spreading investments across different asset classes, sectors, and geographic regions to reduce risk and improve potential returns.",
                    "category": "investing",
                    "topic": "risk_management"
                },
                {
                    "id": "emergency_fund",
                    "content": "An emergency fund should contain 3-6 months of living expenses in easily accessible accounts like savings or money market accounts for unexpected financial needs.",
                    "category": "planning",
                    "topic": "emergency_planning"
                },
                {
                    "id": "retirement_planning",
                    "content": "Retirement planning involves saving and investing for your future needs, typically through 401(k), IRA, and other retirement accounts with tax advantages.",
                    "category": "planning", 
                    "topic": "retirement"
                },
                {
                    "id": "compound_interest",
                    "content": "Compound interest is earning interest on both your initial investment and previously earned interest, making time and consistent investing powerful wealth-building tools.",
                    "category": "concepts",
                    "topic": "growth"
                }
            ]
            
            points = []
            for i, knowledge in enumerate(initial_knowledge):
                embedding = self.embeddings_model.encode(knowledge["content"])
                
                point = PointStruct(
                    id=i + 1,
                    vector=embedding.tolist(),
                    payload={
                        "content": knowledge["content"],
                        "category": knowledge["category"],
                        "topic": knowledge["topic"],
                        "knowledge_id": knowledge["id"]
                    }
                )
                points.append(point)
            
            self.qdrant_client.upsert(
                collection_name=self.collection_name,
                points=points
            )
            
            logger.info(f"Added {len(points)} initial knowledge entries")
            
        except Exception as e:
            logger.error(f"Failed to populate initial knowledge: {e}")
    
    async def search_knowledge(self, query: str, limit: int = 5) -> Dict[str, Any]:
        """Search for relevant financial knowledge"""
        try:
            if not self.is_initialized:
                return {"relevant_knowledge": "", "sources": []}
            
            # Generate query embedding
            query_embedding = self.embeddings_model.encode(query)
            
            # Search in Qdrant
            search_results = self.qdrant_client.search(
                collection_name=self.collection_name,
                query_vector=query_embedding.tolist(),
                limit=limit,
                score_threshold=0.3  # Minimum similarity threshold
            )
            
            # Process results
            relevant_knowledge = []
            sources = []
            
            for result in search_results:
                payload = result.payload
                content = payload.get("content", "")
                category = payload.get("category", "general")
                topic = payload.get("topic", "financial")
                
                relevant_knowledge.append(content)
                sources.append({
                    "category": category,
                    "topic": topic,
                    "score": result.score,
                    "knowledge_id": payload.get("knowledge_id", "unknown")
                })
            
            # Combine knowledge into coherent text
            knowledge_text = "\n\n".join(relevant_knowledge) if relevant_knowledge else ""
            
            return {
                "relevant_knowledge": knowledge_text,
                "sources": sources,
                "query": query,
                "results_count": len(search_results)
            }
            
        except Exception as e:
            logger.error(f"Failed to search knowledge: {e}")
            return {"relevant_knowledge": "", "sources": []}
    
    async def add_knowledge(self, content: str, category: str, topic: str, knowledge_id: str = None) -> bool:
        """Add new knowledge to the semantic memory"""
        try:
            if not self.is_initialized:
                return False
            
            # Generate embedding
            embedding = self.embeddings_model.encode(content)
            
            # Get next available ID
            collection_info = self.qdrant_client.get_collection(self.collection_name)
            next_id = collection_info.points_count + 1
            
            # Create point
            point = PointStruct(
                id=next_id,
                vector=embedding.tolist(),
                payload={
                    "content": content,
                    "category": category,
                    "topic": topic,
                    "knowledge_id": knowledge_id or f"custom_{next_id}",
                    "added_at": asyncio.get_event_loop().time()
                }
            )
            
            # Add to collection
            self.qdrant_client.upsert(
                collection_name=self.collection_name,
                points=[point]
            )
            
            logger.info(f"Added knowledge: {knowledge_id or f'custom_{next_id}'}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to add knowledge: {e}")
            return False
    
    async def get_knowledge_by_category(self, category: str, limit: int = 10) -> List[Dict[str, Any]]:
        """Get knowledge by category"""
        try:
            if not self.is_initialized:
                return []
            
            # Search by category filter
            search_results = self.qdrant_client.scroll(
                collection_name=self.collection_name,
                scroll_filter={
                    "must": [
                        {
                            "key": "category",
                            "match": {"value": category}
                        }
                    ]
                },
                limit=limit
            )
            
            knowledge_items = []
            for point in search_results[0]:  # scroll returns (points, next_page_offset)
                payload = point.payload
                knowledge_items.append({
                    "content": payload.get("content", ""),
                    "topic": payload.get("topic", ""),
                    "knowledge_id": payload.get("knowledge_id", ""),
                    "id": point.id
                })
            
            return knowledge_items
            
        except Exception as e:
            logger.error(f"Failed to get knowledge by category: {e}")
            return []
    
    async def update_knowledge(self, knowledge_id: str, content: str, category: str, topic: str) -> bool:
        """Update existing knowledge"""
        try:
            if not self.is_initialized:
                return False
            
            # Find existing point by knowledge_id
            search_results = self.qdrant_client.scroll(
                collection_name=self.collection_name,
                scroll_filter={
                    "must": [
                        {
                            "key": "knowledge_id",
                            "match": {"value": knowledge_id}
                        }
                    ]
                },
                limit=1
            )
            
            if not search_results[0]:
                logger.warning(f"Knowledge not found: {knowledge_id}")
                return False
            
            point = search_results[0][0]
            
            # Generate new embedding
            embedding = self.embeddings_model.encode(content)
            
            # Update point
            updated_point = PointStruct(
                id=point.id,
                vector=embedding.tolist(),
                payload={
                    "content": content,
                    "category": category,
                    "topic": topic,
                    "knowledge_id": knowledge_id,
                    "updated_at": asyncio.get_event_loop().time()
                }
            )
            
            self.qdrant_client.upsert(
                collection_name=self.collection_name,
                points=[updated_point]
            )
            
            logger.info(f"Updated knowledge: {knowledge_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to update knowledge: {e}")
            return False
    
    async def get_collection_stats(self) -> Dict[str, Any]:
        """Get collection statistics"""
        try:
            if not self.is_initialized:
                return {}
            
            collection_info = self.qdrant_client.get_collection(self.collection_name)
            
            return {
                "total_points": collection_info.points_count,
                "vector_size": collection_info.config.params.vectors.size,
                "distance_metric": collection_info.config.params.vectors.distance.value,
                "status": collection_info.status.value
            }
            
        except Exception as e:
            logger.error(f"Failed to get collection stats: {e}")
            return {}
    
    def update_config(self, config):
        """Update configuration"""
        try:
            if hasattr(config, 'qdrant_url'):
                # Reinitialize with new config
                asyncio.create_task(self.initialize())
            logger.info("Updated semantic memory configuration")
        except Exception as e:
            logger.error(f"Failed to update config: {e}")
