#!/usr/bin/env python3
"""
Test the exact scenario from the screenshot
"""

def test_exact_feedback_scenario():
    """Test the exact scenario: give feedback, then ask about previous feedback"""
    print("🧪 TESTING EXACT FEEDBACK SCENARIO")
    print("=" * 60)
    
    try:
        from memory.enhanced_memory import memory
        from graphs.enhanced_workflow import EnhancedFinancialWorkflow
        
        # Use a consistent user ID (like in a real session)
        test_user_id = "consistent_user_123"
        
        print(f"1. Using consistent user ID: {test_user_id}")
        
        # Clear any existing conversation
        memory.clear_conversation_history(test_user_id)
        print("2. Cleared conversation history")
        
        # Step 1: Simulate first interaction about buying a house
        first_query = "I want to buy my first house"
        first_response = "I'd be happy to help you with buying your first house! Here's a plan to help you get started. I'll guide you through the process of gathering necessary documents, such as pay stubs, bank statements, credit reports, and a list of debts. This will help us identify your financial strengths and weaknesses and create a plan to get you closer to achieving your goal."
        
        print("3. Storing first interaction...")
        memory.store_interaction_with_feedback(test_user_id, first_query, first_response)
        
        # Step 2: Simulate user giving negative feedback
        feedback_text = "This response was too generic and didn't consider my specific financial situation"
        print("4. Storing negative feedback...")
        memory.store_feedback(test_user_id, first_query, first_response, feedback_text, 2)
        
        # Step 3: Check what's in conversation history
        print("5. Checking conversation history...")
        conversation = memory.get_conversation_history(test_user_id)
        print(f"   Conversation length: {len(conversation) if conversation else 0} characters")
        
        if conversation:
            print(f"   Contains FEEDBACK_ENTRY: {'[FEEDBACK_ENTRY]' in conversation}")
            print(f"   Contains feedback text: {feedback_text in conversation}")
            if "[FEEDBACK_ENTRY]" in conversation:
                # Extract the feedback entry
                start = conversation.find("[FEEDBACK_ENTRY]")
                end = conversation.find("---", start) + 3
                feedback_entry = conversation[start:end]
                print(f"   Feedback entry: {feedback_entry}")
        else:
            print("   ❌ No conversation history found!")
            return False
        
        # Step 4: Simulate second query asking about previous feedback
        second_query = "can you tell me my previous query and feedback?"
        print(f"6. Processing second query: {second_query}")
        
        # Create workflow and test context retrieval
        workflow = EnhancedFinancialWorkflow()
        state = {
            "user_id": test_user_id,
            "user_input": second_query,
            "vector_context": "",
            "comprehensive_context": {}
        }
        
        # This should retrieve the feedback
        print("7. Retrieving context (this should show debug output)...")
        updated_state = workflow.retrieve_context(state)
        vector_context = updated_state.get("vector_context", "")
        
        print(f"8. Vector context length: {len(vector_context)}")
        
        # Check if feedback is in the context
        if "PREVIOUS USER FEEDBACK" in vector_context:
            print("   ✅ Feedback section found in vector context!")
            if feedback_text in vector_context:
                print("   ✅ Specific feedback text found in context!")
                print("   ✅ The bot should be able to remember the feedback!")
                return True
            else:
                print("   ❌ Specific feedback text not found in context")
                print(f"   Vector context: {vector_context}")
                return False
        else:
            print("   ❌ No feedback section found in vector context")
            print(f"   Vector context: {vector_context[:500]}...")
            return False
            
    except Exception as e:
        print(f"❌ Error in exact scenario test: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_workflow_response_generation():
    """Test if the workflow can generate a response that includes feedback"""
    print("\n🧪 TESTING WORKFLOW RESPONSE GENERATION")
    print("=" * 60)
    
    try:
        from graphs.enhanced_workflow import EnhancedFinancialWorkflow
        from memory.enhanced_memory import memory
        
        test_user_id = "response_test_user"
        
        # Set up scenario with feedback
        memory.clear_conversation_history(test_user_id)
        memory.store_feedback(test_user_id, 
                            "What should I invest in?", 
                            "I recommend diversified index funds.", 
                            "Too vague, consider my age and income", 
                            2)
        
        # Process a query asking about feedback
        workflow = EnhancedFinancialWorkflow()
        result = workflow.process_query(test_user_id, "What feedback did I give you?")
        
        response = result.get("response", "")
        print(f"Generated response: {response}")
        
        # Check if the response mentions the feedback
        if "vague" in response.lower() or "age" in response.lower() or "income" in response.lower():
            print("✅ Response includes feedback content!")
            return True
        else:
            print("❌ Response does not include feedback content")
            return False
            
    except Exception as e:
        print(f"❌ Error in response generation test: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🔍 EXACT SCENARIO TESTING")
    print("=" * 70)
    
    test1 = test_exact_feedback_scenario()
    test2 = test_workflow_response_generation()
    
    print("\n" + "=" * 70)
    print("📊 EXACT SCENARIO TEST RESULTS")
    print("=" * 70)
    
    print(f"1. Feedback Storage & Retrieval: {'✅ PASSED' if test1 else '❌ FAILED'}")
    print(f"2. Response Generation: {'✅ PASSED' if test2 else '❌ FAILED'}")
    
    if test1 and test2:
        print("\n🎉 FEEDBACK SYSTEM SHOULD BE WORKING!")
        print("If it's still not working in the UI, the issue might be:")
        print("- User ID changing between sessions")
        print("- Feedback not being stored when buttons are clicked")
        print("- Debug output will show what's happening")
    else:
        print("\n⚠️ FEEDBACK SYSTEM STILL HAS ISSUES!")
        print("Check the debug output above for details.")
        
    print("\n🚀 NEXT STEPS:")
    print("1. Run the app: streamlit run main.py")
    print("2. Check the console for DEBUG messages")
    print("3. Give feedback and see if DEBUG shows it's being stored")
    print("4. Ask about feedback and see if DEBUG shows it's being retrieved")
