"""
Short-Term Memory System for PennyWise
Handles conversation history using <PERSON>is + <PERSON>hain
"""

import os
from typing import Dict, List
from langchain.memory import ConversationSummaryBufferMemory
from langchain_community.chat_message_histories import RedisChatMessageHistory
from langchain.schema import BaseMessage
from langchain_groq import ChatGroq
from dotenv import load_dotenv

load_dotenv()

class ShortTermMemory:
    """
    Short-term memory system using <PERSON>is + <PERSON><PERSON>hain
    Manages conversation history for current and recent sessions
    """
    
    def __init__(self):
        # Initialize LLM for memory summarization
        self.llm = ChatGroq(
            model="llama3-8b-8192",
            api_key=os.getenv("GROQ_API_KEY"),
            temperature=0.1
        )
        
        # Redis configuration
        self.use_redis = True  # Enable Redis for session data
        self.redis_url = os.getenv("REDIS_URL", "redis://localhost:6379")
        
        # In-memory cache of conversation memories
        self.conversation_memories = {}
        
        print("✅ Short-Term Memory System initialized with <PERSON><PERSON> + <PERSON><PERSON><PERSON><PERSON>")
    
    def get_conversation_memory(self, user_id: str) -> ConversationSummaryBufferMemory:
        """Get or create Redis-based conversation memory for user"""
        if user_id not in self.conversation_memories:
            if self.use_redis:
                # Use Redis for short-term session memory
                message_history = RedisChatMessageHistory(
                    session_id=user_id,
                    url=self.redis_url
                )
                memory = ConversationSummaryBufferMemory(
                    llm=self.llm,
                    chat_memory=message_history,
                    max_token_limit=1000,
                    return_messages=True
                )
            else:
                # Fallback to in-memory storage
                memory = ConversationSummaryBufferMemory(
                    llm=self.llm,
                    max_token_limit=1000,
                    return_messages=True
                )
            
            self.conversation_memories[user_id] = memory
        
        return self.conversation_memories[user_id]
    
    def add_conversation(self, user_id: str, user_message: str, assistant_message: str):
        """Add conversation turn to short-term memory"""
        memory = self.get_conversation_memory(user_id)
        memory.chat_memory.add_user_message(user_message)
        memory.chat_memory.add_ai_message(assistant_message)

    def add_feedback(self, user_id: str, query: str, response: str, feedback: str, rating: int):
        """Add feedback directly to short-term memory"""
        memory = self.get_conversation_memory(user_id)

        # Store feedback as a user message that includes all context
        feedback_message = f"[FEEDBACK] For query '{query}' and response '{response[:100]}...', user gave feedback: '{feedback}' (Rating: {rating}/5)"

        memory.chat_memory.add_user_message(feedback_message)
        memory.chat_memory.add_ai_message("Feedback received and noted for future responses.")

        print(f"DEBUG: Added feedback to short-term memory for user {user_id}")
        print(f"DEBUG: Feedback message: {feedback_message}")
    
    def get_conversation_history(self, user_id: str) -> str:
        """Get conversation history as string"""
        memory = self.get_conversation_memory(user_id)

        # DEBUG: Check both buffer and messages
        buffer_content = memory.buffer
        messages = memory.chat_memory.messages

        print(f"DEBUG: Buffer length: {len(buffer_content) if buffer_content else 0}")
        print(f"DEBUG: Messages count: {len(messages)}")

        # If buffer is empty or doesn't contain feedback, construct from messages
        if not buffer_content or "[FEEDBACK" not in buffer_content:
            # Construct history from messages
            history_parts = []
            for msg in messages:
                if hasattr(msg, 'content'):
                    content = msg.content
                    if hasattr(msg, 'type'):
                        msg_type = msg.type
                    else:
                        msg_type = "human" if "Human:" in str(msg) else "ai"

                    history_parts.append(f"{msg_type.upper()}: {content}")

            constructed_history = "\n".join(history_parts)
            print(f"DEBUG: Constructed history length: {len(constructed_history)}")
            print(f"DEBUG: Constructed contains FEEDBACK: {'[FEEDBACK' in constructed_history}")

            return constructed_history if constructed_history else buffer_content

        return buffer_content
    
    def get_conversation_messages(self, user_id: str) -> List[BaseMessage]:
        """Get conversation messages as list"""
        memory = self.get_conversation_memory(user_id)
        return memory.chat_memory.messages
    
    def clear_conversation_history(self, user_id: str):
        """Clear conversation history for user"""
        if user_id in self.conversation_memories:
            self.conversation_memories[user_id].clear()
            # Also remove from cache to force recreation
            del self.conversation_memories[user_id]
    
    def get_memory_stats(self, user_id: str) -> Dict:
        """Get memory statistics for debugging"""
        if user_id not in self.conversation_memories:
            return {"status": "no_memory", "message_count": 0}
        
        memory = self.get_conversation_memory(user_id)
        messages = memory.chat_memory.messages
        
        return {
            "status": "active",
            "message_count": len(messages),
            "buffer_length": len(memory.buffer) if memory.buffer else 0,
            "using_redis": self.use_redis
        }

# Global short-term memory instance
short_term_memory = ShortTermMemory()
