"""
Dynamic LLM Manager for PennyWise
Handles switching between default and custom API keys
"""

import os
import time
import streamlit as st
from langchain_groq import <PERSON>t<PERSON>ro<PERSON>
from dotenv import load_dotenv
import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

load_dotenv()

class LLMManager:
    """Manages LLM instances with dynamic API key switching"""
    
    def __init__(self):
        self.default_api_key = os.getenv("GROQ_API_KEY")
        self.current_llm = None
        self.current_api_key = None
        
        # Initialize with default
        self._initialize_llm()
    
    def _initialize_llm(self):
        """Initialize LLM with appropriate API key"""
        # Determine which API key to use
        if hasattr(st.session_state, 'custom_api_key') and st.session_state.custom_api_key:
            api_key = st.session_state.custom_api_key
            key_source = "custom"
        else:
            api_key = self.default_api_key
            key_source = "default"
        
        # Only recreate LLM if API key changed
        if api_key != self.current_api_key:
            try:
                self.current_llm = ChatGroq(
                    groq_api_key=api_key,
                    model_name="llama3-70b-8192",
                    temperature=0.1
                )
                self.current_api_key = api_key
                print(f"✅ LLM initialized with {key_source} API key")
                
            except Exception as e:
                print(f"❌ Error initializing LLM with {key_source} key: {e}")
                
                # Fallback to default if custom key fails
                if key_source == "custom" and self.default_api_key:
                    try:
                        self.current_llm = ChatGroq(
                            groq_api_key=self.default_api_key,
                            model_name="llama3-70b-8192",
                            temperature=0.1
                        )
                        self.current_api_key = self.default_api_key
                        print("✅ Fallback to default API key successful")
                        
                        # Clear invalid custom key
                        if 'custom_api_key' in st.session_state:
                            del st.session_state.custom_api_key
                            
                    except Exception as fallback_error:
                        print(f"❌ Fallback to default key also failed: {fallback_error}")
                        raise Exception("Both custom and default API keys failed")
                else:
                    raise e
    
    def get_llm(self):
        """Get current LLM instance, reinitializing if needed"""
        # Check if we need to switch API keys
        session_key = getattr(st.session_state, 'custom_api_key', None)
        expected_key = session_key if session_key else self.default_api_key
        
        if expected_key != self.current_api_key:
            self._initialize_llm()
        
        return self.current_llm
    
    def get_current_key_info(self):
        """Get information about current API key"""
        if hasattr(st.session_state, 'custom_api_key') and st.session_state.custom_api_key:
            return {
                "source": "custom",
                "key_preview": f"***{st.session_state.custom_api_key[-4:]}",
                "status": "Using Custom API Key"
            }
        else:
            return {
                "source": "default",
                "key_preview": f"***{self.default_api_key[-4:]}" if self.default_api_key else "Not Set",
                "status": "Using Default API Key"
            }
    
    def validate_api_key(self, api_key):
        """Validate an API key by making a test call"""
        try:
            test_llm = ChatGroq(
                groq_api_key=api_key,
                model_name="llama3-70b-8192",
                temperature=0.1
            )
            
            # Make a simple test call
            response = test_llm.invoke("Hello")
            return True, "API key is valid"
            
        except Exception as e:
            return False, f"API key validation failed: {str(e)}"
    
    def reset_to_default(self):
        """Reset to default API key"""
        if 'custom_api_key' in st.session_state:
            del st.session_state.custom_api_key
        self._initialize_llm()
    
    def set_custom_key(self, api_key):
        """Set custom API key"""
        # Validate first
        is_valid, message = self.validate_api_key(api_key)
        
        if is_valid:
            st.session_state.custom_api_key = api_key
            self._initialize_llm()
            return True, "Custom API key set successfully"
        else:
            return False, message

# Global LLM manager instance
llm_manager = LLMManager()
