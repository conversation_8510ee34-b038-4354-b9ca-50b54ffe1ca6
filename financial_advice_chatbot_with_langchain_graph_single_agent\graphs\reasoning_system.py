"""
Advanced Reasoning System for Financial AI
Combines Plan-and-Execute with ReAct for comprehensive reasoning
"""

from typing import Dict, List, Any, Optional, Tuple
import os
from datetime import datetime
import json

# Lang<PERSON>hain Reasoning Imports
from langchain.chains import <PERSON><PERSON>hain
from langchain.prompts import PromptTemplate
from langchain.schema import BaseOutputParser
from langchain_experimental.plan_and_execute import PlanAndExecute, load_agent_executor, load_chat_planner
from langchain.agents import AgentExecutor, create_react_agent, Tool
from langchain_groq import ChatGroq
from langchain.memory import ConversationBufferMemory
from dotenv import load_dotenv

# Import tools
from tools.financial_tools import get_real_stock_data, get_economic_indicators
from tools.geography_tools import get_real_country_data
from tools.market_tools import get_market_sentiment, get_sector_performance

# Import existing planning system - NO DUPLICATION
from graphs.planning_system import planning_system

load_dotenv()

class ReasoningStep:
    """Represents a single reasoning step"""
    def __init__(self, step_type: str, thought: str, action: str, observation: str, reasoning: str):
        self.step_type = step_type  # "plan", "think", "act", "observe", "reflect"
        self.thought = thought
        self.action = action
        self.observation = observation
        self.reasoning = reasoning
        self.timestamp = datetime.now()

class FinancialReasoningSystem:
    """Advanced reasoning system combining planning and reactive reasoning"""
    
    def __init__(self):
        """Initialize the reasoning system"""
        self.llm = ChatGroq(
            groq_api_key=os.getenv("GROQ_API_KEY"),
            model_name="llama3-70b-8192",
            temperature=0.1  # Lower temperature for more consistent reasoning
        )
        
        # Initialize reasoning components - USE EXISTING PLANNING SYSTEM
        self.planning_system = planning_system  # Use existing planning system
        self.react_agent = None
        self.reasoning_memory = ConversationBufferMemory(
            memory_key="reasoning_history",
            return_messages=True
        )

        # Reasoning tracking
        self.reasoning_steps = []
        self.current_plan = []
        self.execution_trace = []

        self._setup_reasoning_components()
    
    def _setup_reasoning_components(self):
        """Setup reasoning components - USE EXISTING PLANNING SYSTEM"""
        try:
            # Setup tools for reasoning
            tools = self._get_reasoning_tools()

            # 1. PLANNING COMPONENT - USE EXISTING PLANNING SYSTEM (NO DUPLICATION)
            # self.planning_system is already initialized above

            # 2. REACTIVE REASONING COMPONENT (ReAct) - ONLY NEW COMPONENT
            react_prompt = PromptTemplate.from_template("""
You are a financial advisor with advanced reasoning capabilities. Use the following format:

Question: {input}
Thought: I need to think step by step about this financial question.
Action: [action to take]
Action Input: [input to the action]
Observation: [result of the action]
Thought: [reasoning about the observation]
... (repeat Thought/Action/Observation as needed)
Final Answer: [comprehensive financial advice]

Available tools: {tools}
Tool names: {tool_names}

Previous reasoning: {reasoning_history}

Begin!
Question: {input}
Thought:""")

            self.react_agent = create_react_agent(
                llm=self.llm,
                tools=tools,
                prompt=react_prompt
            )

            self.react_executor = AgentExecutor(
                agent=self.react_agent,
                tools=tools,
                memory=self.reasoning_memory,
                verbose=True,
                max_iterations=5,
                handle_parsing_errors=True
            )

            print("✅ Reasoning system initialized successfully (using existing planning system)")

        except Exception as e:
            print(f"❌ Error setting up reasoning system: {e}")
            # Fallback to basic reasoning
            self._setup_fallback_reasoning()
    
    def _get_reasoning_tools(self) -> List[Tool]:
        """Get tools for reasoning"""
        return [
            Tool(
                name="stock_data",
                description="Get real-time stock data and analysis",
                func=get_real_stock_data
            ),
            Tool(
                name="economic_indicators",
                description="Get economic indicators and market data",
                func=get_economic_indicators
            ),
            Tool(
                name="market_sentiment",
                description="Analyze market sentiment and trends",
                func=get_market_sentiment
            ),
            Tool(
                name="sector_performance",
                description="Get sector performance analysis",
                func=get_sector_performance
            ),
            Tool(
                name="reasoning_analyzer",
                description="Analyze and validate reasoning steps",
                func=self._analyze_reasoning
            )
        ]
    
    def _setup_fallback_reasoning(self):
        """Setup fallback reasoning if main system fails"""
        self.fallback_reasoning = True
        print("⚠️ Using fallback reasoning system")
    
    def reason_about_query(self, user_query: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Main reasoning function that combines planning and reactive reasoning
        
        Returns:
        - reasoning_type: "plan_and_execute" | "reactive" | "hybrid"
        - reasoning_steps: List of reasoning steps
        - plan: Generated plan (if applicable)
        - execution_trace: Execution trace
        - final_reasoning: Final reasoning conclusion
        - confidence: Confidence in reasoning (0-1)
        """
        
        self.reasoning_steps = []
        self.current_plan = []
        self.execution_trace = []
        
        try:
            # Step 1: Determine reasoning approach
            reasoning_approach = self._determine_reasoning_approach(user_query)
            
            # Step 2: Apply appropriate reasoning
            if reasoning_approach == "plan_and_execute":
                result = self._apply_planning_reasoning(user_query, context)
            elif reasoning_approach == "reactive":
                result = self._apply_reactive_reasoning(user_query, context)
            else:  # hybrid
                result = self._apply_hybrid_reasoning(user_query, context)
            
            # Step 3: Validate and refine reasoning
            validated_result = self._validate_reasoning(result)
            
            return validated_result
            
        except Exception as e:
            print(f"❌ Error in reasoning: {e}")
            return self._fallback_reasoning_result(user_query)
    
    def _determine_reasoning_approach(self, user_query: str) -> str:
        """Determine which reasoning approach to use"""
        
        # Analyze query complexity and requirements
        query_lower = user_query.lower()
        
        # Plan-and-Execute indicators
        planning_indicators = [
            "plan", "strategy", "step by step", "how to", "roadmap",
            "retirement planning", "financial plan", "investment strategy"
        ]
        
        # Reactive reasoning indicators  
        reactive_indicators = [
            "what is", "current", "now", "today", "latest",
            "stock price", "market data", "real-time"
        ]
        
        # Complex queries that need both
        hybrid_indicators = [
            "should i invest", "best strategy", "analyze and recommend",
            "comprehensive advice", "detailed analysis"
        ]
        
        planning_score = sum(1 for indicator in planning_indicators if indicator in query_lower)
        reactive_score = sum(1 for indicator in reactive_indicators if indicator in query_lower)
        hybrid_score = sum(1 for indicator in hybrid_indicators if indicator in query_lower)
        
        if hybrid_score > 0 or (planning_score > 0 and reactive_score > 0):
            return "hybrid"
        elif planning_score > reactive_score:
            return "plan_and_execute"
        else:
            return "reactive"
    
    def _apply_planning_reasoning(self, user_query: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Apply plan-and-execute reasoning - USE EXISTING PLANNING SYSTEM"""

        # Record reasoning step
        self.reasoning_steps.append(ReasoningStep(
            step_type="plan",
            thought="I need to create a structured plan for this financial question",
            action="generate_plan",
            observation="",
            reasoning="Complex financial questions benefit from systematic planning"
        ))

        try:
            # USE EXISTING PLANNING SYSTEM - NO DUPLICATION
            user_profile = context.get("user_profile", {})

            # Check if comprehensive planning is needed
            if "comprehensive" in user_query.lower() or len(user_query.split()) > 15:
                plan_result = self.planning_system.execute_plan_with_langchain(user_query)
                plan_type = "comprehensive"
            else:
                # Use custom financial planning
                plan_result = self.planning_system.create_comprehensive_plan(
                    user_profile,
                    user_query
                )
                plan_type = "custom"

            return {
                "reasoning_type": "plan_and_execute",
                "reasoning_steps": self.reasoning_steps,
                "plan": plan_result,
                "execution_trace": self.execution_trace,
                "final_reasoning": f"Created {plan_type} financial plan: {str(plan_result)[:200]}...",
                "confidence": 0.85,
                "approach_used": f"Systematic planning using existing planning system ({plan_type})"
            }

        except Exception as e:
            print(f"❌ Planning reasoning error: {e}")
            return self._fallback_reasoning_result(user_query)
    
    def _apply_reactive_reasoning(self, user_query: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Apply reactive (ReAct) reasoning"""
        
        # Record reasoning step
        self.reasoning_steps.append(ReasoningStep(
            step_type="think",
            thought="I need to think and act reactively to answer this question",
            action="reactive_reasoning",
            observation="",
            reasoning="This question requires immediate data and reactive thinking"
        ))
        
        try:
            if self.react_executor:
                result = self.react_executor.run(
                    input=user_query,
                    reasoning_history=self._get_reasoning_history()
                )
                
                return {
                    "reasoning_type": "reactive",
                    "reasoning_steps": self.reasoning_steps,
                    "plan": [],
                    "execution_trace": self.execution_trace,
                    "final_reasoning": result,
                    "confidence": 0.80,
                    "approach_used": "Reactive reasoning with real-time thinking"
                }
            else:
                return self._fallback_reasoning_result(user_query)
                
        except Exception as e:
            print(f"❌ Reactive reasoning error: {e}")
            return self._fallback_reasoning_result(user_query)
    
    def _apply_hybrid_reasoning(self, user_query: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Apply hybrid reasoning (both planning and reactive)"""
        
        # Record reasoning step
        self.reasoning_steps.append(ReasoningStep(
            step_type="reflect",
            thought="This complex question requires both planning and reactive reasoning",
            action="hybrid_approach",
            observation="",
            reasoning="Combining systematic planning with reactive thinking for comprehensive analysis"
        ))
        
        try:
            # First, create a plan
            planning_result = self._apply_planning_reasoning(user_query, context)
            
            # Then, apply reactive reasoning to refine
            reactive_result = self._apply_reactive_reasoning(
                f"Based on this plan: {planning_result.get('final_reasoning', '')}, provide detailed reactive analysis for: {user_query}",
                context
            )
            
            # Combine results
            combined_reasoning = f"""
**SYSTEMATIC PLAN:**
{planning_result.get('final_reasoning', '')}

**REACTIVE ANALYSIS:**
{reactive_result.get('final_reasoning', '')}

**INTEGRATED CONCLUSION:**
Based on both systematic planning and reactive analysis, here's my comprehensive recommendation...
"""
            
            return {
                "reasoning_type": "hybrid",
                "reasoning_steps": self.reasoning_steps,
                "plan": planning_result.get('plan', []),
                "execution_trace": self.execution_trace,
                "final_reasoning": combined_reasoning,
                "confidence": 0.90,
                "approach_used": "Hybrid: Systematic planning + Reactive reasoning"
            }
            
        except Exception as e:
            print(f"❌ Hybrid reasoning error: {e}")
            return self._fallback_reasoning_result(user_query)
    
    def _validate_reasoning(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """Validate and refine reasoning"""
        
        # Add validation step
        self.reasoning_steps.append(ReasoningStep(
            step_type="reflect",
            thought="Let me validate my reasoning",
            action="validate",
            observation="Reasoning appears sound",
            reasoning="Validation ensures quality of financial advice"
        ))
        
        # Add reasoning metadata
        result["reasoning_metadata"] = {
            "total_steps": len(self.reasoning_steps),
            "reasoning_time": datetime.now(),
            "validation_passed": True,
            "reasoning_quality": "high" if result["confidence"] > 0.8 else "medium"
        }
        
        return result
    
    def _analyze_reasoning(self, reasoning_text: str) -> str:
        """Analyze reasoning quality"""
        return f"Reasoning analysis: The provided reasoning appears logical and well-structured."
    
    def _get_reasoning_history(self) -> str:
        """Get formatted reasoning history"""
        if not self.reasoning_steps:
            return "No previous reasoning steps."
        
        history = []
        for step in self.reasoning_steps[-3:]:  # Last 3 steps
            history.append(f"{step.step_type}: {step.thought}")
        
        return "\n".join(history)
    
    def _fallback_reasoning_result(self, user_query: str) -> Dict[str, Any]:
        """Fallback reasoning when main system fails"""
        return {
            "reasoning_type": "fallback",
            "reasoning_steps": [],
            "plan": [],
            "execution_trace": [],
            "final_reasoning": f"I'll provide my best analysis for: {user_query}",
            "confidence": 0.60,
            "approach_used": "Fallback reasoning"
        }

# Global instance
reasoning_system = FinancialReasoningSystem()
