#!/usr/bin/env python3
"""
Test script to verify both fixes are working:
1. Planning and reasoning steps are captured and stored
2. Feedback is stored in memory and considered by LLM
"""

def test_planning_reasoning_capture():
    """Test that planning and reasoning data is properly captured"""
    print("🧪 TESTING PLANNING & REASONING CAPTURE")
    print("=" * 50)
    
    # Check if enhanced workflow returns the required data
    from graphs.enhanced_workflow import enhanced_workflow
    
    try:
        # Test with a simple query
        result = enhanced_workflow.process_query("test_user", "What should I invest in?")
        
        print("✅ Workflow executed successfully")
        print(f"📊 Result keys: {list(result.keys())}")
        
        # Check for planning data
        if "planning_steps" in result:
            print(f"✅ Planning steps found: {len(result['planning_steps'])} steps")
            for i, step in enumerate(result['planning_steps']):
                print(f"   Step {i+1}: {step.get('step', 'Unknown')} - {step.get('status', 'Unknown')}")
        else:
            print("❌ Planning steps not found in result")
        
        # Check for reasoning data
        if "reasoning_result" in result:
            reasoning = result['reasoning_result']
            if reasoning:
                print(f"✅ Reasoning data found: {reasoning.get('reasoning_type', 'Unknown type')}")
                print(f"   Approach: {reasoning.get('approach_used', 'Unknown')}")
                print(f"   Steps: {len(reasoning.get('reasoning_steps', []))}")
            else:
                print("⚠️ Reasoning result is empty")
        else:
            print("❌ Reasoning result not found")
        
        # Check workflow state
        if "workflow_state" in result:
            print("✅ Full workflow state included")
        else:
            print("❌ Workflow state not included")
            
        return True
        
    except Exception as e:
        print(f"❌ Error testing workflow: {e}")
        return False

def test_feedback_storage():
    """Test that feedback storage mechanism is working"""
    print("\n🧪 TESTING FEEDBACK STORAGE")
    print("=" * 50)
    
    try:
        from memory.enhanced_memory import memory
        
        # Test storing feedback
        test_user_id = "test_feedback_user"
        
        # Simulate positive feedback
        feedback_message = "[POSITIVE_FEEDBACK] User rated response as GOOD (👍) for question: 'Test question'. Response was helpful and accurate."
        memory.add_conversation(test_user_id, "SYSTEM_FEEDBACK", feedback_message)
        print("✅ Positive feedback stored")
        
        # Simulate negative feedback
        feedback_message = "[NEGATIVE_FEEDBACK] User rated response as POOR (👎) for question: 'Test question'. Need to improve approach."
        memory.add_conversation(test_user_id, "SYSTEM_FEEDBACK", feedback_message)
        print("✅ Negative feedback stored")
        
        # Retrieve conversation history
        history = memory.get_conversation_history(test_user_id)
        print(f"✅ Retrieved conversation history: {len(history)} messages")
        
        # Check if feedback is in history
        feedback_found = False
        for msg in history:
            content = msg.get('content', '') if isinstance(msg, dict) else str(msg)
            if 'FEEDBACK' in content:
                feedback_found = True
                print(f"✅ Found feedback in history: {content[:50]}...")
                break
        
        if not feedback_found:
            print("❌ Feedback not found in conversation history")
            
        return feedback_found
        
    except Exception as e:
        print(f"❌ Error testing feedback storage: {e}")
        return False

def test_feedback_consideration():
    """Test that LLM considers feedback in responses"""
    print("\n🧪 TESTING FEEDBACK CONSIDERATION")
    print("=" * 50)
    
    try:
        from graphs.enhanced_workflow import enhanced_workflow
        
        # Create a user with feedback history
        test_user_id = "test_feedback_consideration"
        
        # Add some feedback to memory first
        from memory.enhanced_memory import memory
        feedback_message = "[NEGATIVE_FEEDBACK] User rated previous investment advice as POOR (👎). Need more detailed explanations and specific recommendations."
        memory.add_conversation(test_user_id, "SYSTEM_FEEDBACK", feedback_message)
        
        # Now ask a similar question
        result = enhanced_workflow.process_query(test_user_id, "What should I invest in for retirement?")
        
        print("✅ Workflow executed with feedback context")
        
        # Check if feedback context is included
        vector_context = result.get("vector_context", "")
        if "FEEDBACK" in vector_context:
            print("✅ Feedback context found in vector_context")
            print(f"   Context includes: {vector_context.count('FEEDBACK')} feedback references")
        else:
            print("❌ Feedback context not found in vector_context")
        
        # Check response for signs of considering feedback
        response = result.get("response", "")
        if any(word in response.lower() for word in ["detailed", "specific", "thorough", "comprehensive"]):
            print("✅ Response appears to consider feedback (uses detailed language)")
        else:
            print("⚠️ Response may not be considering feedback")
            
        return True
        
    except Exception as e:
        print(f"❌ Error testing feedback consideration: {e}")
        return False

def main():
    """Run all tests"""
    print("🔧 VERIFYING FIXES FOR PLANNING/REASONING STEPS AND FEEDBACK")
    print("=" * 70)
    
    test1_passed = test_planning_reasoning_capture()
    test2_passed = test_feedback_storage()
    test3_passed = test_feedback_consideration()
    
    print("\n" + "=" * 70)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 70)
    
    print(f"1. Planning & Reasoning Capture: {'✅ PASSED' if test1_passed else '❌ FAILED'}")
    print(f"2. Feedback Storage: {'✅ PASSED' if test2_passed else '❌ FAILED'}")
    print(f"3. Feedback Consideration: {'✅ PASSED' if test3_passed else '❌ FAILED'}")
    
    if all([test1_passed, test2_passed, test3_passed]):
        print("\n🎉 ALL TESTS PASSED! Both fixes are working correctly.")
        print("\n📋 WHAT TO TEST IN FRONTEND:")
        print("1. Ask a question and click 'Show Planning' and 'Show Reasoning' buttons")
        print("2. Give feedback using 👍👎 buttons")
        print("3. Ask another question and see if the response style changes based on feedback")
    else:
        print("\n⚠️ Some tests failed. Check the implementation.")
    
    return all([test1_passed, test2_passed, test3_passed])

if __name__ == "__main__":
    main()
