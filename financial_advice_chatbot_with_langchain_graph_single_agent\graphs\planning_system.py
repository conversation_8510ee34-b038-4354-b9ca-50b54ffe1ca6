"""
LangChain Built-in Planning System
Uses LangChain's experimental plan-and-execute functionality
"""

from typing import Dict, List, Any, Optional
import os
from datetime import datetime

# Lang<PERSON>hain Planning Imports
from langchain.chains import <PERSON><PERSON>hain
from langchain.prompts import PromptTemplate
from langchain.schema import BaseOutputParser
from langchain_experimental.plan_and_execute import PlanAndExecute, load_agent_executor, load_chat_planner
from langchain.agents import AgentExecutor, create_react_agent
from langchain_groq import ChatGroq
from dotenv import load_dotenv

# Import tools
from tools.financial_tools import get_real_stock_data, get_economic_indicators
from tools.geography_tools import get_real_country_data
from tools.market_tools import get_market_sentiment, get_sector_performance

load_dotenv()

class FinancialPlanParser(BaseOutputParser):
    """Custom parser for financial plans"""
    
    def parse(self, text: str) -> Dict[str, Any]:
        """Parse the financial plan output"""
        lines = text.strip().split('\n')
        
        plan = {
            "steps": [],
            "timeline": "Not specified",
            "priority": "Medium"
        }
        
        current_step = None
        for line in lines:
            line = line.strip()
            if line.startswith(('1.', '2.', '3.', '4.', '5.', 'Step')):
                if current_step:
                    plan["steps"].append(current_step)
                current_step = {
                    "action": line,
                    "details": [],
                    "tools_needed": []
                }
            elif line and current_step:
                current_step["details"].append(line)
        
        if current_step:
            plan["steps"].append(current_step)
        
        return plan

class LangChainPlanningSystem:
    """Planning system using LangChain's built-in planning capabilities"""
    
    def __init__(self):
        # Initialize LLM
        self.llm = ChatGroq(
            model="llama3-8b-8192",
            api_key=os.getenv("GROQ_API_KEY"),
            temperature=0.3
        )
        
        # Available tools for planning
        self.tools = [
            get_real_stock_data,
            get_economic_indicators,
            get_real_country_data,
            get_market_sentiment,
            get_sector_performance
        ]
        
        # Initialize LangChain planner
        self.planner = load_chat_planner(self.llm)
        
        # Initialize executor
        self.executor = load_agent_executor(
            self.llm,
            self.tools,
            verbose=True
        )
        
        # Create plan-and-execute chain
        self.plan_and_execute = PlanAndExecute(
            planner=self.planner,
            executor=self.executor,
            verbose=True
        )
        
        # Custom financial planning chain
        self.financial_planner = self._create_financial_planning_chain()
    
    def _create_financial_planning_chain(self):
        """Create custom financial planning chain"""
        
        planning_prompt = PromptTemplate(
            input_variables=["user_profile", "query", "available_tools"],
            template="""
            You are PennyWise, an expert financial advisor creating a comprehensive financial plan.
            
            User Profile: {user_profile}
            User Query: {query}
            Available Tools: {available_tools}
            
            Create a detailed, step-by-step financial plan that:
            1. Analyzes the user's current situation
            2. Identifies specific goals and timelines
            3. Recommends concrete actions with priorities
            4. Suggests which tools to use for data gathering
            5. Provides implementation timeline
            
            Format your response as:
            
            FINANCIAL PLAN:
            
            Step 1: [Action]
            - Details about this step
            - Tools needed: [tool names]
            - Timeline: [when to complete]
            
            Step 2: [Action]
            - Details about this step
            - Tools needed: [tool names]
            - Timeline: [when to complete]
            
            [Continue for all necessary steps]
            
            IMPLEMENTATION PRIORITY:
            - High Priority: [steps that must be done first]
            - Medium Priority: [steps that can be done in parallel]
            - Low Priority: [steps for later]
            
            REVIEW SCHEDULE:
            - [When to review and adjust the plan]
            """
        )
        
        return LLMChain(
            llm=self.llm,
            prompt=planning_prompt,
            output_parser=FinancialPlanParser()
        )
    
    def create_comprehensive_plan(self, user_profile: Dict[str, Any], query: str) -> Dict[str, Any]:
        """Create comprehensive financial plan using LangChain planning"""
        
        # Format available tools
        tool_descriptions = []
        for tool in self.tools:
            tool_descriptions.append(f"- {tool.name}: {tool.description}")
        
        available_tools = "\n".join(tool_descriptions)
        
        # Create plan using custom chain
        plan_result = self.financial_planner.run(
            user_profile=str(user_profile),
            query=query,
            available_tools=available_tools
        )
        
        return plan_result
    
    def execute_plan_with_langchain(self, query: str) -> str:
        """Execute plan using LangChain's built-in plan-and-execute"""
        
        # Enhanced query for better planning
        enhanced_query = f"""
        As a financial advisor, help with this request: {query}
        
        Use the available tools to gather real market data and provide comprehensive advice.
        Create a step-by-step approach and execute each step systematically.
        """
        
        try:
            result = self.plan_and_execute.run(enhanced_query)
            return result
        except Exception as e:
            return f"Error in plan execution: {str(e)}"
    
    def create_investment_strategy(self, user_profile: Dict[str, Any]) -> Dict[str, Any]:
        """Create investment strategy based on user profile"""
        
        age = user_profile.get('age', 30)
        income = user_profile.get('income', 50000)
        risk_tolerance = user_profile.get('risk_tolerance', 'moderate')
        goals = user_profile.get('goals', ['retirement'])
        
        strategy = {
            "asset_allocation": self._calculate_asset_allocation(age, risk_tolerance),
            "investment_timeline": self._create_investment_timeline(age, goals),
            "specific_recommendations": self._get_specific_recommendations(income, risk_tolerance),
            "rebalancing_schedule": "Quarterly review, annual rebalancing",
            "risk_management": self._create_risk_management_plan(income)
        }
        
        return strategy
    
    def _calculate_asset_allocation(self, age: int, risk_tolerance: str) -> Dict[str, int]:
        """Calculate asset allocation based on age and risk tolerance"""
        
        # Base allocation using age
        base_stock_percentage = 100 - age
        
        # Adjust for risk tolerance
        if risk_tolerance == 'aggressive':
            stock_percentage = min(90, base_stock_percentage + 20)
        elif risk_tolerance == 'conservative':
            stock_percentage = max(20, base_stock_percentage - 20)
        else:  # moderate
            stock_percentage = base_stock_percentage
        
        bond_percentage = 100 - stock_percentage
        
        return {
            "stocks": stock_percentage,
            "bonds": bond_percentage,
            "international_stocks": stock_percentage // 3,  # 1/3 of stocks in international
            "domestic_stocks": (stock_percentage * 2) // 3  # 2/3 of stocks domestic
        }
    
    def _create_investment_timeline(self, age: int, goals: List[str]) -> Dict[str, str]:
        """Create investment timeline based on age and goals"""
        
        timeline = {}
        
        if 'retirement' in goals:
            years_to_retirement = 65 - age
            timeline['retirement'] = f"{years_to_retirement} years"
        
        if 'home_purchase' in goals:
            timeline['home_purchase'] = "5-7 years (typical first-time buyer timeline)"
        
        timeline['emergency_fund'] = "3-6 months (immediate priority)"
        timeline['investment_start'] = "Immediately after emergency fund"
        
        return timeline
    
    def _get_specific_recommendations(self, income: int, risk_tolerance: str) -> List[str]:
        """Get specific investment recommendations"""
        
        recommendations = []
        
        # Emergency fund
        emergency_amount = (income // 2)  # 6 months expenses
        recommendations.append(f"Build emergency fund: ${emergency_amount:,}")
        
        # Retirement contributions
        retirement_contribution = int(income * 0.15)  # 15% of income
        recommendations.append(f"Annual retirement contribution: ${retirement_contribution:,}")
        
        # Investment vehicles
        if risk_tolerance == 'aggressive':
            recommendations.extend([
                "Consider growth-focused ETFs (VTI, VXUS)",
                "Small allocation to emerging markets (VWO)",
                "Technology sector exposure (VGT)"
            ])
        elif risk_tolerance == 'conservative':
            recommendations.extend([
                "Focus on dividend ETFs (VYM, VXUS)",
                "Bond allocation (BND, VTIAX)",
                "Stable value funds in 401(k)"
            ])
        else:  # moderate
            recommendations.extend([
                "Balanced approach with target-date funds",
                "Mix of growth and value stocks",
                "International diversification (VTIAX)"
            ])
        
        return recommendations
    
    def _create_risk_management_plan(self, income: int) -> Dict[str, str]:
        """Create risk management recommendations"""
        
        return {
            "life_insurance": f"Term life insurance: ${income * 10:,} coverage",
            "disability_insurance": "Consider disability insurance through employer",
            "health_insurance": "Maximize HSA contributions if available",
            "estate_planning": "Create will and beneficiary designations",
            "regular_reviews": "Annual financial plan review and adjustment"
        }

# Global planning system instance
planning_system = LangChainPlanningSystem()
