"""
Financial Agent - Financial Data and Analysis
Handles stock data, economic indicators, and financial analysis
"""

import asyncio
import logging
from typing import Dict, Any, List, Optional
import yfinance as yf
import requests
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

class FinancialAgent:
    """Agent responsible for financial data and analysis"""
    
    def __init__(self):
        self.is_initialized = False
        self.alpha_vantage_key = None
        
    async def initialize(self):
        """Initialize the financial agent"""
        try:
            logger.info("Initializing Financial Agent")
            
            # Get API keys from environment
            import os
            self.alpha_vantage_key = os.getenv('ALPHA_VANTAGE_API_KEY', 'demo')
            
            self.is_initialized = True
            logger.info("✅ Financial Agent initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize Financial Agent: {e}")
            raise
    
    async def get_data(self, user_id: str, query: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Get financial data based on query"""
        try:
            query_lower = query.lower()
            data = {}
            
            # Determine what financial data is needed
            if any(word in query_lower for word in ['stock', 'price', 'ticker', 'share']):
                stock_data = await self._get_stock_data(query)
                if stock_data:
                    data['stock_analysis'] = stock_data
            
            if any(word in query_lower for word in ['economy', 'economic', 'gdp', 'inflation', 'unemployment']):
                economic_data = await self._get_economic_data()
                if economic_data:
                    data['economic_analysis'] = economic_data
            
            if any(word in query_lower for word in ['market', 'index', 'sp500', 'dow', 'nasdaq']):
                market_data = await self._get_market_indices()
                if market_data:
                    data['market_analysis'] = market_data
            
            return data
            
        except Exception as e:
            logger.error(f"Failed to get financial data: {e}")
            return {}
    
    async def _get_stock_data(self, query: str) -> Dict[str, Any]:
        """Get stock data from query"""
        try:
            # Extract potential stock symbols from query
            symbols = self._extract_stock_symbols(query)
            
            if not symbols:
                # Default to major indices if no specific stock mentioned
                symbols = ['SPY', 'QQQ', 'IWM']
            
            stock_data = {}
            
            for symbol in symbols[:3]:  # Limit to 3 stocks
                try:
                    ticker = yf.Ticker(symbol)
                    info = ticker.info
                    hist = ticker.history(period="5d")
                    
                    if not hist.empty:
                        current_price = hist['Close'].iloc[-1]
                        prev_price = hist['Close'].iloc[-2] if len(hist) > 1 else current_price
                        change = current_price - prev_price
                        change_pct = (change / prev_price) * 100 if prev_price != 0 else 0
                        
                        stock_data[symbol] = {
                            'name': info.get('longName', symbol),
                            'current_price': round(current_price, 2),
                            'change': round(change, 2),
                            'change_percent': round(change_pct, 2),
                            'volume': hist['Volume'].iloc[-1] if 'Volume' in hist else 0,
                            'market_cap': info.get('marketCap', 'N/A'),
                            'pe_ratio': info.get('trailingPE', 'N/A'),
                            'sector': info.get('sector', 'N/A')
                        }
                        
                except Exception as e:
                    logger.warning(f"Failed to get data for {symbol}: {e}")
                    continue
            
            return {
                'symbols_analyzed': list(stock_data.keys()),
                'data': stock_data,
                'analysis_time': datetime.now().isoformat(),
                'data_source': 'Yahoo Finance'
            }
            
        except Exception as e:
            logger.error(f"Failed to get stock data: {e}")
            return {}
    
    async def _get_economic_data(self) -> Dict[str, Any]:
        """Get economic indicators"""
        try:
            economic_data = {}
            
            # Try to get data from Alpha Vantage
            if self.alpha_vantage_key and self.alpha_vantage_key != 'demo':
                try:
                    # GDP data
                    gdp_url = f"https://www.alphavantage.co/query?function=REAL_GDP&interval=annual&apikey={self.alpha_vantage_key}"
                    response = requests.get(gdp_url, timeout=10)
                    
                    if response.status_code == 200:
                        gdp_data = response.json()
                        if 'data' in gdp_data and gdp_data['data']:
                            latest_gdp = gdp_data['data'][0]
                            economic_data['gdp'] = {
                                'value': latest_gdp.get('value', 'N/A'),
                                'date': latest_gdp.get('date', 'N/A')
                            }
                    
                    # Inflation data
                    inflation_url = f"https://www.alphavantage.co/query?function=INFLATION&apikey={self.alpha_vantage_key}"
                    response = requests.get(inflation_url, timeout=10)
                    
                    if response.status_code == 200:
                        inflation_data = response.json()
                        if 'data' in inflation_data and inflation_data['data']:
                            latest_inflation = inflation_data['data'][0]
                            economic_data['inflation'] = {
                                'value': latest_inflation.get('value', 'N/A'),
                                'date': latest_inflation.get('date', 'N/A')
                            }
                            
                except Exception as e:
                    logger.warning(f"Alpha Vantage API error: {e}")
            
            # Fallback to general economic indicators
            if not economic_data:
                economic_data = {
                    'note': 'Economic data requires Alpha Vantage API key',
                    'general_indicators': {
                        'market_trend': 'Monitor major indices for market direction',
                        'interest_rates': 'Check Federal Reserve announcements',
                        'inflation_watch': 'Monitor CPI and PPI reports'
                    }
                }
            
            economic_data.update({
                'analysis_time': datetime.now().isoformat(),
                'data_source': 'Alpha Vantage' if self.alpha_vantage_key != 'demo' else 'General Indicators'
            })
            
            return economic_data
            
        except Exception as e:
            logger.error(f"Failed to get economic data: {e}")
            return {}
    
    async def _get_market_indices(self) -> Dict[str, Any]:
        """Get major market indices data"""
        try:
            indices = {
                '^GSPC': 'S&P 500',
                '^DJI': 'Dow Jones',
                '^IXIC': 'NASDAQ',
                '^RUT': 'Russell 2000'
            }
            
            market_data = {}
            
            for symbol, name in indices.items():
                try:
                    ticker = yf.Ticker(symbol)
                    hist = ticker.history(period="5d")
                    
                    if not hist.empty:
                        current_price = hist['Close'].iloc[-1]
                        prev_price = hist['Close'].iloc[-2] if len(hist) > 1 else current_price
                        change = current_price - prev_price
                        change_pct = (change / prev_price) * 100 if prev_price != 0 else 0
                        
                        market_data[symbol] = {
                            'name': name,
                            'current_value': round(current_price, 2),
                            'change': round(change, 2),
                            'change_percent': round(change_pct, 2),
                            'volume': hist['Volume'].iloc[-1] if 'Volume' in hist else 0
                        }
                        
                except Exception as e:
                    logger.warning(f"Failed to get data for {symbol}: {e}")
                    continue
            
            return {
                'indices': market_data,
                'analysis_time': datetime.now().isoformat(),
                'data_source': 'Yahoo Finance'
            }
            
        except Exception as e:
            logger.error(f"Failed to get market indices: {e}")
            return {}
    
    def _extract_stock_symbols(self, query: str) -> List[str]:
        """Extract potential stock symbols from query"""
        symbols = []
        words = query.upper().split()
        
        # Common stock symbols and companies
        symbol_map = {
            'APPLE': 'AAPL',
            'MICROSOFT': 'MSFT',
            'GOOGLE': 'GOOGL',
            'AMAZON': 'AMZN',
            'TESLA': 'TSLA',
            'META': 'META',
            'FACEBOOK': 'META',
            'NVIDIA': 'NVDA',
            'NETFLIX': 'NFLX',
            'DISNEY': 'DIS',
            'COCA-COLA': 'KO',
            'MCDONALD': 'MCD',
            'WALMART': 'WMT',
            'JOHNSON': 'JNJ',
            'VISA': 'V',
            'MASTERCARD': 'MA'
        }
        
        for word in words:
            # Check if word is a potential stock symbol (2-5 uppercase letters)
            if len(word) >= 2 and len(word) <= 5 and word.isalpha():
                symbols.append(word)
            
            # Check if word matches company names
            for company, symbol in symbol_map.items():
                if company in query.upper():
                    symbols.append(symbol)
                    break
        
        return list(set(symbols))  # Remove duplicates
    
    async def get_stock_analysis(self, symbol: str) -> Dict[str, Any]:
        """Get detailed analysis for a specific stock"""
        try:
            ticker = yf.Ticker(symbol)
            info = ticker.info
            hist = ticker.history(period="1mo")
            
            if hist.empty:
                return {"error": f"No data found for symbol {symbol}"}
            
            # Calculate technical indicators
            current_price = hist['Close'].iloc[-1]
            high_52w = hist['High'].max()
            low_52w = hist['Low'].min()
            avg_volume = hist['Volume'].mean()
            
            # Simple moving averages
            sma_20 = hist['Close'].tail(20).mean()
            sma_50 = hist['Close'].tail(50).mean() if len(hist) >= 50 else None
            
            analysis = {
                'symbol': symbol,
                'company_name': info.get('longName', symbol),
                'current_price': round(current_price, 2),
                'market_cap': info.get('marketCap', 'N/A'),
                'pe_ratio': info.get('trailingPE', 'N/A'),
                'dividend_yield': info.get('dividendYield', 'N/A'),
                'beta': info.get('beta', 'N/A'),
                'sector': info.get('sector', 'N/A'),
                'industry': info.get('industry', 'N/A'),
                'technical_analysis': {
                    '52_week_high': round(high_52w, 2),
                    '52_week_low': round(low_52w, 2),
                    'sma_20': round(sma_20, 2),
                    'sma_50': round(sma_50, 2) if sma_50 else None,
                    'average_volume': int(avg_volume)
                },
                'analysis_time': datetime.now().isoformat()
            }
            
            return analysis
            
        except Exception as e:
            logger.error(f"Failed to get stock analysis for {symbol}: {e}")
            return {"error": str(e)}
    
    async def execute_task(self, task_type: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """Execute a financial analysis task"""
        try:
            if task_type == "get_data":
                return await self.get_data(data['user_id'], data['query'], data.get('context', {}))
            elif task_type == "stock_analysis":
                symbol = data.get('symbol', 'SPY')
                return await self.get_stock_analysis(symbol)
            elif task_type == "market_overview":
                return await self._get_market_indices()
            elif task_type == "economic_indicators":
                return await self._get_economic_data()
            else:
                return {'success': False, 'error': f'Unknown task type: {task_type}'}
                
        except Exception as e:
            logger.error(f"Failed to execute financial task {task_type}: {e}")
            return {'success': False, 'error': str(e)}
    
    def update_config(self, config):
        """Update configuration"""
        try:
            # Update API keys if needed
            import os
            self.alpha_vantage_key = os.getenv('ALPHA_VANTAGE_API_KEY', 'demo')
            logger.info("Updated financial agent configuration")
        except Exception as e:
            logger.error(f"Failed to update config: {e}")
