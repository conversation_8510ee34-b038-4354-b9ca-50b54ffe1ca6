"""
Financial Agent - Intelligent Financial Analysis and Advisory
Specialized AI agent for financial data analysis, investment advice, and market insights
"""

import asyncio
import logging
import os
from typing import Dict, Any, List, Optional
import yfinance as yf
import requests
from datetime import datetime, timedelta
from .base_agent import BaseAgent

logger = logging.getLogger(__name__)

class FinancialAgent(BaseAgent):
    """Intelligent agent specialized in financial analysis and investment advisory"""
    
    def __init__(self):
        super().__init__(
            agent_name="Financial Advisor",
            specialization="Financial analysis, investment strategies, market insights, and portfolio management"
        )
        self.alpha_vantage_key = None
        self.financial_knowledge = {}
        
    async def _agent_specific_init(self):
        """Initialize financial-specific capabilities"""
        try:
            # Get API keys
            self.alpha_vantage_key = os.getenv('ALPHA_VANTAGE_API_KEY', 'demo')
            
            # Load financial knowledge base
            await self._load_financial_knowledge()
            
            logger.info("Financial Agent specific initialization completed")
            
        except Exception as e:
            logger.error(f"Financial Agent specific initialization failed: {e}")
            raise
    
    async def _load_financial_knowledge(self):
        """Load financial knowledge base"""
        self.financial_knowledge = {
            "investment_strategies": {
                "conservative": "Focus on bonds, CDs, and stable value funds with 3-5% returns",
                "moderate": "Balanced portfolio with 60% stocks, 40% bonds for 6-8% returns",
                "aggressive": "Growth stocks, emerging markets, 80%+ equity allocation for 8-12% returns"
            },
            "risk_profiles": {
                "low": "Capital preservation, minimal volatility, 3-5% target return",
                "medium": "Balanced growth and income, moderate volatility, 6-8% target return", 
                "high": "Maximum growth potential, high volatility tolerance, 10%+ target return"
            },
            "asset_allocation": {
                "20s": "90% stocks, 10% bonds - long time horizon allows aggressive growth",
                "30s": "80% stocks, 20% bonds - building wealth phase",
                "40s": "70% stocks, 30% bonds - peak earning years",
                "50s": "60% stocks, 40% bonds - pre-retirement planning",
                "60s+": "40% stocks, 60% bonds - capital preservation focus"
            },
            "financial_rules": {
                "emergency_fund": "3-6 months of expenses in high-yield savings",
                "debt_payoff": "Pay off high-interest debt (>6%) before investing",
                "retirement_savings": "Save 10-15% of income for retirement",
                "house_down_payment": "20% down payment to avoid PMI"
            }
        }
    
    def _build_system_prompt(self, context: Dict[str, Any]) -> str:
        """Build specialized financial advisor system prompt"""
        user_profile = context.get('user_profile', {})
        age = user_profile.get('age', 30)
        income = user_profile.get('income', 50000)
        risk_tolerance = user_profile.get('risk_tolerance', 'moderate')
        
        return f"""You are a specialized Financial Advisor AI agent with 20+ years of experience.

Your expertise includes:
- Investment strategy and portfolio management
- Risk assessment and asset allocation
- Retirement and financial planning
- Market analysis and economic trends
- Tax-efficient investing strategies

Current client profile:
- Age: {age}
- Income: ${income:,}
- Risk tolerance: {risk_tolerance}
- Goals: {user_profile.get('goals', [])}

Financial knowledge base available:
- Investment strategies for different risk levels
- Age-appropriate asset allocation models
- Financial planning rules and best practices
- Market data and economic indicators

Provide personalized, actionable financial advice based on the client's specific situation.
Always consider their age, income, risk tolerance, and goals in your recommendations.
Be specific with numbers, percentages, and timeframes when possible.

Respond in JSON format with detailed analysis and specific recommendations."""
    
    async def _fallback_response(self, user_id: str, query: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Fallback response when LLM is not available"""
        user_profile = context.get('user_profile', {})
        age = user_profile.get('age', 30)
        risk_tolerance = user_profile.get('risk_tolerance', 'moderate')
        
        # Provide basic financial advice based on profile
        if 'invest' in query.lower():
            strategy = self.financial_knowledge["investment_strategies"].get(risk_tolerance, 
                "Diversified portfolio with mix of stocks and bonds")
            return {
                "analysis": f"Based on your {risk_tolerance} risk tolerance, I recommend: {strategy}",
                "recommendations": [
                    "Start with low-cost index funds",
                    "Diversify across asset classes",
                    "Consider your time horizon"
                ],
                "confidence": 0.7,
                "reasoning": "Standard advice based on risk profile",
                "data": {"strategy": strategy},
                "next_steps": ["Open investment account", "Set up automatic investing"]
            }
        
        elif 'retirement' in query.lower():
            allocation = self.financial_knowledge["asset_allocation"].get(f"{age//10*10}s", 
                "Balanced allocation appropriate for your age")
            return {
                "analysis": f"For retirement planning at age {age}: {allocation}",
                "recommendations": [
                    "Maximize employer 401(k) match",
                    "Consider Roth IRA for tax diversification",
                    "Increase savings rate by 1% annually"
                ],
                "confidence": 0.8,
                "reasoning": "Age-based retirement planning strategy",
                "data": {"allocation": allocation},
                "next_steps": ["Calculate retirement needs", "Set up automatic contributions"]
            }
        
        else:
            return {
                "analysis": "I can help with investment advice, retirement planning, budgeting, and financial goal setting.",
                "recommendations": [
                    "Build emergency fund first",
                    "Pay off high-interest debt",
                    "Start investing for long-term goals"
                ],
                "confidence": 0.6,
                "reasoning": "General financial planning principles",
                "data": {},
                "next_steps": ["Assess current financial situation", "Set specific financial goals"]
            }
    
    async def get_market_data(self, symbol: str) -> Dict[str, Any]:
        """Get real-time market data for a stock symbol"""
        try:
            ticker = yf.Ticker(symbol.upper())
            info = ticker.info
            hist = ticker.history(period="1mo")
            
            if hist.empty:
                return {"error": f"No data found for symbol {symbol}"}
            
            current_price = hist['Close'].iloc[-1]
            prev_price = hist['Close'].iloc[-2] if len(hist) > 1 else current_price
            change = current_price - prev_price
            change_pct = (change / prev_price) * 100 if prev_price != 0 else 0
            
            return {
                "symbol": symbol.upper(),
                "current_price": round(current_price, 2),
                "change": round(change, 2),
                "change_percent": round(change_pct, 2),
                "volume": hist['Volume'].iloc[-1],
                "market_cap": info.get('marketCap', 'N/A'),
                "pe_ratio": info.get('trailingPE', 'N/A'),
                "company_name": info.get('longName', symbol.upper())
            }
            
        except Exception as e:
            logger.error(f"Error getting market data for {symbol}: {e}")
            return {"error": f"Could not retrieve data for {symbol}"}
    
    async def analyze_portfolio_allocation(self, user_profile: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze and recommend portfolio allocation based on user profile"""
        age = user_profile.get('age', 30)
        risk_tolerance = user_profile.get('risk_tolerance', 'moderate')
        income = user_profile.get('income', 50000)
        goals = user_profile.get('goals', [])
        
        # Age-based allocation
        stock_percentage = max(20, 100 - age)  # Rule of thumb: 100 - age = stock %
        bond_percentage = 100 - stock_percentage
        
        # Adjust based on risk tolerance
        if risk_tolerance == 'aggressive':
            stock_percentage = min(90, stock_percentage + 20)
        elif risk_tolerance == 'conservative':
            stock_percentage = max(20, stock_percentage - 20)
        
        bond_percentage = 100 - stock_percentage
        
        # Specific recommendations
        recommendations = []
        if 'Retirement' in goals:
            recommendations.append("Maximize 401(k) contributions, especially if employer matches")
        if 'House Purchase' in goals:
            recommendations.append("Keep house down payment in high-yield savings, not stocks")
        if income < 50000:
            recommendations.append("Focus on emergency fund before aggressive investing")
        
        return {
            "recommended_allocation": {
                "stocks": stock_percentage,
                "bonds": bond_percentage,
                "cash": 5  # Emergency fund component
            },
            "specific_recommendations": recommendations,
            "reasoning": f"Based on age {age} and {risk_tolerance} risk tolerance",
            "next_steps": [
                "Open low-cost index fund accounts",
                "Set up automatic monthly investments",
                "Review and rebalance quarterly"
            ]
        }
